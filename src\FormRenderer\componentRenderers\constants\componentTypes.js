/**
 * Component Type Definitions and Mappings
 * 
 * Comprehensive type definitions, mappings, and categorizations for all
 * supported form components in the ComponentRenderer system.
 */

/**
 * Data Entry Component Types
 * 
 * Components that allow user input and data entry.
 */
export const DATA_ENTRY_TYPES = {
  INPUT: 'input',
  EMAIL: 'email',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  RADIO: 'radio',
  CHECKBOX: 'checkbox',
  INPUT_NUMBER: 'inputnumber',
  NUMBER: 'number',
  PASSWORD: 'password',
  DATE_PICKER: 'datepicker',
  DATE: 'date',
  RANGE_PICKER: 'rangepicker',
  SWITCH: 'switch',
  RATE: 'rate',
  SLIDER: 'slider',
  UPLOAD: 'upload',
};

/**
 * Advanced Data Entry Component Types
 * 
 * More sophisticated input components with advanced features.
 */
export const ADVANCED_DATA_ENTRY_TYPES = {
  AUTOCOMPLETE: 'autocomplete',
  CASCADER: 'cascader',
  COLOR_PICKER: 'colorpicker',
  MENTIONS: 'mentions',
  TIME_PICKER: 'timepicker',
  TRANSFER: 'transfer',
  TREE_SELECT: 'treeselect',
};

/**
 * Display Component Types
 * 
 * Components that display information without user interaction.
 */
export const DISPLAY_TYPES = {
  AVATAR: 'avatar',
  BADGE: 'badge',
  IMAGE: 'image',
  TAG: 'tag',
  BUTTON: 'button',
  TYPOGRAPHY: 'typography',
  STATISTIC: 'statistic',
};

/**
 * Data Display Component Types
 * 
 * Components that display structured data and information.
 */
export const DATA_DISPLAY_TYPES = {
  TABLE: 'table',
  LIST: 'list',
  CALENDAR: 'calendar',
  CAROUSEL: 'carousel',
  DESCRIPTIONS: 'descriptions',
  EMPTY: 'empty',
  TIMELINE: 'timeline',
  TREE: 'tree',
};

/**
 * Navigation Component Types
 * 
 * Components that help users navigate through the application.
 */
export const NAVIGATION_TYPES = {
  BREADCRUMB: 'breadcrumb',
  MENU: 'menu',
  PAGINATION: 'pagination',
  STEPS: 'steps',
};

/**
 * Layout Component Types
 * 
 * Components that help structure and organize layouts.
 */
export const LAYOUT_TYPES = {
  DIVIDER: 'divider',
  SPACE: 'space',
};

/**
 * Feedback Component Types
 * 
 * Components that provide user feedback and loading states.
 */
export const FEEDBACK_TYPES = {
  ALERT: 'alert',
  PROGRESS: 'progress',
  SKELETON: 'skeleton',
  SPIN: 'spin',
};

/**
 * Container Component Types
 * 
 * Components that can contain other components and provide complex layouts.
 */
export const CONTAINER_TYPES = {
  TAB_CONTAINER: 'TAB_CONTAINER',
  CARD_CONTAINER: 'CARD_CONTAINER',
  FORM_SECTION: 'FORM_SECTION',
  ACCORDION_CONTAINER: 'ACCORDION_CONTAINER',
  STEPS_CONTAINER: 'STEPS_CONTAINER',
  GRID_CONTAINER: 'GRID_CONTAINER',
  FLEX_CONTAINER: 'FLEX_CONTAINER',
};

/**
 * All component types grouped by category
 */
export const COMPONENT_TYPES_BY_CATEGORY = {
  DATA_ENTRY: Object.values(DATA_ENTRY_TYPES),
  ADVANCED_DATA_ENTRY: Object.values(ADVANCED_DATA_ENTRY_TYPES),
  DISPLAY: Object.values(DISPLAY_TYPES),
  DATA_DISPLAY: Object.values(DATA_DISPLAY_TYPES),
  NAVIGATION: Object.values(NAVIGATION_TYPES),
  LAYOUT: Object.values(LAYOUT_TYPES),
  FEEDBACK: Object.values(FEEDBACK_TYPES),
  CONTAINER: Object.values(CONTAINER_TYPES),
};

/**
 * All supported component types (flat array)
 */
export const ALL_COMPONENT_TYPES = Object.values(COMPONENT_TYPES_BY_CATEGORY).flat();

/**
 * Component type to category mapping
 */
export const TYPE_TO_CATEGORY_MAP = {};
Object.entries(COMPONENT_TYPES_BY_CATEGORY).forEach(([category, types]) => {
  types.forEach(type => {
    TYPE_TO_CATEGORY_MAP[type] = category;
  });
});

/**
 * Component types that require form binding
 */
export const FORM_BINDABLE_TYPES = [
  ...COMPONENT_TYPES_BY_CATEGORY.DATA_ENTRY,
  ...COMPONENT_TYPES_BY_CATEGORY.ADVANCED_DATA_ENTRY,
];

/**
 * Component types that support validation
 */
export const VALIDATABLE_TYPES = [...FORM_BINDABLE_TYPES];

/**
 * Component types that can have children
 */
export const CONTAINER_COMPONENT_TYPES = [...COMPONENT_TYPES_BY_CATEGORY.CONTAINER];

/**
 * Component types that are read-only (display only)
 */
export const READONLY_TYPES = [
  ...COMPONENT_TYPES_BY_CATEGORY.DISPLAY,
  ...COMPONENT_TYPES_BY_CATEGORY.DATA_DISPLAY,
  ...COMPONENT_TYPES_BY_CATEGORY.NAVIGATION,
  ...COMPONENT_TYPES_BY_CATEGORY.LAYOUT,
  ...COMPONENT_TYPES_BY_CATEGORY.FEEDBACK,
];

/**
 * Component size options
 */
export const COMPONENT_SIZES = {
  SMALL: 'small',
  MIDDLE: 'middle',
  LARGE: 'large',
};

/**
 * Component validation status options
 */
export const VALIDATION_STATUS = {
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  VALIDATING: 'validating',
};

/**
 * Button types
 */
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  DEFAULT: 'default',
  DASHED: 'dashed',
  TEXT: 'text',
  LINK: 'link',
};

/**
 * Input types
 */
export const INPUT_TYPES = {
  TEXT: 'text',
  EMAIL: 'email',
  PASSWORD: 'password',
  NUMBER: 'number',
  TEL: 'tel',
  URL: 'url',
  SEARCH: 'search',
};

/**
 * Alert types
 */
export const ALERT_TYPES = {
  SUCCESS: 'success',
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
};

/**
 * Progress types
 */
export const PROGRESS_TYPES = {
  LINE: 'line',
  CIRCLE: 'circle',
  DASHBOARD: 'dashboard',
};

/**
 * Gets the category for a component type
 * 
 * @param {string} componentType - Type of component
 * @returns {string} Component category
 */
export const getComponentCategory = (componentType) => {
  return TYPE_TO_CATEGORY_MAP[componentType] || 'UNKNOWN';
};

/**
 * Checks if a component type is valid
 * 
 * @param {string} componentType - Type of component
 * @returns {boolean} True if component type is valid
 */
export const isValidComponentType = (componentType) => {
  return ALL_COMPONENT_TYPES.includes(componentType);
};

/**
 * Checks if a component type requires form binding
 * 
 * @param {string} componentType - Type of component
 * @returns {boolean} True if component requires form binding
 */
export const requiresFormBinding = (componentType) => {
  return FORM_BINDABLE_TYPES.includes(componentType);
};

/**
 * Checks if a component type supports validation
 * 
 * @param {string} componentType - Type of component
 * @returns {boolean} True if component supports validation
 */
export const supportsValidation = (componentType) => {
  return VALIDATABLE_TYPES.includes(componentType);
};

/**
 * Checks if a component type can have children
 * 
 * @param {string} componentType - Type of component
 * @returns {boolean} True if component can have children
 */
export const canHaveChildren = (componentType) => {
  return CONTAINER_COMPONENT_TYPES.includes(componentType);
};

/**
 * Checks if a component type is read-only
 * 
 * @param {string} componentType - Type of component
 * @returns {boolean} True if component is read-only
 */
export const isReadOnly = (componentType) => {
  return READONLY_TYPES.includes(componentType);
};

/**
 * Gets all component types for a specific category
 * 
 * @param {string} category - Component category
 * @returns {Array} Array of component types in the category
 */
export const getTypesByCategory = (category) => {
  return COMPONENT_TYPES_BY_CATEGORY[category] || [];
};

/**
 * Component type aliases for backward compatibility
 */
export const TYPE_ALIASES = {
  'input-number': 'inputnumber',
  'date-picker': 'datepicker',
  'range-picker': 'rangepicker',
  'time-picker': 'timepicker',
  'color-picker': 'colorpicker',
  'tree-select': 'treeselect',
  'auto-complete': 'autocomplete',
};

/**
 * Resolves component type aliases
 * 
 * @param {string} componentType - Type of component (may be an alias)
 * @returns {string} Resolved component type
 */
export const resolveComponentType = (componentType) => {
  return TYPE_ALIASES[componentType] || componentType;
};
