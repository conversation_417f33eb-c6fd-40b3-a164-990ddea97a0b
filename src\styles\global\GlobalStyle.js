import { createGlobalStyle } from "styled-components";
import { colors } from "../theme";

// Global styles with Microsoft Fluent Design principles
export const GlobalStyle = createGlobalStyle`
  html,
  body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: ${colors.backgroundTertiary};
    color: ${colors.textPrimary};
    line-height: 1.4;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    box-sizing: border-box;
  }

  /* Microsoft Fluent Design focus styles */
  *:focus {
    outline: 2px solid ${colors.accent};
    outline-offset: 2px;
    border-radius: 2px;
  }

  /* Smooth scrolling with Fluent motion */
  html {
    scroll-behavior: smooth;
  }

  /* Fluent Design selection styles */
  ::selection {
    background-color: ${colors.accentLight};
    color: ${colors.textPrimary};
  }

  /* Custom scrollbar with Fluent Design */
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  ::-webkit-scrollbar-track {
    background: ${colors.backgroundSecondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${colors.gray60};
    border-radius: 6px;
    border: 2px solid ${colors.backgroundSecondary};
    
    &:hover {
      background: ${colors.gray80};
    }
    
    &:active {
      background: ${colors.gray90};
    }
  }

  ::-webkit-scrollbar-corner {
    background: ${colors.backgroundSecondary};
  }
`;
