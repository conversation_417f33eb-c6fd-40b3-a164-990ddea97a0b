// Main styles barrel export - Single entry point for all styles
// This provides a clean API for importing styles throughout the application

// Theme exports
export * from './theme';

// Global styles
export * from './global';

// Layout styles
export * from './layout';

// Component styles
export * from './components';

// For backward compatibility, you can also import everything as:
// import * as S from './styles';
// Then use as: S.Component, S.DropZone, etc.
