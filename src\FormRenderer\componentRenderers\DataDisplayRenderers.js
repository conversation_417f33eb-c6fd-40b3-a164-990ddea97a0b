/**
 * Data Display Component Renderers
 * 
 * Renders complex data display components like Table, List, Calendar, etc.
 * These components are used for displaying structured data and information.
 */

import React from 'react';
import {
  Table,
  List,
  Calendar,
  Carousel,
  Descriptions,
  Empty,
  Timeline,
  Tree,
} from 'antd';

/**
 * Renders a Table component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Table component
 */
export const renderTable = (component) => {
  return (
    <Table
      columns={component.columns || []}
      dataSource={component.dataSource || []}
      bordered={component.styling?.bordered}
      size={component.styling?.size}
      pagination={component.styling?.pagination}
      scroll={component.styling?.scroll}
    />
  );
};

/**
 * Renders a List component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered List component
 */
export const renderList = (component) => {
  return (
    <List
      header={component.header}
      footer={component.footer}
      bordered={component.styling?.bordered}
      split={component.styling?.split}
      size={component.styling?.size}
      itemLayout={component.styling?.itemLayout}
      dataSource={component.dataSource || []}
      renderItem={(item) => (
        <List.Item>
          <List.Item.Meta
            title={item.title}
            description={item.description}
          />
        </List.Item>
      )}
    />
  );
};

/**
 * Renders a Calendar component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Calendar component
 */
export const renderCalendar = (component) => {
  return (
    <Calendar
      fullscreen={component.styling?.fullscreen}
      mode={component.styling?.mode}
      style={{ border: '1px solid #d9d9d9', borderRadius: '6px' }}
    />
  );
};

/**
 * Renders a Carousel component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Carousel component
 */
export const renderCarousel = (component) => {
  return (
    <Carousel
      autoplay={component.styling?.autoplay}
      dots={component.styling?.dots}
      infinite={component.styling?.infinite}
      speed={component.styling?.speed}
    >
      {(component.items || []).map((item, index) => (
        <div key={item.id || index}>
          <div
            style={{
              height: '160px',
              color: '#fff',
              lineHeight: '160px',
              textAlign: 'center',
              background: item.background || '#364d79',
            }}
          >
            {item.content}
          </div>
        </div>
      ))}
    </Carousel>
  );
};

/**
 * Renders a Descriptions component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Descriptions component
 */
export const renderDescriptions = (component) => {
  return (
    <Descriptions
      title={component.title}
      bordered={component.styling?.bordered}
      column={component.styling?.column}
      size={component.styling?.size}
      layout={component.styling?.layout}
    >
      {(component.items || []).map((item) => (
        <Descriptions.Item key={item.key} label={item.label}>
          {item.children}
        </Descriptions.Item>
      ))}
    </Descriptions>
  );
};

/**
 * Renders an Empty component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Empty component
 */
export const renderEmpty = (component) => {
  return (
    <Empty
      image={component.styling?.image}
      imageStyle={component.styling?.imageStyle}
      description={component.description}
    />
  );
};

/**
 * Renders a Timeline component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Timeline component
 */
export const renderTimeline = (component) => {
  return (
    <Timeline
      mode={component.styling?.mode}
      pending={component.styling?.pending}
      reverse={component.styling?.reverse}
      items={component.items || []}
    />
  );
};

/**
 * Renders a Tree component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Tree component
 */
export const renderTree = (component) => {
  return (
    <Tree
      treeData={component.treeData || []}
      showLine={component.styling?.showLine}
      showIcon={component.styling?.showIcon}
      checkable={component.styling?.checkable}
      selectable={component.styling?.selectable}
      multiple={component.styling?.multiple}
    />
  );
};
