/**
 * FormBuilderApp Animation Constants
 * 
 * This module contains all Framer Motion animation variants, timing configurations,
 * and animation utilities used throughout the FormBuilderApp components.
 * 
 * Features:
 * - Framer Motion variants for smooth component animations
 * - Enterprise-grade animation timing and easing
 * - Consistent animation patterns across the form builder interface
 * - Performance-optimized with proper GPU acceleration
 * - Accessibility-friendly with reduced motion support
 * 
 * Animation Philosophy:
 * - Subtle and professional animations that enhance UX
 * - Consistent timing and easing curves matching AIChatSection
 * - Performance-optimized for complex form structures
 * - Accessibility-friendly with reduced motion support
 */

/**
 * Tab navigation animation variants
 * Provides smooth transitions between builder and preview tabs
 */
export const tabVariants = {
  hidden: {
    opacity: 0,
    y: 10,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1], // Custom cubic-bezier for smooth motion
    },
  },
  exit: {
    opacity: 0,
    y: -10,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1],
    },
  },
};

/**
 * Modal animation variants for JSON modals
 * Provides smooth entrance and exit animations
 */
export const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    y: 20,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
    },
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    y: 20,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1],
    },
  },
};

/**
 * Error boundary animation variants
 * Provides smooth error state transitions
 */
export const errorVariants = {
  hidden: {
    opacity: 0,
    scale: 0.9,
    y: 30,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: [0.4, 0, 0.2, 1],
      delay: 0.1,
    },
  },
};

/**
 * Loading state animation variants
 * Provides smooth loading transitions
 */
export const loadingVariants = {
  initial: {
    opacity: 0,
    scale: 0.95,
  },
  animate: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
    },
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1],
    },
  },
};

/**
 * Form state transition variants
 * Used for AI form generation state changes
 */
export const formStateVariants = {
  idle: {
    opacity: 1,
    scale: 1,
  },
  updating: {
    opacity: 0.7,
    scale: 0.98,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1],
    },
  },
  updated: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
      delay: 0.1,
    },
  },
};

/**
 * Container animation variants for staggered children
 * Creates smooth cascading effects for multiple elements
 */
export const containerVariants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.05,
    },
  },
};

/**
 * Animation duration constants
 * Consistent timing across all FormBuilderApp animations
 */
export const animationDurations = {
  // Quick micro-interactions
  fast: 0.15,
  
  // Standard UI animations
  normal: 0.3,
  
  // Slower, more deliberate animations
  slow: 0.5,
  
  // State transitions
  stateChange: 0.4,
};

/**
 * Easing curves for consistent motion feel
 * Matches AIChatSection animation patterns
 */
export const easingCurves = {
  // Standard easing
  smooth: [0.4, 0, 0.2, 1],
  
  // Gentle bounce for interactive elements
  gentle: [0.25, 0.46, 0.45, 0.94],
  
  // Quick response for immediate feedback
  quick: [0.4, 0, 1, 1],
  
  // Elastic for playful interactions
  elastic: [0.175, 0.885, 0.32, 1.275],
};

/**
 * Spring animation configurations
 * For more natural, physics-based animations
 */
export const springConfigs = {
  // Gentle spring for subtle animations
  gentle: {
    type: "spring",
    stiffness: 120,
    damping: 14,
  },
  
  // Responsive spring for UI interactions
  responsive: {
    type: "spring",
    stiffness: 200,
    damping: 15,
  },
  
  // Stiff spring for quick, snappy animations
  stiff: {
    type: "spring",
    stiffness: 300,
    damping: 20,
  },
};

/**
 * Animation timing constants
 * Replaces the old ANIMATION.DELAYS with Framer Motion timing
 */
export const animationTiming = {
  // State update delays
  stateReset: 0.05,
  stateUpdate: 0.1,
  
  // UI feedback delays
  feedback: 0.15,
  
  // Tab switching delay
  tabSwitch: 0.1,
};

/**
 * Accessibility-aware animation utilities
 * Respects user's reduced motion preferences
 */
export const accessibleAnimations = {
  /**
   * Get animation variants based on user preferences
   * @param {Object} normalVariants - Standard animation variants
   * @param {Object} reducedVariants - Reduced motion variants
   * @returns {Object} Appropriate variants for user's preferences
   */
  getVariants: (normalVariants, reducedVariants = { hidden: {}, visible: {} }) => {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    return prefersReducedMotion ? reducedVariants : normalVariants;
  },
  
  /**
   * Get duration based on user preferences
   * @param {number} normalDuration - Standard duration
   * @returns {number} Appropriate duration for user's preferences
   */
  getDuration: (normalDuration) => {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    return prefersReducedMotion ? 0.01 : normalDuration;
  },
};
