/**
 * Category Configuration Module
 *
 * This module contains all category-related configuration for the CategorizedSidebar.
 * It defines category metadata, icons, descriptions, and the logic for categorizing
 * components based on their type and functionality.
 *
 * Features:
 * - Category metadata with icons, descriptions, and colors
 * - Component categorization logic based on Ant Design component types
 * - Extensible configuration for adding new categories
 * - Type-safe category definitions
 * - Comprehensive component type mappings
 *
 * Categories:
 * - Containers: Layout containers and wrappers
 * - Data Entry: Input fields and form controls
 * - Data Display: Display and visualization components
 * - Feedback: User feedback and status indicators
 * - Navigation: Navigation and flow components
 * - Layout: Structure and spacing elements
 * - General: General purpose components
 */

import React from 'react';
import {
  FormOutlined,
  EyeOutlined,
  MessageOutlined,
  MenuOutlined,
  LayoutOutlined,
  ToolOutlined,
  ContainerOutlined,
} from '@ant-design/icons';
import { colors } from '../../../styles/theme';

/**
 * Category configuration object containing metadata for each category
 *
 * Each category includes:
 * - icon: React icon component for visual identification
 * - description: Human-readable description of the category purpose
 * - color: Theme color associated with the category
 *
 * This configuration drives the visual representation and organization
 * of components in the sidebar interface.
 */
export const categoryConfig = {
  /**
   * Data Entry Components Category
   *
   * Contains all input-related components that allow users to enter
   * data into the form. This includes text inputs, selectors, date
   * pickers, and other interactive form controls.
   */
  'Data Entry': {
    icon: <FormOutlined className='category-icon' />,
    description: 'Input fields and form controls',
    color: colors.primary,
  },

  /**
   * Data Display Components Category
   *
   * Contains components that display information to users without
   * allowing direct input. This includes tables, lists, images,
   * and other visualization components.
   */
  'Data Display': {
    icon: <EyeOutlined className='category-icon' />,
    description: 'Display and visualization components',
    color: colors.info,
  },

  /**
   * Layout Components Category
   *
   * Contains components that affect the layout and spacing of the
   * form without containing other components. This includes dividers,
   * spacers, and other structural elements.
   */
  Layout: {
    icon: <LayoutOutlined className='category-icon' />,
    description: 'Structure and spacing elements',
    color: colors.textSecondary,
  },

  /**
   * Feedback Components Category
   *
   * Contains components that provide feedback to users about system
   * status, loading states, errors, and other important information
   * that affects user experience.
   */
  Feedback: {
    icon: <MessageOutlined className='category-icon' />,
    description: 'User feedback and status indicators',
    color: colors.warning,
  },

  /**
   * Navigation Components Category
   *
   * Contains components that help users navigate through the form
   * or application. This includes breadcrumbs, menus, pagination,
   * and step indicators.
   */
  Navigation: {
    icon: <MenuOutlined className='category-icon' />,
    description: 'Navigation and flow components',
    color: colors.success,
  },

  /**
   * General Components Category
   *
   * Contains general-purpose components that don't fit into other
   * specific categories. This serves as a fallback category for
   * miscellaneous components.
   */
  General: {
    icon: <ToolOutlined className='category-icon' />,
    description: 'General purpose components',
    color: colors.textTertiary,
  },

  /**
   * Container Components Category
   *
   * Contains layout containers and wrapper components that can hold
   * other components. These are typically used for organizing form
   * structure and creating complex layouts.
   */
  Containers: {
    icon: <ContainerOutlined className='category-icon' />,
    description: 'Layout containers and wrappers',
    color: colors.aiPrimary,
  },
};

/**
 * Component type mappings for each category
 *
 * These arrays define which component types belong to each category.
 * The categorizeComponents function uses these mappings to automatically
 * sort components into the appropriate categories.
 */

/**
 * Data Entry component types
 *
 * All components that allow user input and data entry.
 * These are the most commonly used components in forms.
 */
export const DATA_ENTRY_TYPES = [
  'input', // Text input field
  'email', // Email input field
  'textarea', // Multi-line text input
  'select', // Dropdown selection
  'radio', // Radio button group
  'checkbox', // Checkbox input
  'number', // Number input field
  'date', // Date picker
  'switch', // Toggle switch
  'autocomplete', // Auto-complete input
  'cascader', // Cascading selection
  'colorpicker', // Color picker
  'mentions', // Mentions input
  'timepicker', // Time picker
  'transfer', // Transfer list
  'treeselect', // Tree selection
  'rate', // Rating component
  'slider', // Slider input
];

/**
 * Data Display component types
 *
 * Components that display information without allowing direct input.
 * These are used for showing data, images, and other content.
 */
export const DATA_DISPLAY_TYPES = [
  'avatar', // User avatar display
  'badge', // Badge/notification indicator
  'calendar', // Calendar display
  'carousel', // Image/content carousel
  'descriptions', // Description list
  'empty', // Empty state display
  'image', // Image display
  'list', // List display
  'tag', // Tag display
  'timeline', // Timeline display
  'tree', // Tree structure display
  'table', // Data table
];

/**
 * Feedback component types
 *
 * Components that provide user feedback and status information.
 * These help communicate system state and user actions.
 */
export const FEEDBACK_TYPES = [
  'alert', // Alert message
  'progress', // Progress indicator
  'skeleton', // Loading skeleton
  'spin', // Loading spinner
];

/**
 * Navigation component types
 *
 * Components that help users navigate through the interface.
 * These provide wayfinding and flow control.
 */
export const NAVIGATION_TYPES = [
  'breadcrumb', // Breadcrumb navigation
  'menu', // Menu navigation
  'pagination', // Page navigation
  'steps', // Step indicator
];

/**
 * Layout component types
 *
 * Components that affect layout and spacing without containing content.
 * These provide structure and visual organization.
 */
export const LAYOUT_TYPES = [
  'divider', // Visual divider
  'space', // Spacing component
  'anchor', // Anchor navigation
  'backtop', // Back to top button
];

/**
 * General component types
 *
 * General-purpose components that don't fit into specific categories.
 * These are commonly used utility components.
 */
export const GENERAL_TYPES = [
  'button', // Button component
  'typography', // Typography component
  'upload', // File upload component
];

/**
 * Container component types
 *
 * Components that can contain and organize other components.
 * These are essential for creating complex form layouts.
 */
export const CONTAINER_TYPES = [
  'tabContainer', // Tab container
  'cardContainer', // Card container
  'formSection', // Form section container
  'accordionContainer', // Accordion container
  'stepsContainer', // Steps container
];

/**
 * Default category structure
 *
 * Defines the initial structure of categories with empty arrays.
 * This is used as a template for organizing components.
 */
export const DEFAULT_CATEGORIES = {
  Containers: [],
  General: [],
  'Data Entry': [],
  'Data Display': [],
  Feedback: [],
  Navigation: [],
  Layout: [],
};
