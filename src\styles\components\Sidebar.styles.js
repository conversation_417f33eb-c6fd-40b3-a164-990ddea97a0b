import styled from "styled-components";
import { colors, elevation, motionCurves } from "../theme";

// Sidebar container with Microsoft Fluent Design - Updated for three-section layout
export const SideBar = styled.div`
  width: 100%;
  height: 100%;
  padding: 20px 16px;
  background: ${colors.background};
  overflow-y: auto;
  position: relative;
  z-index: 10;

  /* Microsoft Fluent Design scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: ${colors.backgroundSecondary};
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${colors.gray60};
    border-radius: 4px;
    border: 1px solid ${colors.backgroundSecondary};

    &:hover {
      background: ${colors.gray80};
    }

    &:active {
      background: ${colors.gray90};
    }
  }

  h3 {
    font-size: 12px;
    font-weight: 600;
    color: ${colors.textSecondary};
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 20px 0 12px 0;
    padding: 0 8px;
    font-family: "Segoe UI", sans-serif;

    &:first-child {
      margin-top: 0;
    }
  }

  /* Fluent Design acrylic effect */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    pointer-events: none;
    border-radius: 8px 0 0 8px;
  }
`;

// Sidebar items with Microsoft Fluent Design
export const SideBarItem = styled.div`
  padding: 16px 20px;
  margin: 4px 0;
  background: ${colors.background};
  border: 1px solid ${colors.borderLight};
  border-radius: 6px;
  cursor: move;
  transition: all 0.15s ${motionCurves.decelerate};
  position: relative;
  min-height: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: ${elevation.depth2};
  user-select: none;

  /* Microsoft Fluent Design hover effect */
  &:hover {
    border-color: ${colors.borderHover};
    box-shadow: ${elevation.depth4};
    transform: translateY(-1px) scale(1.01);
    background: ${colors.backgroundSecondary};

    /* Fluent Design reveal effect */
    &::before {
      opacity: 1;
      transform: scale(1);
    }
  }

  &:active {
    transform: translateY(0) scale(1);
    box-shadow: ${elevation.depth2};
    transition: all 0.1s ${motionCurves.accelerate};
  }

  /* Fluent Design reveal highlight */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(0, 120, 212, 0.1) 0%,
      rgba(0, 120, 212, 0.05) 40%,
      transparent 70%
    );
    border-radius: 6px;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ${motionCurves.decelerate};
    pointer-events: none;
  }

  .component-type {
    font-size: 11px;
    color: ${colors.textTertiary};
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: 4px;
    font-weight: 600;
    font-family: "Segoe UI", sans-serif;
  }

  .component-label {
    font-size: 14px;
    color: ${colors.textPrimary};
    font-weight: 400;
    line-height: 1.4;
    font-family: "Segoe UI", sans-serif;
  }

  /* Fluent Design focus ring */
  &:focus {
    outline: 2px solid ${colors.accent};
    outline-offset: 2px;
  }

  /* Dragging state */
  &.dragging {
    opacity: 0.8;
    transform: rotate(2deg) scale(1.05);
    box-shadow: ${elevation.depth16};
    z-index: 1000;
  }
`;
