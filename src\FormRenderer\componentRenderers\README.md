# Component Renderers

This directory contains individual component renderer functions organized by category.

## Structure

### Data Entry Components
- **DataEntryRenderers.js** - Input, textarea, select, radio, checkbox, etc.
- **AdvancedDataEntryRenderers.js** - AutoComplete, cascader, mentions, etc.

### Display Components  
- **DisplayRenderers.js** - Avatar, badge, image, tag, etc.
- **DataDisplayRenderers.js** - Table, list, descriptions, calendar, etc.

### Navigation Components
- **NavigationRenderers.js** - Breadcrumb, menu, pagination, steps

### Layout Components
- **LayoutRenderers.js** - Divider, space, typography
- **ContainerRenderers.js** - Tab, card, form section, accordion, steps containers

### Feedback Components
- **FeedbackRenderers.js** - Alert, progress, skeleton, spin

### Utility Components
- **UtilityRenderers.js** - Button and other utility components

## Usage

Each file exports renderer functions that handle specific component types, making the code more maintainable and easier to test.
