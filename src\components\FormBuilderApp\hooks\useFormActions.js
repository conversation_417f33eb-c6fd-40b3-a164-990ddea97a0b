/**
 * @fileoverview Custom hook for form actions and JSON operations
 *
 * This hook encapsulates all form-related actions including JSON import/export,
 * schema validation, form submission, and test schema loading.
 *
 * @module useFormActions
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useCallback } from 'react';
import { message } from 'antd';
import { validateFormSchema, sanitizeSchemaForStorage } from '../../../utils';
import { enhancedContainerTestSchema } from '../../../data/enhancedContainerTestSchema';

/**
 * Custom hook for form actions and JSON operations
 *
 * Provides comprehensive form action handlers including JSON import/export,
 * schema validation, form submission, and test data loading capabilities.
 *
 * @param {Object} params - Hook parameters
 * @param {Object} params.currentFormSchema - Current form schema object
 * @param {Function} params.setLayout - Layout state setter
 * @param {Function} params.setComponents - Components state setter
 * @param {Function} params.setForceRenderKey - Force render key setter
 * @param {Function} params.setJsonModalVisible - JSON modal visibility setter
 * @param {Function} params.setImportModalVisible - Import modal visibility setter
 * @param {Function} params.setJsonInput - JSON input setter
 * @param {string} params.jsonInput - Current JSON input value
 *
 * @returns {Object} Form action handlers
 * @returns {Function} returns.handleFormSubmit - Form submission handler
 * @returns {Function} returns.handleExportJSON - JSON export handler
 * @returns {Function} returns.handleViewJSON - JSON view handler
 * @returns {Function} returns.handleImportJSON - JSON import handler
 * @returns {Function} returns.handleImportSubmit - JSON import submission handler
 * @returns {Function} returns.handleLoadEnhancedContainerTest - Test schema loader
 *
 * @example
 * ```jsx
 * const {
 *   handleFormSubmit,
 *   handleExportJSON,
 *   handleViewJSON,
 *   handleImportJSON,
 *   handleImportSubmit,
 *   handleLoadEnhancedContainerTest
 * } = useFormActions({
 *   currentFormSchema,
 *   setLayout,
 *   setComponents,
 *   setForceRenderKey,
 *   setJsonModalVisible,
 *   setImportModalVisible,
 *   setJsonInput,
 *   jsonInput
 * });
 * ```
 */
export const useFormActions = ({
  currentFormSchema,
  setLayout,
  setComponents,
  setForceRenderKey,
  setJsonModalVisible,
  setImportModalVisible,
  setJsonInput,
  jsonInput,
}) => {
  /**
   * Handle form submission from preview
   *
   * Processes form submission with success messaging and logging.
   *
   * @param {Object} values - Form values from submission
   * @returns {Promise} Promise that resolves after processing
   */
  const handleFormSubmit = useCallback(async (values) => {
    console.log('Form submitted with values:', values);
    message.success('Form submitted successfully!');
    return Promise.resolve();
  }, []);

  /**
   * Handle JSON export with validation and sanitization
   *
   * Validates the current schema, sanitizes it for storage, and triggers
   * a download of the JSON file with comprehensive error handling.
   */
  const handleExportJSON = useCallback(() => {
    try {
      // Validate schema before export
      const validation = validateFormSchema(currentFormSchema);

      if (!validation.isValid) {
        message.error({
          content: (
            <div>
              <strong>Schema Validation Failed:</strong>
              <ul style={{ marginTop: '8px', marginBottom: 0 }}>
                {validation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          ),
          duration: 8,
        });
        return;
      }

      // Show warnings if any
      if (validation.warnings.length > 0) {
        message.warning({
          content: (
            <div>
              <strong>Schema Warnings:</strong>
              <ul style={{ marginTop: '8px', marginBottom: 0 }}>
                {validation.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          ),
          duration: 6,
        });
      }

      // Sanitize schema for storage
      const sanitizedSchema = sanitizeSchemaForStorage(currentFormSchema);
      const dataStr = JSON.stringify(sanitizedSchema, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'form-schema.json';
      link.click();
      URL.revokeObjectURL(url);

      message.success({
        content: (
          <div>
            <strong>✅ Form schema exported successfully!</strong>
            <br />
            <small>Schema validated and optimized for MongoDB storage</small>
          </div>
        ),
        duration: 4,
      });
    } catch (error) {
      message.error(`Export failed: ${error.message}`);
    }
  }, [currentFormSchema]);

  /**
   * Handle JSON view modal display
   *
   * Opens the JSON view modal to display the current schema.
   */
  const handleViewJSON = useCallback(() => {
    setJsonModalVisible(true);
  }, [setJsonModalVisible]);

  /**
   * Handle JSON import modal display
   *
   * Opens the JSON import modal and resets the input field.
   */
  const handleImportJSON = useCallback(() => {
    console.log('🔍 [Import] handleImportJSON called');
    console.log(
      '🔍 [Import] setImportModalVisible function:',
      typeof setImportModalVisible,
    );
    console.log('🔍 [Import] setJsonInput function:', typeof setJsonInput);

    setImportModalVisible(true);
    setJsonInput('');

    console.log('🔍 [Import] Modal should be visible now');
  }, [setImportModalVisible, setJsonInput]);

  /**
   * Handle loading enhanced container test schema
   *
   * Loads the enhanced container test schema for testing purposes
   * with comprehensive error handling and user feedback.
   */
  const handleLoadEnhancedContainerTest = useCallback(() => {
    try {
      setLayout(enhancedContainerTestSchema.layout);
      setComponents(enhancedContainerTestSchema.components);
      setForceRenderKey((prev) => prev + 1);

      message.success({
        content: (
          <div>
            <strong>✅ Enhanced Container Test Schema Loaded</strong>
            <br />
            Test schema with enhanced drop zones for all container components
            has been loaded successfully.
          </div>
        ),
        duration: 5,
        style: { marginTop: '20vh' },
      });
    } catch (error) {
      console.error('Error loading enhanced container test schema:', error);
      message.error({
        content: (
          <div>
            <strong>❌ Failed to Load Test Schema</strong>
            <br />
            There was an error loading the enhanced container test schema.
          </div>
        ),
        duration: 5,
        style: { marginTop: '20vh' },
      });
    }
  }, [setLayout, setComponents, setForceRenderKey]);

  /**
   * Handle JSON import submission with comprehensive validation
   *
   * Processes JSON import with parsing, validation, sanitization,
   * and state updates with detailed error handling.
   */
  const handleImportSubmit = useCallback(() => {
    try {
      const parsedSchema = JSON.parse(jsonInput);

      // Basic structure check
      if (!parsedSchema.layout || !parsedSchema.components) {
        throw new Error(
          "Invalid schema format. Must contain 'layout' and 'components' properties.",
        );
      }

      // Comprehensive schema validation
      const validation = validateFormSchema(parsedSchema);

      if (!validation.isValid) {
        message.error({
          content: (
            <div>
              <strong>❌ Schema Import Failed:</strong>
              <ul style={{ marginTop: '8px', marginBottom: 0 }}>
                {validation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          ),
          duration: 10,
        });
        return;
      }

      // Show warnings but allow import
      if (validation.warnings.length > 0) {
        message.warning({
          content: (
            <div>
              <strong>⚠️ Schema Import Warnings:</strong>
              <ul style={{ marginTop: '8px', marginBottom: 0 }}>
                {validation.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
              <div
                style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}
              >
                Import will proceed despite warnings.
              </div>
            </div>
          ),
          duration: 8,
        });
      }

      // Sanitize imported schema
      const sanitizedSchema = sanitizeSchemaForStorage(parsedSchema);

      // Debug logging for import process
      console.log('🔍 [Import] Sanitized schema:', {
        layoutLength: sanitizedSchema.layout?.length || 0,
        componentsCount: Object.keys(sanitizedSchema.components || {}).length,
        originalLayout: parsedSchema.layout?.length || 0,
        originalComponents: Object.keys(parsedSchema.components || {}).length,
      });

      // Update state with proper error handling
      try {
        setLayout(sanitizedSchema.layout);
        setComponents(sanitizedSchema.components);

        // Force re-render to ensure Preview tab updates
        setForceRenderKey((prev) => prev + 1);

        setImportModalVisible(false);
        setJsonInput('');

        message.success({
          content: (
            <div>
              <strong>✅ Form schema imported successfully!</strong>
              <br />
              <small>
                Schema validated and ready for use.
                {validation.warnings.length === 0
                  ? ' No issues found.'
                  : ` ${validation.warnings.length} warnings noted.`}
              </small>
              <br />
              <small style={{ color: '#666' }}>
                Layout: {sanitizedSchema.layout?.length || 0} items, Components:{' '}
                {Object.keys(sanitizedSchema.components || {}).length}{' '}
                registered
              </small>
            </div>
          ),
          duration: 6,
        });
      } catch (updateError) {
        console.error('❌ [Import] State update failed:', updateError);
        message.error({
          content: (
            <div>
              <strong>State Update Error:</strong>
              <br />
              {updateError.message}
              <br />
              <small>Failed to update form builder state.</small>
            </div>
          ),
          duration: 8,
        });
      }
    } catch (error) {
      if (error instanceof SyntaxError) {
        message.error({
          content: (
            <div>
              <strong>JSON Parse Error:</strong>
              <br />
              {error.message}
              <br />
              <small>Please check your JSON syntax and try again.</small>
            </div>
          ),
          duration: 8,
        });
      } else {
        message.error(`Import failed: ${error.message}`);
      }
    }
  }, [
    jsonInput,
    setLayout,
    setComponents,
    setImportModalVisible,
    setJsonInput,
  ]);

  return {
    handleFormSubmit,
    handleExportJSON,
    handleViewJSON,
    handleImportJSON,
    handleLoadEnhancedContainerTest,
    handleImportSubmit,
  };
};
