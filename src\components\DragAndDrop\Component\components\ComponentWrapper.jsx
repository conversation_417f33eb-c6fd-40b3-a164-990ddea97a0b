/**
 * ComponentWrapper Component
 *
 * Main wrapper component that provides the visual container and interaction
 * handling for draggable form components. Integrates all the extracted
 * functionality including drag-and-drop, animations, and visual feedback.
 *
 * Features:
 * - Framer Motion animations for smooth interactions
 * - Drag-and-drop integration with visual feedback
 * - Hover and selection state management
 * - Accessibility compliance with proper ARIA attributes
 * - Container component differentiation
 * - Performance optimizations with memoization
 *
 * @module ComponentWrapper
 */

import React, { memo } from 'react';
import { AnimatePresence } from 'framer-motion';
import { Tooltip } from 'antd';
import { EditOutlined, DeleteOutlined, CopyOutlined } from '@ant-design/icons';
import { useDoubleClickHandler } from '../../../FormBuilderApp/components/PropertiesPanel';

// Import styled components
import {
  AdvancedComponentWrapper,
  ComponentOverlay,
  ComponentLabel,
  ComponentActions,
  DragHandle,
  ComponentTypeIndicator,
} from '../styles/StyledComponents';

// Import animation variants
import {
  componentVariants,
  labelVariants,
  dragHandleVariants,
} from '../constants/animations';

/**
 * ComponentWrapper Component
 *
 * Wraps form components with drag-and-drop functionality, visual feedback,
 * and interaction handlers. Provides consistent styling and behavior.
 *
 * @param {Object} props - Component props
 * @param {Object} props.componentInfo - Component type and label information
 * @param {boolean} props.isDragging - Whether component is being dragged
 * @param {boolean} props.isSelected - Whether component is selected
 * @param {boolean} props.isHovered - Whether component is hovered
 * @param {boolean} props.isContainerComponent - Whether this is a container component
 * @param {string} props.componentId - Unique component identifier
 * @param {Function} props.onSelect - Selection handler
 * @param {Function} props.onEdit - Edit handler
 * @param {Function} props.onDelete - Delete handler
 * @param {Function} props.onCopy - Copy handler
 * @param {Function} props.onMouseEnter - Mouse enter handler
 * @param {Function} props.onMouseLeave - Mouse leave handler
 * @param {React.Ref} props.dragRef - Ref for drag functionality
 * @param {React.ReactNode} props.children - Component content to render
 * @param {Object} props.componentData - Component data for properties panel
 * @param {Object} props.components - Components registry for lookups
 * @returns {JSX.Element} Wrapped component
 */
const ComponentWrapper = memo(
  ({
    componentInfo,
    isDragging,
    isSelected,
    isHovered,
    isContainerComponent,
    componentId,
    onSelect,
    onEdit,
    onDelete,
    onCopy,
    onMouseEnter,
    onMouseLeave,
    dragRef,
    children,
    componentData,
    components,
  }) => {
    // Double-click handler for properties panel
    const { getDoubleClickProps } = useDoubleClickHandler({
      componentData,
      componentId,
      components,
    });

    /**
     * Generates CSS classes for the wrapper based on state
     */
    const getWrapperClasses = () => {
      const classes = ['component-wrapper'];

      if (isDragging) classes.push('dragging');
      if (isSelected) classes.push('selected');
      if (isContainerComponent) classes.push('container-component');

      return classes.join(' ');
    };

    /**
     * Gets accessibility attributes for the wrapper
     */
    const getAccessibilityProps = () => ({
      role: 'button',
      tabIndex: 0,
      'aria-label': `${componentInfo?.type || 'Component'} - ${
        componentInfo?.label || 'Unlabeled'
      }`,
      'aria-selected': isSelected,
      'aria-grabbed': isDragging,
    });

    return (
      <AdvancedComponentWrapper
        ref={dragRef}
        className={getWrapperClasses()}
        onClick={onSelect}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        variants={componentVariants}
        initial='initial'
        animate={isDragging ? 'dragging' : 'initial'}
        whileHover='hover'
        whileTap='tap'
        layout
        layoutId={`component-${componentId}`}
        {...getAccessibilityProps()}
        {...getDoubleClickProps({ useNativeEvent: true })}
      >
        {/* Overlay for advanced visual effects */}
        <ComponentOverlay className='component-overlay' />

        {/* Component label that appears on hover */}
        <AnimatePresence>
          {isHovered && componentInfo?.label && (
            <ComponentLabel
              className='component-label'
              variants={labelVariants}
              initial='hidden'
              animate='visible'
              exit='hidden'
            >
              {componentInfo.label}
            </ComponentLabel>
          )}
        </AnimatePresence>

        {/* Drag handle that appears on hover */}
        <AnimatePresence>
          {isHovered && (
            <DragHandle
              className='drag-handle'
              variants={dragHandleVariants}
              initial='hidden'
              animate='visible'
              exit='hidden'
              whileHover='hover'
              whileTap={{ scale: 0.95 }}
            />
          )}
        </AnimatePresence>

        {/* Action buttons that appear on hover */}
        <ComponentActions className='component-actions'>
          <Tooltip title='Edit Component'>
            <div className='action-btn' onClick={onEdit}>
              <EditOutlined />
            </div>
          </Tooltip>
          <Tooltip title='Copy Component'>
            <div className='action-btn warning' onClick={onCopy}>
              <CopyOutlined />
            </div>
          </Tooltip>
          <Tooltip title='Delete Component'>
            <div className='action-btn danger' onClick={onDelete}>
              <DeleteOutlined />
            </div>
          </Tooltip>
        </ComponentActions>

        {/* Main component content */}
        {children}

        {/* Component type indicator */}
        <ComponentTypeIndicator>
          {componentInfo?.type || 'component'}
        </ComponentTypeIndicator>
      </AdvancedComponentWrapper>
    );
  },
);

// Set display name for debugging
ComponentWrapper.displayName = 'ComponentWrapper';

export default ComponentWrapper;
