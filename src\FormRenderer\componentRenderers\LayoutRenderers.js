/**
 * Layout Component Renderers
 * 
 * Renders layout components like Divider, Space, etc.
 * These components help structure and organize form layouts.
 */

import React from 'react';
import {
  Divider,
  Space,
  Button,
} from 'antd';

/**
 * Renders a Divider component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Divider component
 */
export const renderDivider = (component) => {
  return (
    <Divider
      type={component.styling?.type}
      orientation={component.styling?.orientation}
      orientationMargin={component.styling?.orientationMargin}
      dashed={component.styling?.dashed}
      plain={component.styling?.plain}
    >
      {component.children}
    </Divider>
  );
};

/**
 * Renders a Space component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Space component
 */
export const renderSpace = (component) => {
  return (
    <Space
      direction={component.styling?.direction}
      size={component.styling?.size}
      align={component.styling?.align}
      wrap={component.styling?.wrap}
    >
      {(component.children || []).map((child, index) => (
        <Button key={index}>{child.content}</Button>
      ))}
    </Space>
  );
};
