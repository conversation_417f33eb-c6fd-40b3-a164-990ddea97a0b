/**
 * @fileoverview Project Controls component
 *
 * This component provides project management controls including save, publish,
 * export, and project metadata editing functionality.
 *
 * @module ProjectControls
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useState, useCallback } from 'react';
import { Button, Input, Tooltip, Dropdown, Space } from 'antd';
import {
  SaveOutlined,
  CloudUploadOutlined,
  ExportOutlined,
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
  MoreOutlined,
  HistoryOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { useProjectState } from '../hooks/useProjectState';
import {
  ProjectSection,
  ProjectTitle,
  ProjectMeta,
  StatusBadge,
  ActionGroup,
} from '../styles/EnterpriseHeader.styles';
import { HEADER_ANIMATIONS } from '../constants/headerConstants';

/**
 * Project Controls component
 *
 * @param {Object} props - Component props
 * @param {Object} props.currentFormSchema - Current form schema
 * @param {Function} props.onSave - Save callback function
 * @param {Function} props.onPublish - Publish callback function
 * @param {Function} props.onExport - Export callback function
 * @returns {React.ReactNode} Project controls JSX
 */
const ProjectControls = ({
  currentFormSchema,
  onSave,
  onPublish,
  onExport,
}) => {
  // Project state management
  const {
    project,
    isModified,
    isSaving,
    isPublishing,
    lastSaved,
    saveProject,
    publishProject,
    exportProject,
    updateProjectName,
    toggleAutoSave,
    autoSaveEnabled,
  } = useProjectState({
    currentFormSchema,
    onSave,
    onPublish,
    onExport,
  });

  // Local state for inline editing
  const [isEditingName, setIsEditingName] = useState(false);
  const [editingName, setEditingName] = useState(project.name);

  /**
   * Handle project name editing
   */
  const handleStartEdit = useCallback(() => {
    setIsEditingName(true);
    setEditingName(project.name);
  }, [project.name]);

  const handleSaveEdit = useCallback(() => {
    updateProjectName(editingName);
    setIsEditingName(false);
  }, [editingName, updateProjectName]);

  const handleCancelEdit = useCallback(() => {
    setEditingName(project.name);
    setIsEditingName(false);
  }, [project.name]);

  const handleKeyPress = useCallback((e) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  }, [handleSaveEdit, handleCancelEdit]);

  /**
   * Format last saved time
   */
  const formatLastSaved = useCallback(() => {
    if (!lastSaved) return 'Never saved';
    
    const now = new Date();
    const diff = now - lastSaved;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just saved';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    return lastSaved.toLocaleDateString();
  }, [lastSaved]);

  /**
   * More actions dropdown menu
   */
  const moreActionsMenu = {
    items: [
      {
        key: 'duplicate',
        label: 'Duplicate Project',
        icon: <CopyOutlined />,
        onClick: () => {
          // TODO: Implement project duplication
          console.log('Duplicate project');
        },
      },
      {
        key: 'history',
        label: 'Version History',
        icon: <HistoryOutlined />,
        onClick: () => {
          // TODO: Implement version history
          console.log('View version history');
        },
      },
      {
        type: 'divider',
      },
      {
        key: 'autosave',
        label: `${autoSaveEnabled ? 'Disable' : 'Enable'} Auto-save`,
        onClick: toggleAutoSave,
      },
    ],
  };

  return (
    <ProjectSection>
      {/* Project Title with Inline Editing */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <AnimatePresence mode="wait">
          {isEditingName ? (
            <motion.div
              key="editing"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
            >
              <Input
                value={editingName}
                onChange={(e) => setEditingName(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleSaveEdit}
                autoFocus
                size="small"
                style={{ width: '200px' }}
              />
              <Button
                type="text"
                size="small"
                icon={<CheckOutlined />}
                onClick={handleSaveEdit}
                style={{ color: '#52c41a' }}
              />
              <Button
                type="text"
                size="small"
                icon={<CloseOutlined />}
                onClick={handleCancelEdit}
                style={{ color: '#ff4d4f' }}
              />
            </motion.div>
          ) : (
            <motion.div
              key="display"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
            >
              <ProjectTitle onClick={handleStartEdit}>
                {project.name}
              </ProjectTitle>
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={handleStartEdit}
                style={{ opacity: 0.6 }}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Project Metadata */}
      <ProjectMeta>
        <StatusBadge status={project.status}>
          {project.status}
        </StatusBadge>
        <span>•</span>
        <span>{formatLastSaved()}</span>
        {isModified && (
          <>
            <span>•</span>
            <span style={{ color: '#faad14' }}>Unsaved changes</span>
          </>
        )}
        {autoSaveEnabled && (
          <>
            <span>•</span>
            <span style={{ color: '#52c41a' }}>Auto-save on</span>
          </>
        )}
      </ProjectMeta>

      {/* Action Buttons */}
      <ActionGroup style={{ marginTop: '8px' }}>
        <Space size="small">
          {/* Save Button */}
          <Tooltip title={`Save project${autoSaveEnabled ? ' (Auto-save enabled)' : ''}`}>
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={HEADER_ANIMATIONS.SAVE_SUCCESS}
            >
              <Button
                type={isModified ? 'primary' : 'default'}
                size="small"
                icon={<SaveOutlined />}
                loading={isSaving}
                onClick={saveProject}
                disabled={!isModified && !isSaving}
              >
                Save
              </Button>
            </motion.div>
          </Tooltip>

          {/* Publish Button */}
          <Tooltip title="Publish project to make it accessible">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={HEADER_ANIMATIONS.PUBLISH_SUCCESS}
            >
              <Button
                type={project.status === 'published' ? 'default' : 'primary'}
                size="small"
                icon={<CloudUploadOutlined />}
                loading={isPublishing}
                onClick={publishProject}
                ghost={project.status === 'published'}
              >
                {project.status === 'published' ? 'Published' : 'Publish'}
              </Button>
            </motion.div>
          </Tooltip>

          {/* Export Button */}
          <Tooltip title="Export project in various formats">
            <Button
              size="small"
              icon={<ExportOutlined />}
              onClick={() => exportProject('json')}
            >
              Export
            </Button>
          </Tooltip>

          {/* More Actions */}
          <Dropdown menu={moreActionsMenu} trigger={['click']}>
            <Button
              size="small"
              icon={<MoreOutlined />}
              type="text"
            />
          </Dropdown>
        </Space>
      </ActionGroup>
    </ProjectSection>
  );
};

export default ProjectControls;
