/**
 * @fileoverview Custom hook for component properties management
 *
 * This hook encapsulates component property schema management, validation,
 * and property-specific logic for different component types.
 *
 * @module useComponentProperties
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useMemo, useCallback } from 'react';
import {
  getComponentPropertySchema,
  validatePropertyValue,
  formatPropertyValue,
  setNestedProperty,
} from '../utils/propertiesPanelUtils';
import { PROPERTY_CATEGORIES } from '../constants/propertiesPanelConstants';

/**
 * Custom hook for component properties management
 *
 * Provides property schema management, validation, and formatting
 * utilities for different component types in the properties panel.
 *
 * @param {Object} params - Hook parameters
 * @param {Object} params.componentData - Current component data
 * @param {string} params.componentType - Component type
 *
 * @returns {Object} Component properties utilities
 * @returns {Object} returns.propertySchema - Property schema for the component
 * @returns {Function} returns.validateProperty - Property validation function
 * @returns {Function} returns.formatProperty - Property formatting function
 * @returns {Function} returns.getPropertyCategories - Get organized property categories
 * @returns {Function} returns.getDefaultValue - Get default value for a property
 * @returns {Function} returns.updateComponentProperty - Update component property helper
 *
 * @example
 * ```jsx
 * const {
 *   propertySchema,
 *   validateProperty,
 *   formatProperty,
 *   getPropertyCategories,
 *   updateComponentProperty
 * } = useComponentProperties({
 *   componentData,
 *   componentType: 'input'
 * });
 * ```
 */
export const useComponentProperties = ({ componentData, componentType }) => {
  // Memoized property schema for the component type
  const propertySchema = useMemo(() => {
    return getComponentPropertySchema(componentType);
  }, [componentType]);

  // Memoized property categories with current values
  const propertyCategories = useMemo(() => {
    const categories = {};

    Object.entries(propertySchema).forEach(([categoryName, categoryProps]) => {
      categories[categoryName] = {
        name: categoryName,
        displayName: getCategoryDisplayName(categoryName),
        properties: Object.entries(categoryProps).map(([propName, propSchema]) => ({
          name: propName,
          displayName: getPropertyDisplayName(propName),
          schema: propSchema,
          currentValue: getNestedPropertyValue(componentData, propName),
          formattedValue: formatPropertyValue(
            getNestedPropertyValue(componentData, propName),
            propSchema,
          ),
        })),
      };
    });

    return categories;
  }, [propertySchema, componentData]);

  /**
   * Validates a property value against its schema
   *
   * @param {string} propertyName - Name of the property
   * @param {*} value - Value to validate
   * @returns {Object} Validation result
   */
  const validateProperty = useCallback(
    (propertyName, value) => {
      const propSchema = findPropertySchema(propertySchema, propertyName);
      if (!propSchema) {
        return { isValid: true, errors: [] };
      }

      return validatePropertyValue(value, propSchema);
    },
    [propertySchema],
  );

  /**
   * Formats a property value for display
   *
   * @param {string} propertyName - Name of the property
   * @param {*} value - Value to format
   * @returns {string} Formatted value
   */
  const formatProperty = useCallback(
    (propertyName, value) => {
      const propSchema = findPropertySchema(propertySchema, propertyName);
      if (!propSchema) {
        return String(value || '');
      }

      return formatPropertyValue(value, propSchema);
    },
    [propertySchema],
  );

  /**
   * Gets organized property categories for display
   *
   * @returns {Object} Organized property categories
   */
  const getPropertyCategories = useCallback(() => {
    return propertyCategories;
  }, [propertyCategories]);

  /**
   * Gets default value for a property
   *
   * @param {string} propertyName - Name of the property
   * @returns {*} Default value
   */
  const getDefaultValue = useCallback(
    (propertyName) => {
      const propSchema = findPropertySchema(propertySchema, propertyName);
      return propSchema?.default;
    },
    [propertySchema],
  );

  /**
   * Updates a component property with validation
   *
   * @param {Object} currentComponent - Current component data
   * @param {string} propertyPath - Property path (supports dot notation)
   * @param {*} value - New value
   * @returns {Object} Update result with new component data and validation
   */
  const updateComponentProperty = useCallback(
    (currentComponent, propertyPath, value) => {
      // Validate the new value
      const validation = validateProperty(propertyPath, value);

      if (!validation.isValid) {
        return {
          success: false,
          errors: validation.errors,
          componentData: currentComponent,
        };
      }

      // Update the component data
      const updatedComponent = setNestedProperty(currentComponent, propertyPath, value);

      return {
        success: true,
        errors: [],
        componentData: updatedComponent,
      };
    },
    [validateProperty],
  );

  /**
   * Gets property suggestions based on component type and context
   *
   * @param {string} propertyName - Name of the property
   * @returns {Array} Array of suggested values
   */
  const getPropertySuggestions = useCallback(
    (propertyName) => {
      const suggestions = {
        size: ['small', 'middle', 'large'],
        type: getComponentTypeSuggestions(componentType),
        placeholder: getPlaceholderSuggestions(componentType),
        label: getLabelSuggestions(componentType),
      };

      return suggestions[propertyName] || [];
    },
    [componentType],
  );

  /**
   * Checks if a property is required for the component type
   *
   * @param {string} propertyName - Name of the property
   * @returns {boolean} Whether the property is required
   */
  const isPropertyRequired = useCallback(
    (propertyName) => {
      const propSchema = findPropertySchema(propertySchema, propertyName);
      return propSchema?.required || false;
    },
    [propertySchema],
  );

  /**
   * Gets property constraints (min, max, options, etc.)
   *
   * @param {string} propertyName - Name of the property
   * @returns {Object} Property constraints
   */
  const getPropertyConstraints = useCallback(
    (propertyName) => {
      const propSchema = findPropertySchema(propertySchema, propertyName);
      if (!propSchema) return {};

      const { min, max, options, pattern, ...constraints } = propSchema;
      return { min, max, options, pattern, ...constraints };
    },
    [propertySchema],
  );

  return {
    // Schema and categories
    propertySchema,
    propertyCategories,

    // Validation and formatting
    validateProperty,
    formatProperty,

    // Property management
    getPropertyCategories,
    getDefaultValue,
    updateComponentProperty,

    // Property utilities
    getPropertySuggestions,
    isPropertyRequired,
    getPropertyConstraints,
  };
};

/**
 * Helper function to find property schema by name (supports nested paths)
 *
 * @param {Object} schema - Property schema object
 * @param {string} propertyName - Property name (supports dot notation)
 * @returns {Object|null} Property schema or null if not found
 */
const findPropertySchema = (schema, propertyName) => {
  for (const categoryProps of Object.values(schema)) {
    if (categoryProps[propertyName]) {
      return categoryProps[propertyName];
    }
  }
  return null;
};

/**
 * Helper function to get nested property value
 *
 * @param {Object} obj - Object to search
 * @param {string} path - Property path (supports dot notation)
 * @returns {*} Property value or undefined
 */
const getNestedPropertyValue = (obj, path) => {
  if (!obj) return undefined;

  const keys = path.split('.');
  let current = obj;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return undefined;
    }
  }

  return current;
};

/**
 * Helper function to get display name for property categories
 *
 * @param {string} categoryName - Category name
 * @returns {string} Display name
 */
const getCategoryDisplayName = (categoryName) => {
  const displayNames = {
    [PROPERTY_CATEGORIES.BASIC]: 'Basic Properties',
    [PROPERTY_CATEGORIES.STYLING]: 'Styling & Appearance',
    [PROPERTY_CATEGORIES.VALIDATION]: 'Validation Rules',
    [PROPERTY_CATEGORIES.BEHAVIOR]: 'Behavior Settings',
    [PROPERTY_CATEGORIES.ACCESSIBILITY]: 'Accessibility',
    [PROPERTY_CATEGORIES.ADVANCED]: 'Advanced Settings',
    [PROPERTY_CATEGORIES.ROLES]: 'Roles & Permissions',
  };

  return displayNames[categoryName] || categoryName;
};

/**
 * Helper function to get display name for properties
 *
 * @param {string} propertyName - Property name
 * @returns {string} Display name
 */
const getPropertyDisplayName = (propertyName) => {
  const displayNames = {
    label: 'Label',
    name: 'Field Name',
    placeholder: 'Placeholder',
    required: 'Required',
    disabled: 'Disabled',
    size: 'Size',
    minLength: 'Minimum Length',
    maxLength: 'Maximum Length',
    pattern: 'Pattern',
    rows: 'Rows',
    options: 'Options',
  };

  return displayNames[propertyName] || propertyName.charAt(0).toUpperCase() + propertyName.slice(1);
};

/**
 * Helper function to get component type suggestions
 *
 * @param {string} componentType - Component type
 * @returns {Array} Type suggestions
 */
const getComponentTypeSuggestions = (componentType) => {
  const suggestions = {
    button: ['default', 'primary', 'dashed', 'text', 'link'],
    input: ['text', 'password', 'email', 'number', 'tel', 'url'],
  };

  return suggestions[componentType] || [];
};

/**
 * Helper function to get placeholder suggestions
 *
 * @param {string} componentType - Component type
 * @returns {Array} Placeholder suggestions
 */
const getPlaceholderSuggestions = (componentType) => {
  const suggestions = {
    input: ['Enter text...', 'Type here...', 'Please enter...'],
    email: ['Enter email address...', '<EMAIL>'],
    textarea: ['Enter your message...', 'Type your text here...'],
    select: ['Please select...', 'Choose an option...'],
  };

  return suggestions[componentType] || [];
};

/**
 * Helper function to get label suggestions
 *
 * @param {string} componentType - Component type
 * @returns {Array} Label suggestions
 */
const getLabelSuggestions = (componentType) => {
  const suggestions = {
    input: ['Name', 'Title', 'Description', 'Value'],
    email: ['Email Address', 'Email', 'Contact Email'],
    textarea: ['Message', 'Comments', 'Description', 'Notes'],
    select: ['Category', 'Type', 'Status', 'Option'],
    button: ['Submit', 'Save', 'Cancel', 'Continue'],
  };

  return suggestions[componentType] || [];
};
