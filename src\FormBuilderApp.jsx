/**
 * @fileoverview Main FormBuilderApp component
 *
 * This is the main form builder application component that orchestrates
 * all form building functionality including AI generation, drag-and-drop,
 * and form preview capabilities.
 *
 * This refactored version maintains 100% functionality while improving
 * maintainability through modular architecture and separation of concerns.
 *
 * @module FormBuilderApp
 * @version 2.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { memo, useMemo, useCallback } from 'react';

// Import hooks
import { useFormBuilderState } from './components/FormBuilderApp/hooks/useFormBuilderState';
import { useAIFormGeneration } from './components/FormBuilderApp/hooks/useAIFormGeneration';
import { useDragAndDropHandlers } from './components/FormBuilderApp/hooks/useDragAndDropHandlers';
import { useFormActions } from './components/FormBuilderApp/hooks/useFormActions';
import { useTabManagement } from './components/FormBuilderApp/hooks/useTabManagement';

// Import components
import TabNavigation from './components/FormBuilderApp/components/TabNavigation';
import JSONModals from './components/FormBuilderApp/components/JSONModals';

// Import utilities
import { createMemoizedFormProps } from './components/FormBuilderApp/utils/formBuilderUtils';

import * as S from './styles';
import './utils/testRunner'; // Load AI test console

/**
 * Main FormBuilderApp component
 *
 * Orchestrates all form builder functionality using extracted hooks and components.
 * This refactored version maintains 100% functionality while improving maintainability.
 *
 * Features:
 * - AI-powered form generation with schema validation
 * - Drag-and-drop form building with advanced container support
 * - Real-time form preview with error boundary protection
 * - JSON import/export with comprehensive validation
 * - Modular architecture with separated concerns
 *
 * @returns {React.ReactNode} FormBuilderApp JSX
 */
const FormBuilderApp = memo(() => {
  // Extract all state management to custom hooks
  const {
    layout,
    setLayout,
    components,
    setComponents,
    forceRenderKey,
    setForceRenderKey,
    componentUpdateCounter,
    setComponentUpdateCounter,
    aiGenerationAttemptRef,
    currentFormSchema,
  } = useFormBuilderState();

  // Extract tab and modal management
  const {
    activeTab,
    setActiveTab,
    jsonModalVisible,
    setJsonModalVisible,
    importModalVisible,
    setImportModalVisible,
    jsonInput,
    setJsonInput,
  } = useTabManagement();

  // Extract AI form generation logic
  const { handleAIFormGenerated, handleSchemaUpdate } = useAIFormGeneration({
    setLayout,
    setComponents,
    setForceRenderKey,
    setActiveTab,
    aiGenerationAttemptRef,
    layout,
    components,
  });

  // Extract drag-and-drop handlers
  const { handleDrop, handleDropToTrashBin, handleComponentUpdate } =
    useDragAndDropHandlers({
      layout,
      components,
      setLayout,
      setComponents,
      setComponentUpdateCounter,
    });

  // Handle form schema updates (for header title changes, etc.)
  const handleFormSchemaUpdate = useCallback(
    (updatedSchema) => {
      console.log('🔄 [FormBuilderApp] Updating form schema:', updatedSchema);
      // Update the schema metadata while preserving layout and components
      // This is mainly for title changes and other metadata updates
      if (updatedSchema.title !== undefined) {
        // Force a re-render to update the header
        setForceRenderKey((prev) => prev + 1);
      }
    },
    [setForceRenderKey],
  );

  // Extract form actions
  const {
    handleFormSubmit,
    handleExportJSON,
    handleViewJSON,
    handleImportJSON,
    handleLoadEnhancedContainerTest,
    handleImportSubmit,
  } = useFormActions({
    currentFormSchema,
    setLayout,
    setComponents,
    setForceRenderKey,
    setJsonModalVisible,
    setImportModalVisible,
    setJsonInput,
    jsonInput,
  });

  // Memoized form props to prevent unnecessary re-renders
  const formProps = useMemo(() => createMemoizedFormProps(), []);

  // Prepare props for tab components
  const builderTabProps = useMemo(
    () => ({
      layout,
      components,
      forceRenderKey,
      handleDrop,
      handleDropToTrashBin,
      handleAIFormGenerated,
      handleSchemaUpdate,
      handleComponentUpdate,
      currentFormSchema,
      onUpdateFormSchema: handleFormSchemaUpdate,
    }),
    [
      layout,
      components,
      forceRenderKey,
      handleDrop,
      handleDropToTrashBin,
      handleAIFormGenerated,
      handleSchemaUpdate,
      handleComponentUpdate,
      currentFormSchema,
      handleFormSchemaUpdate,
    ],
  );

  const previewTabProps = useMemo(
    () => ({
      layout,
      components,
      forceRenderKey,
      componentUpdateCounter,
      currentFormSchema,
      handleFormSubmit,
      formProps,
      handleViewJSON,
      handleImportJSON,
      handleExportJSON,
      handleLoadEnhancedContainerTest,
    }),
    [
      layout,
      components,
      forceRenderKey,
      componentUpdateCounter,
      currentFormSchema,
      handleFormSubmit,
      formProps,
      handleViewJSON,
      handleImportJSON,
      handleExportJSON,
      handleLoadEnhancedContainerTest,
    ],
  );

  // Main render - simplified and clean
  return (
    <S.Body>
      <TabNavigation
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        builderTabProps={builderTabProps}
        previewTabProps={previewTabProps}
      />

      <JSONModals
        jsonModalVisible={jsonModalVisible}
        setJsonModalVisible={setJsonModalVisible}
        importModalVisible={importModalVisible}
        setImportModalVisible={setImportModalVisible}
        jsonInput={jsonInput}
        setJsonInput={setJsonInput}
        currentFormSchema={currentFormSchema}
        handleImportSubmit={handleImportSubmit}
      />
    </S.Body>
  );
});

// Set display name for debugging
FormBuilderApp.displayName = 'FormBuilderApp';

export default FormBuilderApp;
