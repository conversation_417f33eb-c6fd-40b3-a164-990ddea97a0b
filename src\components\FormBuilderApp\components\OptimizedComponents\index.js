/**
 * @fileoverview Optimized Components barrel exports
 *
 * This module provides performance-optimized versions of core components
 * with React.memo, useMemo, useCallback, and other optimization techniques.
 *
 * @module OptimizedComponents
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

// Optimized core components
export { default as Optimized<PERSON>orm<PERSON>enderer } from './OptimizedFormRenderer';
export { default as OptimizedComponentRenderer } from './OptimizedComponentRenderer';
export { default as OptimizedPropertiesPanel } from './OptimizedPropertiesPanel';
export { default as OptimizedSidebar } from './OptimizedSidebar';

// Optimized UI components
export { default as OptimizedButton } from './OptimizedButton';
export { default as OptimizedInput } from './OptimizedInput';
export { default as OptimizedSelect } from './OptimizedSelect';
export { default as OptimizedCard } from './OptimizedCard';

// Virtual components for large lists
export { default as VirtualComponentList } from './VirtualComponentList';
export { default as VirtualPropertyList } from './VirtualPropertyList';
export { default as VirtualHierarchyTree } from './VirtualHierarchyTree';

// Lazy loaded components
export { default as LazyAnalytics } from './LazyAnalytics';
export { default as LazyPreview } from './LazyPreview';
export { default as LazyAdvancedEditor } from './LazyAdvancedEditor';

// Performance monitoring components
export { default as PerformanceMonitor } from './PerformanceMonitor';
export { default as MemoryUsageIndicator } from './MemoryUsageIndicator';
export { default as RenderTimeTracker } from './RenderTimeTracker';

// Optimization utilities
export { default as MemoizedWrapper } from './MemoizedWrapper';
export { default as StablePropsProvider } from './StablePropsProvider';
export { default as OptimizationProvider } from './OptimizationProvider';

// HOCs for optimization
export { withPerformanceOptimization } from './withPerformanceOptimization';
export { withMemoization } from './withMemoization';
export { withLazyLoading } from './withLazyLoading';
export { withVirtualization } from './withVirtualization';
