/**
 * Component Utilities Module
 *
 * This module contains utility functions for component categorization, search filtering,
 * and data processing used in the CategorizedSidebar component.
 *
 * Features:
 * - Component categorization based on type and functionality
 * - Advanced search and filtering capabilities
 * - Data processing and transformation utilities
 * - Performance-optimized filtering algorithms
 * - Type-safe component handling
 *
 * Functions:
 * - categorizeComponents: Sorts components into categories
 * - filterComponentsBySearch: Filters components based on search terms
 * - calculateCategoryStats: Computes statistics for categories
 * - validateComponentStructure: Validates component data structure
 */

import {
  DEFAULT_CATEGORIES,
  DATA_ENTRY_TYPES,
  DATA_DISPLAY_TYPES,
  FEEDBACK_TYPES,
  NAVIGATION_TYPES,
  LAYOUT_TYPES,
  GENERAL_TYPES,
  CONTAINER_TYPES,
  categoryConfig,
} from '../constants/categoryConfig';

/**
 * Categorizes components based on their type and functionality
 *
 * This function is the core categorization engine for the sidebar. It takes
 * an array of sidebar items and organizes them into logical categories based
 * on their component type and intended use in form building.
 *
 * Categorization Algorithm:
 * 1. Initialize empty categories structure using DEFAULT_CATEGORIES template
 * 2. Iterate through each component item in the input array
 * 3. Extract component type from nested item.component.type structure
 * 4. Apply categorization rules in priority order (most specific first)
 * 5. Assign component to the first matching category
 * 6. Use General category as fallback for unknown or unmatched types
 * 7. Return organized categories object for UI consumption
 *
 * Category Priority Order (Important for Correct Classification):
 * 1. Data Entry - Form input components (highest priority for form builders)
 * 2. Containers - Layout containers that can hold other components
 * 3. Data Display - Information display components
 * 4. Feedback - User feedback and status indicators
 * 5. Navigation - Navigation and flow components
 * 6. Layout - Structure and spacing elements
 * 7. General - Fallback category for all other components
 *
 * Performance Considerations:
 * - Uses early return pattern for efficient type checking
 * - Spread operator creates new category arrays to avoid mutations
 * - Console warnings for debugging malformed component data
 * - Optimized for frequent re-categorization during search/filtering
 *
 * Error Handling:
 * - Gracefully handles missing or undefined component types
 * - Logs warnings for debugging without breaking the UI
 * - Assigns problematic items to General category as safe fallback
 *
 * @param {Array} items - Array of sidebar component items with structure:
 *   [{
 *     id: string,
 *     component: {
 *       type: string,     // Component type (e.g., 'input', 'button')
 *       content: string,  // Display name
 *       label?: string    // Optional label
 *     }
 *   }]
 * @returns {Object} Object with categorized components:
 *   {
 *     'Data Entry': Array,
 *     'Containers': Array,
 *     'Data Display': Array,
 *     'Feedback': Array,
 *     'Navigation': Array,
 *     'Layout': Array,
 *     'General': Array
 *   }
 *
 * @example
 * const items = [
 *   { id: '1', component: { type: 'input', content: 'Text Input' } },
 *   { id: '2', component: { type: 'button', content: 'Button' } },
 *   { id: '3', component: { type: 'tabContainer', content: 'Tab Container' } }
 * ];
 * const categorized = categorizeComponents(items);
 * // Returns: {
 * //   'Data Entry': [inputItem],
 * //   'General': [buttonItem],
 * //   'Containers': [tabContainerItem],
 * //   ... (other categories empty)
 * // }
 */
export const categorizeComponents = (items) => {
  // Initialize categories with empty arrays
  // Using spread operator to avoid reference issues
  const categories = { ...DEFAULT_CATEGORIES };

  // Reset all categories to empty arrays
  Object.keys(categories).forEach((key) => {
    categories[key] = [];
  });

  // Process each component item
  items.forEach((item) => {
    // Extract component type from item structure
    // Handle cases where component or type might be undefined
    const type = item?.component?.type;

    // Skip items without valid type
    if (!type) {
      console.warn('Component item missing type:', item);
      categories['General'].push(item);
      return;
    }

    // Categorize based on component type
    // Order matters - more specific categories first
    if (DATA_ENTRY_TYPES.includes(type)) {
      categories['Data Entry'].push(item);
    } else if (CONTAINER_TYPES.includes(type)) {
      categories['Containers'].push(item);
    } else if (DATA_DISPLAY_TYPES.includes(type)) {
      categories['Data Display'].push(item);
    } else if (FEEDBACK_TYPES.includes(type)) {
      categories['Feedback'].push(item);
    } else if (NAVIGATION_TYPES.includes(type)) {
      categories['Navigation'].push(item);
    } else if (LAYOUT_TYPES.includes(type)) {
      categories['Layout'].push(item);
    } else if (GENERAL_TYPES.includes(type)) {
      categories['General'].push(item);
    } else {
      console.info(
        `Unknown component type '${type}' assigned to General category`,
      );
      categories['General'].push(item);
    }
  });

  return categories;
};

/**
 * Filters components based on search terms
 *
 * This function performs advanced search filtering across component
 * properties and category information. It searches through multiple
 * fields to provide comprehensive search results.
 *
 * Search Fields:
 * - Component content/name
 * - Component label
 * - Component type
 * - Category name
 * - Category description
 *
 * @param {Object} categorizedItems - Object with categorized components
 * @param {string} searchTerm - Search term to filter by
 * @returns {Object} Filtered categorized components
 *
 * @example
 * const filtered = filterComponentsBySearch(categories, 'input');
 * // Returns categories containing only components matching 'input'
 */
export const filterComponentsBySearch = (categorizedItems, searchTerm) => {
  // Return all items if no search term provided
  if (!searchTerm || !searchTerm.trim()) {
    return categorizedItems;
  }

  const filtered = {};
  const searchLower = searchTerm.toLowerCase().trim();

  // Process each category
  Object.entries(categorizedItems).forEach(([category, items]) => {
    // Filter items within this category
    const matchingItems = items.filter((item) => {
      // Build searchable text from multiple fields
      const searchableFields = [
        item.component?.content || '',
        item.component?.label || '',
        item.component?.type || '',
        category || '',
        categoryConfig[category]?.description || '',
      ];

      // Combine all searchable text
      const searchableText = searchableFields.join(' ').toLowerCase();

      // Check if search term matches any field
      return searchableText.includes(searchLower);
    });

    // Only include categories with matching items
    if (matchingItems.length > 0) {
      filtered[category] = matchingItems;
    }
  });

  return filtered;
};

/**
 * Calculates statistics for categorized components
 *
 * This function computes various statistics about the categorized
 * components, useful for displaying counts and other metrics.
 *
 * @param {Object} categorizedItems - Object with categorized components
 * @returns {Object} Statistics object with counts and metrics
 *
 * @example
 * const stats = calculateCategoryStats(categories);
 * // Returns: { totalComponents: 25, categoryCounts: {...}, ... }
 */
export const calculateCategoryStats = (categorizedItems) => {
  const stats = {
    totalComponents: 0,
    categoryCounts: {},
    nonEmptyCategories: 0,
    largestCategory: null,
    largestCategorySize: 0,
  };

  // Calculate statistics for each category
  Object.entries(categorizedItems).forEach(([category, items]) => {
    const count = items.length;

    // Update category count
    stats.categoryCounts[category] = count;

    // Update total count
    stats.totalComponents += count;

    // Count non-empty categories
    if (count > 0) {
      stats.nonEmptyCategories++;
    }

    // Track largest category
    if (count > stats.largestCategorySize) {
      stats.largestCategory = category;
      stats.largestCategorySize = count;
    }
  });

  return stats;
};

/**
 * Validates component structure
 *
 * This function validates that component items have the required
 * structure and properties for proper categorization and display.
 *
 * @param {Array} items - Array of component items to validate
 * @returns {Object} Validation result with errors and warnings
 *
 * @example
 * const validation = validateComponentStructure(items);
 * if (!validation.isValid) {
 *   console.error('Validation errors:', validation.errors);
 * }
 */
export const validateComponentStructure = (items) => {
  const result = {
    isValid: true,
    errors: [],
    warnings: [],
    validItems: [],
    invalidItems: [],
  };

  // Validate each item
  items.forEach((item, index) => {
    const itemErrors = [];
    const itemWarnings = [];

    // Check required properties
    if (!item) {
      itemErrors.push(`Item at index ${index} is null or undefined`);
    } else {
      // Check for required id
      if (!item.id) {
        itemErrors.push(
          `Item at index ${index} missing required 'id' property`,
        );
      }

      // Check for component object
      if (!item.component) {
        itemErrors.push(
          `Item at index ${index} missing required 'component' property`,
        );
      } else {
        // Check component properties
        if (!item.component.type) {
          itemErrors.push(
            `Item at index ${index} missing component 'type' property`,
          );
        }

        if (!item.component.content && !item.component.label) {
          itemWarnings.push(
            `Item at index ${index} missing both 'content' and 'label' properties`,
          );
        }
      }
    }

    // Categorize item based on validation results
    if (itemErrors.length > 0) {
      result.invalidItems.push({
        item,
        errors: itemErrors,
        warnings: itemWarnings,
      });
      result.errors.push(...itemErrors);
    } else {
      result.validItems.push(item);
    }

    // Add warnings to global warnings
    result.warnings.push(...itemWarnings);
  });

  // Set overall validation status
  result.isValid = result.errors.length === 0;

  return result;
};
