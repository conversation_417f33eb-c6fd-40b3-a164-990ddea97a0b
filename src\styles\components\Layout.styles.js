import styled from 'styled-components';
import { colors, elevation, motionCurves } from '../theme';

// Columns container with improved spacing
export const Columns = styled.div`
  display: flex;
  padding: 12px 0;
  gap: 12px;
  min-height: 160px;
  align-items: stretch;

  /* Responsive adjustments */
  @media (max-width: 768px) {
    gap: 8px;
    min-height: 140px;
    padding: 8px 0;
  }
`;

// Column container with enhanced layout
export const ColumnContainer = styled.div`
  display: flex;
  gap: 12px;
  align-items: stretch;

  @media (max-width: 768px) {
    gap: 8px;
  }
`;

// Base draggable styling with enterprise design
export const Base = styled.div`
  padding: 12px;
  background-color: ${colors.background};
  cursor: move;
  border-radius: 6px;
  border: 1px solid ${colors.border};
  transition: all 0.15s ${motionCurves.decelerate};

  &:hover {
    border-color: ${colors.borderHover};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 4px rgba(0, 0, 0, 0.06);
  }

  &:active {
    transform: translateY(0);
  }
`;

// UX-optimized Column: Clear boundaries with progressive disclosure
export const Column = styled.div`
  background-color: ${colors.background};
  cursor: move;
  border: 1px dashed ${colors.borderInactive};
  border-radius: 8px;
  flex: 1 1 100%;
  padding: 16px;
  margin: 6px;
  min-height: 120px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  user-select: none;
  font-family: 'Segoe UI', sans-serif;

  /* UX: Clear visual affordance for drop zones */
  &:empty {
    background: linear-gradient(
      135deg,
      ${colors.backgroundSecondary} 0%,
      ${colors.background} 100%
    );
    border-style: dashed;

    &::after {
      content: 'Drop components here';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: ${colors.textTertiary};
      font-size: 12px;
      font-weight: 400;
      opacity: 0.7;
      pointer-events: none;
    }
  }

  /* UX-optimized hover: Clear interaction feedback with accessibility considerations */
  &:hover:not(.dragging),
  &:focus-within:not(.dragging) {
    border: 1px solid ${colors.borderHover};
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
    background: ${colors.backgroundSecondary};
    transform: translateY(-1px);

    &:before {
      color: ${colors.textOnPrimary};
      border-color: ${colors.primary};
      background: ${colors.primary};
      font-weight: 600;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }

    /* UX: Hide empty state message on hover to reduce visual noise */
    &:empty::after {
      opacity: 0.9;
      color: ${colors.primary};
    }
  }

  /* Accessibility: Keyboard focus indicator */
  &:focus-visible {
    outline: 2px solid ${colors.borderFocus};
    outline-offset: 2px;
  }

  &:active:not(.dragging) {
    transform: translateY(0);
    transition: all 0.1s ${motionCurves.accelerate};
  }

  &.dragging {
    opacity: 0.8;
    transform: rotate(1deg) scale(1.02);
    box-shadow: ${elevation.depth16};
    z-index: 1000;
    border-color: ${colors.primary};
    background: ${colors.background};
    transition: none;

    &:before {
      color: ${colors.textOnPrimary};
      border-color: ${colors.primary};
      background: ${colors.primary};
      font-weight: 600;
      box-shadow: ${elevation.depth2};
    }
  }

  &:before {
    content: 'Column';
    position: absolute;
    top: -6px;
    left: 12px;
    background: ${colors.background};
    padding: 3px 8px;
    font-size: 10px;
    color: ${colors.textSecondary};
    border: 1px solid ${colors.borderLight};
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    letter-spacing: 0.3px;
    text-transform: uppercase;
    pointer-events: none;
    font-family: 'Segoe UI', sans-serif;
    opacity: 0.8;

    /* UX: Only show label when it provides value */
    @media (max-width: 768px) {
      font-size: 9px;
      padding: 2px 6px;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    padding: 16px;
    min-height: 130px;
    margin: 4px;
  }
`;

// Row styling with Microsoft Fluent Design
export const Row = styled.div`
  background-color: ${colors.background};
  cursor: move;
  border: 1px dashed ${colors.borderInactive};
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  transition: all 0.15s ${motionCurves.decelerate};
  position: relative;
  user-select: none;
  font-family: 'Segoe UI', sans-serif;

  /* Visual indicator for rows with many columns */
  &[data-column-count='4'] {
    border-color: #faad14;
    background: linear-gradient(
      135deg,
      ${colors.background} 0%,
      rgba(250, 173, 20, 0.05) 100%
    );

    &::before {
      content: '⚠️ 4 columns';
      position: absolute;
      top: -8px;
      right: 8px;
      background: #faad14;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      z-index: 1;
    }
  }

  &[data-column-count='5'],
  &[data-column-count='6'] {
    border-color: #ff7875;
    background: linear-gradient(
      135deg,
      ${colors.background} 0%,
      rgba(255, 120, 117, 0.05) 100%
    );

    &::before {
      content: '⚠️ ' attr(data-column-count) ' columns';
      position: absolute;
      top: -8px;
      right: 8px;
      background: #ff7875;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      z-index: 1;
    }
  }

  /* Focused hover effect with shadow */
  &:hover:not(.dragging),
  &:focus-within:not(.dragging) {
    border: 1px solid ${colors.borderHover};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 4px rgba(0, 0, 0, 0.06);
    background: ${colors.backgroundSecondary};
  }

  &:active:not(.dragging) {
    transform: translateY(0);
    transition: all 0.1s ${motionCurves.accelerate};
  }

  &.dragging {
    opacity: 0.8;
    transform: rotate(1deg) scale(1.02);
    box-shadow: ${elevation.depth16};
    z-index: 1000;
    border-color: ${colors.primary};
    background: ${colors.background};
    transition: none;

    &:before {
      color: ${colors.textOnPrimary};
      border-color: ${colors.primary};
      background: ${colors.primary};
      font-weight: 600;
      box-shadow: ${elevation.depth2};
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    padding: 16px;
    margin: 8px 0;
  }
`;
