/**
 * @fileoverview Properties Tab component for component configuration
 *
 * This component renders the Properties tab content with component-specific
 * configuration options including labels, placeholders, validation rules, etc.
 *
 * @module PropertiesTab
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useCallback, useMemo } from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Divider,
  Typography,
  Space,
} from 'antd';
import {
  InfoCircleOutlined,
  SettingOutlined,
  EyeOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import * as FormStyles from '../styles/FormSections.styles';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * Properties Tab component
 *
 * Renders component-specific configuration forms for basic properties
 * like labels, placeholders, validation rules, and styling options.
 *
 * @param {Object} props - Component props
 * @param {Object} props.componentData - Current component data
 * @param {string} props.componentId - Component ID
 * @param {Function} props.onPropertyUpdate - Property update callback
 * @param {Object} props.formSchema - Current form schema for context
 *
 * @returns {JSX.Element} Properties tab content
 */
const PropertiesTab = ({
  componentData,
  componentId,
  onPropertyUpdate,
  formSchema,
}) => {
  // Form instance for validation
  const [form] = Form.useForm();

  // Handle form field changes
  const handleFieldChange = useCallback(
    (field, value) => {
      onPropertyUpdate(field, value, { immediate: false });
    },
    [onPropertyUpdate],
  );

  // Handle form field blur (immediate update)
  const handleFieldBlur = useCallback(
    (field, value) => {
      onPropertyUpdate(field, value, { immediate: true });
    },
    [onPropertyUpdate],
  );

  // Helper function to get component display name
  const getDisplayName = useCallback((type) => {
    const displayNames = {
      input: 'Text Input',
      textarea: 'Text Area',
      select: 'Select Dropdown',
      radio: 'Radio Group',
      checkbox: 'Checkbox',
      datepicker: 'Date Picker',
      upload: 'File Upload',
      inputnumber: 'Number Input',
      switch: 'Switch Toggle',
      button: 'Button',
      typography: 'Typography',
      row: 'Row Layout',
      column: 'Column Layout',
      cardContainer: 'Card Container',
      tabContainer: 'Tab Container',
      formSection: 'Form Section',
      accordionContainer: 'Accordion Container',
      stepsContainer: 'Steps Container',
    };
    return displayNames[type] || type.charAt(0).toUpperCase() + type.slice(1);
  }, []);

  // Helper function to get component category
  const getComponentCategory = useCallback((type) => {
    const categories = {
      input: 'Data Entry',
      textarea: 'Data Entry',
      select: 'Data Entry',
      radio: 'Data Entry',
      checkbox: 'Data Entry',
      datepicker: 'Data Entry',
      upload: 'Data Entry',
      inputnumber: 'Data Entry',
      switch: 'Data Entry',
      button: 'Display',
      typography: 'Display',
      row: 'Layout',
      column: 'Layout',
      cardContainer: 'Container',
      tabContainer: 'Container',
      formSection: 'Container',
      accordionContainer: 'Container',
      stepsContainer: 'Container',
    };
    return categories[type] || 'Other';
  }, []);

  // Helper function to get component-specific properties
  const getComponentSpecificProps = useCallback((type) => {
    const specificProps = {
      input: ['maxLength', 'allowClear', 'showCount', 'prefix', 'suffix'],
      textarea: ['rows', 'autoSize', 'maxLength', 'showCount'],
      select: ['mode', 'allowClear', 'showSearch', 'options'],
      radio: ['options', 'optionType'],
      checkbox: ['options', 'indeterminate'],
      datepicker: ['format', 'showTime', 'allowClear'],
      upload: ['accept', 'multiple', 'listType', 'maxCount'],
      inputnumber: ['min', 'max', 'step', 'precision'],
      switch: ['checkedChildren', 'unCheckedChildren'],
      button: ['type', 'shape', 'icon', 'loading', 'block'],
      typography: ['level', 'type', 'mark', 'code', 'delete', 'underline'],
    };
    return specificProps[type] || [];
  }, []);

  // Memoized component type information
  const componentTypeInfo = useMemo(() => {
    if (!componentData) return null;

    const type = componentData.type;
    const isFormComponent = [
      'input',
      'textarea',
      'select',
      'radio',
      'checkbox',
      'datepicker',
      'upload',
      'inputnumber',
      'switch',
      'rate',
      'slider',
      'autocomplete',
      'cascader',
      'mentions',
      'timepicker',
    ].includes(type);

    const isContainer = componentData.children !== undefined;
    const hasValidation = isFormComponent;
    const isLayoutComponent = ['row', 'column'].includes(type);
    const isDisplayComponent = [
      'button',
      'typography',
      'divider',
      'image',
      'avatar',
      'tag',
    ].includes(type);

    // Get component-specific properties
    const specificProps = getComponentSpecificProps(type);

    return {
      type,
      isFormComponent,
      isContainer,
      hasValidation,
      isLayoutComponent,
      isDisplayComponent,
      specificProps,
      displayName: getDisplayName(type),
      category: getComponentCategory(type),
    };
  }, [
    componentData,
    getDisplayName,
    getComponentCategory,
    getComponentSpecificProps,
  ]);

  // Render component-specific properties
  const renderComponentSpecificProps = (type, props) => {
    return props.map((propName) => {
      switch (propName) {
        case 'maxLength':
          return (
            <Form.Item
              key={propName}
              label='Maximum Length'
              tooltip='Maximum number of characters allowed'
            >
              <InputNumber
                min={0}
                placeholder='Max length'
                style={{ width: '100%' }}
                defaultValue={componentData[propName]}
                onChange={(value) => handleFieldChange(propName, value)}
                onBlur={(e) => handleFieldBlur(propName, e.target.value)}
              />
            </Form.Item>
          );

        case 'allowClear':
          return (
            <Form.Item
              key={propName}
              label='Allow Clear'
              valuePropName='checked'
              tooltip='Show clear button to remove input value'
            >
              <Switch
                defaultChecked={componentData[propName] || false}
                onChange={(checked) => {
                  handleFieldChange(propName, checked);
                  handleFieldBlur(propName, checked);
                }}
              />
            </Form.Item>
          );

        case 'showCount':
          return (
            <Form.Item
              key={propName}
              label='Show Character Count'
              valuePropName='checked'
              tooltip='Display character count indicator'
            >
              <Switch
                defaultChecked={componentData[propName] || false}
                onChange={(checked) => {
                  handleFieldChange(propName, checked);
                  handleFieldBlur(propName, checked);
                }}
              />
            </Form.Item>
          );

        case 'rows':
          return (
            <Form.Item
              key={propName}
              label='Rows'
              tooltip='Number of rows for textarea'
            >
              <InputNumber
                min={1}
                max={20}
                placeholder='Number of rows'
                style={{ width: '100%' }}
                defaultValue={componentData[propName] || 4}
                onChange={(value) => handleFieldChange(propName, value)}
                onBlur={(e) => handleFieldBlur(propName, e.target.value)}
              />
            </Form.Item>
          );

        case 'mode':
          return (
            <Form.Item
              key={propName}
              label='Selection Mode'
              tooltip='Select component mode'
            >
              <Select
                defaultValue={componentData[propName] || 'default'}
                style={{ width: '100%' }}
                onChange={(value) => handleFieldChange(propName, value)}
                onBlur={() =>
                  handleFieldBlur(propName, form.getFieldValue(propName))
                }
              >
                <Option value='default'>Single Select</Option>
                <Option value='multiple'>Multiple Select</Option>
                <Option value='tags'>Tags Mode</Option>
              </Select>
            </Form.Item>
          );

        case 'showSearch':
          return (
            <Form.Item
              key={propName}
              label='Show Search'
              valuePropName='checked'
              tooltip='Enable search functionality'
            >
              <Switch
                defaultChecked={componentData[propName] || false}
                onChange={(checked) => {
                  handleFieldChange(propName, checked);
                  handleFieldBlur(propName, checked);
                }}
              />
            </Form.Item>
          );

        case 'prefix':
          return (
            <Form.Item
              key={propName}
              label='Prefix'
              tooltip='Text or icon to show before input'
            >
              <Input
                placeholder='Enter prefix text'
                defaultValue={componentData[propName] || ''}
                onChange={(e) => handleFieldChange(propName, e.target.value)}
                onBlur={(e) => handleFieldBlur(propName, e.target.value)}
              />
            </Form.Item>
          );

        case 'suffix':
          return (
            <Form.Item
              key={propName}
              label='Suffix'
              tooltip='Text or icon to show after input'
            >
              <Input
                placeholder='Enter suffix text'
                defaultValue={componentData[propName] || ''}
                onChange={(e) => handleFieldChange(propName, e.target.value)}
                onBlur={(e) => handleFieldBlur(propName, e.target.value)}
              />
            </Form.Item>
          );

        default:
          return (
            <Form.Item
              key={propName}
              label={propName.charAt(0).toUpperCase() + propName.slice(1)}
              tooltip={`Configure ${propName} property`}
            >
              <Input
                placeholder={`Enter ${propName}`}
                defaultValue={componentData[propName] || ''}
                onChange={(e) => handleFieldChange(propName, e.target.value)}
                onBlur={(e) => handleFieldBlur(propName, e.target.value)}
              />
            </Form.Item>
          );
      }
    });
  };

  // Don't render if no component data
  if (!componentData || !componentTypeInfo) {
    return (
      <div
        style={{ textAlign: 'center', padding: '40px 20px', color: '#8c8c8c' }}
      >
        <InfoCircleOutlined
          style={{ fontSize: '24px', marginBottom: '12px' }}
        />
        <div>No component selected</div>
      </div>
    );
  }

  return (
    <div>
      {/* Enhanced Component Info Header */}
      <FormStyles.FormSection
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        <FormStyles.SectionHeader>
          <div className='section-icon'>
            <InfoCircleOutlined />
          </div>
          <h4 className='section-title'>
            {componentTypeInfo.displayName} Configuration
          </h4>
        </FormStyles.SectionHeader>

        <Space direction='vertical' size='small' style={{ width: '100%' }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text strong style={{ fontSize: '14px', color: '#262626' }}>
              Component Type
            </Text>
            <Text
              style={{ fontSize: '13px', color: '#1890ff', fontWeight: 600 }}
            >
              {componentTypeInfo.category}
            </Text>
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text strong style={{ fontSize: '14px', color: '#262626' }}>
              Component ID
            </Text>
            <Text code style={{ fontSize: '12px' }}>
              {componentId}
            </Text>
          </div>
        </Space>
      </FormStyles.FormSection>

      {/* Properties Form */}
      <Form
        form={form}
        layout='vertical'
        size='small'
        initialValues={{
          label: componentData.label || '',
          name: componentData.name || '',
          placeholder: componentData.placeholder || '',
          required: componentData.validation?.required || false,
          disabled: componentData.disabled || false,
          size: componentData.size || 'middle',
        }}
      >
        {/* Enhanced Basic Properties Section */}
        <FormStyles.FormSection>
          <FormStyles.SectionHeader>
            <div className='section-icon'>
              <SettingOutlined />
            </div>
            <h4 className='section-title'>Basic Properties</h4>
          </FormStyles.SectionHeader>

          <FormStyles.FormItem>
            <Form.Item
              label='Label'
              name='label'
              tooltip='Display label for the component'
            >
              <Input
                placeholder='Enter component label'
                onChange={(e) => handleFieldChange('label', e.target.value)}
                onBlur={(e) => handleFieldBlur('label', e.target.value)}
              />
            </Form.Item>
            <FormStyles.HelpText>
              <InfoCircleOutlined />
              The display label shown to users
            </FormStyles.HelpText>
          </FormStyles.FormItem>

          {componentTypeInfo.isFormComponent && (
            <FormStyles.FormItem>
              <Form.Item
                label='Field Name'
                name='name'
                tooltip='Unique field name for form submission'
              >
                <Input
                  placeholder='Enter field name'
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                  onBlur={(e) => handleFieldBlur('name', e.target.value)}
                />
              </Form.Item>
              <FormStyles.HelpText>
                <InfoCircleOutlined />
                Unique identifier for form data submission
              </FormStyles.HelpText>
            </FormStyles.FormItem>
          )}

          {(componentTypeInfo.type === 'input' ||
            componentTypeInfo.type === 'textarea' ||
            componentTypeInfo.type === 'select') && (
            <FormStyles.FormItem>
              <Form.Item
                label='Placeholder'
                name='placeholder'
                tooltip='Placeholder text shown when field is empty'
              >
                <Input
                  placeholder='Enter placeholder text'
                  onChange={(e) =>
                    handleFieldChange('placeholder', e.target.value)
                  }
                  onBlur={(e) => handleFieldBlur('placeholder', e.target.value)}
                />
              </Form.Item>
              <FormStyles.HelpText>
                <InfoCircleOutlined />
                Hint text displayed when the field is empty
              </FormStyles.HelpText>
            </FormStyles.FormItem>
          )}
        </FormStyles.FormSection>

        {/* Enhanced Styling Properties Section */}
        <FormStyles.FormSection>
          <FormStyles.SectionHeader>
            <div className='section-icon'>
              <EyeOutlined />
            </div>
            <h4 className='section-title'>Appearance & Styling</h4>
          </FormStyles.SectionHeader>

          <FormStyles.PropertyGroup>
            <div className='group-title'>Visual Settings</div>

            <FormStyles.FormItem>
              <Form.Item label='Size' name='size' tooltip='Component size'>
                <Select
                  onChange={(value) => handleFieldChange('size', value)}
                  onBlur={() =>
                    handleFieldBlur('size', form.getFieldValue('size'))
                  }
                >
                  <Option value='small'>Small</Option>
                  <Option value='middle'>Medium</Option>
                  <Option value='large'>Large</Option>
                </Select>
              </Form.Item>
              <FormStyles.HelpText>
                <InfoCircleOutlined />
                Controls the overall size of the component
              </FormStyles.HelpText>
            </FormStyles.FormItem>

            <FormStyles.ToggleRow>
              <div>
                <div className='toggle-label'>Disabled State</div>
                <div className='toggle-description'>
                  Prevent user interaction with this component
                </div>
              </div>
              <Form.Item
                name='disabled'
                valuePropName='checked'
                style={{ margin: 0 }}
              >
                <Switch
                  onChange={(checked) => {
                    handleFieldChange('disabled', checked);
                    handleFieldBlur('disabled', checked);
                  }}
                />
              </Form.Item>
            </FormStyles.ToggleRow>
          </FormStyles.PropertyGroup>
        </FormStyles.FormSection>

        {/* Enhanced Validation Properties Section */}
        {componentTypeInfo.hasValidation && (
          <FormStyles.FormSection>
            <FormStyles.SectionHeader>
              <div className='section-icon'>
                <CheckCircleOutlined />
              </div>
              <h4 className='section-title'>Validation Rules</h4>
            </FormStyles.SectionHeader>

            <FormStyles.PropertyGroup>
              <div className='group-title'>Field Requirements</div>

              <FormStyles.ToggleRow>
                <div>
                  <div className='toggle-label'>Required Field</div>
                  <div className='toggle-description'>
                    Make this field mandatory for form submission
                  </div>
                </div>
                <Form.Item
                  name='required'
                  valuePropName='checked'
                  style={{ margin: 0 }}
                >
                  <Switch
                    onChange={(checked) => {
                      handleFieldChange('validation.required', checked);
                      handleFieldBlur('validation.required', checked);
                    }}
                  />
                </Form.Item>
              </FormStyles.ToggleRow>

              {(componentTypeInfo.type === 'input' ||
                componentTypeInfo.type === 'textarea') && (
                <div style={{ marginTop: '12px' }}>
                  <div className='group-title'>Length Constraints</div>

                  <FormStyles.FormItem>
                    <Form.Item
                      label='Minimum Length'
                      tooltip='Minimum number of characters required'
                    >
                      <InputNumber
                        min={0}
                        placeholder='Min length'
                        style={{ width: '100%' }}
                        onChange={(value) =>
                          handleFieldChange('validation.minLength', value)
                        }
                        onBlur={(e) =>
                          handleFieldBlur(
                            'validation.minLength',
                            e.target.value,
                          )
                        }
                      />
                    </Form.Item>
                  </FormStyles.FormItem>

                  <FormStyles.FormItem>
                    <Form.Item
                      label='Maximum Length'
                      tooltip='Maximum number of characters allowed'
                    >
                      <InputNumber
                        min={0}
                        placeholder='Max length'
                        style={{ width: '100%' }}
                        onChange={(value) =>
                          handleFieldChange('validation.maxLength', value)
                        }
                        onBlur={(e) =>
                          handleFieldBlur(
                            'validation.maxLength',
                            e.target.value,
                          )
                        }
                      />
                    </Form.Item>
                  </FormStyles.FormItem>
                </div>
              )}
            </FormStyles.PropertyGroup>
          </FormStyles.FormSection>
        )}

        {/* Component-Specific Properties */}
        {componentTypeInfo.specificProps.length > 0 && (
          <>
            <Divider style={{ margin: '20px 0' }} />

            <Title level={5} style={{ fontSize: '14px', marginBottom: '12px' }}>
              {componentTypeInfo.displayName} Settings
            </Title>

            {renderComponentSpecificProps(
              componentTypeInfo.type,
              componentTypeInfo.specificProps,
            )}
          </>
        )}

        {/* Container Properties */}
        {componentTypeInfo.isContainer && (
          <>
            <Divider style={{ margin: '20px 0' }} />

            <Title level={5} style={{ fontSize: '14px', marginBottom: '12px' }}>
              Container Settings
            </Title>

            <Form.Item label='Title' tooltip='Container title or heading'>
              <Input
                placeholder='Enter container title'
                defaultValue={
                  componentData.title || componentData.cardProps?.title || ''
                }
                onChange={(e) => handleFieldChange('title', e.target.value)}
                onBlur={(e) => handleFieldBlur('title', e.target.value)}
              />
            </Form.Item>

            <Form.Item
              label='Collapsible'
              valuePropName='checked'
              tooltip='Allow users to collapse/expand this container'
            >
              <Switch
                defaultChecked={componentData.collapsible || false}
                onChange={(checked) => {
                  handleFieldChange('collapsible', checked);
                  handleFieldBlur('collapsible', checked);
                }}
              />
            </Form.Item>
          </>
        )}
      </Form>
    </div>
  );
};

export default PropertiesTab;
