/**
 * @fileoverview Unified Header Styles
 *
 * Modern, enterprise-grade styling for the unified header component
 * implementing Microsoft Fluent Design principles with 2024+ standards.
 *
 * @module UnifiedHeaderStyles
 * @version 2.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import styled from 'styled-components';
import { motion } from 'framer-motion';
import {
  colors,
  spacing,
  typography,
  elevation,
  borderRadius,
} from '../../../../../styles/theme';

/**
 * Main unified header container with enterprise styling
 */
export const UnifiedHeaderContainer = styled(motion.header)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${spacing.md} ${spacing.xl};
  background: ${colors.background};
  border-bottom: 1px solid ${colors.border};
  box-shadow: ${elevation.sm};
  position: relative;
  z-index: 100;
  min-height: 64px;

  /* Glass morphism effect for modern look */
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);

  /* Responsive design */
  @media (max-width: 1024px) {
    padding: ${spacing.sm} ${spacing.lg};
    min-height: 56px;
  }

  @media (max-width: 768px) {
    padding: ${spacing.sm} ${spacing.md};
    flex-wrap: wrap;
    gap: ${spacing.sm};
  }
`;

/**
 * Header section for logical grouping
 */
export const HeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.lg};

  &.left {
    flex: 1;
    min-width: 0;
  }

  &.center {
    flex: 0 0 auto;

    @media (max-width: 1200px) {
      display: none;
    }
  }

  &.right {
    flex: 0 0 auto;
    gap: ${spacing.md};

    @media (max-width: 768px) {
      flex-basis: 100%;
      order: 3;
      justify-content: space-between;
    }
  }
`;

/**
 * Project information section
 */
export const ProjectInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.xs};
  min-width: 0;

  .project-title {
    font-size: ${typography.fontSize.lg};
    font-weight: ${typography.fontWeight.semibold};
    color: ${colors.textPrimary};
    margin: 0;
    line-height: 1.2;

    /* Editable title styling */
    &.editable {
      cursor: text;
      padding: 2px 6px;
      border-radius: ${borderRadius.md};
      transition: all 0.2s ease;

      &:hover {
        background: ${colors.backgroundSecondary};
      }

      &:focus {
        outline: 2px solid ${colors.primary};
        background: ${colors.background};
      }
    }
  }

  .project-meta {
    display: flex;
    align-items: center;
    gap: ${spacing.sm};
    font-size: ${typography.fontSize.sm};
    color: ${colors.textSecondary};

    .status-indicator {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      padding: 2px 8px;
      border-radius: ${borderRadius.full};
      font-size: ${typography.fontSize.xs};
      font-weight: ${typography.fontWeight.medium};
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.saved {
        background: ${colors.successLight};
        color: ${colors.success};
      }

      &.modified {
        background: ${colors.warningLight};
        color: ${colors.warning};
      }

      &.published {
        background: ${colors.primaryLight};
        color: ${colors.primary};
      }
    }
  }
`;

/**
 * Action group for buttons
 */
export const ActionGroup = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};

  .ant-btn {
    height: 36px;
    border-radius: ${borderRadius.lg};
    font-weight: ${typography.fontWeight.medium};
    transition: all 0.2s ease;

    &.primary {
      background: ${colors.primary};
      border-color: ${colors.primary};

      &:hover {
        background: ${colors.primaryHover};
        border-color: ${colors.primaryHover};
        transform: translateY(-1px);
        box-shadow: ${elevation.md};
      }
    }

    &.secondary {
      background: ${colors.background};
      border-color: ${colors.border};
      color: ${colors.textPrimary};

      &:hover {
        border-color: ${colors.primary};
        color: ${colors.primary};
        transform: translateY(-1px);
        box-shadow: ${elevation.sm};
      }
    }

    &.ghost {
      background: transparent;
      border-color: transparent;
      color: ${colors.textSecondary};

      &:hover {
        background: ${colors.backgroundSecondary};
        color: ${colors.textPrimary};
      }
    }
  }

  .divider {
    width: 1px;
    height: 24px;
    background: ${colors.border};
    margin: 0 ${spacing.sm};
  }
`;

/**
 * Context indicator for current mode
 */
export const ContextIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  padding: ${spacing.sm} ${spacing.md};
  background: ${colors.backgroundSecondary};
  border: 1px solid ${colors.border};
  border-radius: ${borderRadius.lg};
  font-size: ${typography.fontSize.sm};
  font-weight: ${typography.fontWeight.medium};
  color: ${colors.textPrimary};

  .mode-icon {
    font-size: 16px;
  }

  &.builder {
    background: linear-gradient(
      135deg,
      ${colors.primaryLight} 0%,
      ${colors.backgroundSecondary} 100%
    );
    border-color: ${colors.primary}40;
    color: ${colors.primary};
  }

  &.preview {
    background: linear-gradient(
      135deg,
      ${colors.successLight} 0%,
      ${colors.backgroundSecondary} 100%
    );
    border-color: ${colors.success}40;
    color: ${colors.success};
  }
`;

/**
 * Metrics display for form statistics
 */
export const MetricsDisplay = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.lg};

  .metric-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    min-width: 60px;

    .metric-value {
      font-size: ${typography.fontSize.lg};
      font-weight: ${typography.fontWeight.semibold};
      color: ${colors.textPrimary};
      line-height: 1;
    }

    .metric-label {
      font-size: ${typography.fontSize.xs};
      color: ${colors.textSecondary};
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: ${typography.fontWeight.medium};
    }
  }

  .metric-divider {
    width: 1px;
    height: 32px;
    background: ${colors.border};
  }
`;

/**
 * Dropdown menu styling
 */
export const DropdownMenu = styled.div`
  .ant-dropdown-menu {
    border-radius: ${borderRadius.lg};
    box-shadow: ${elevation.lg};
    border: 1px solid ${colors.border};
    padding: ${spacing.sm};

    .ant-dropdown-menu-item {
      border-radius: ${borderRadius.md};
      margin-bottom: 2px;

      &:hover {
        background: ${colors.backgroundSecondary};
      }
    }
  }
`;

/**
 * Responsive design utilities
 */
export const ResponsiveContainer = styled.div`
  /* Desktop-first responsive design */
  .secondary-actions {
    display: flex;

    @media (max-width: 1200px) {
      display: none;
    }
  }

  .mobile-only {
    display: none;

    @media (max-width: 768px) {
      display: flex;
    }
  }

  .desktop-only {
    display: flex;

    @media (max-width: 768px) {
      display: none;
    }
  }
`;

/**
 * Enhanced button styling for enterprise look
 */
export const EnterpriseButton = styled(motion.button)`
  display: inline-flex;
  align-items: center;
  gap: ${spacing.sm};
  padding: ${spacing.sm} ${spacing.md};
  border: 1px solid ${colors.border};
  border-radius: ${borderRadius.lg};
  background: ${colors.background};
  color: ${colors.textPrimary};
  font-size: ${typography.fontSize.sm};
  font-weight: ${typography.fontWeight.medium};
  font-family: ${typography.fontFamily.primary};
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* Remove default button styles */
  outline: none;
  text-decoration: none;

  /* Hover state */
  &:hover {
    border-color: ${colors.primary};
    color: ${colors.primary};
    transform: translateY(-1px);
    box-shadow: ${elevation.sm};
  }

  /* Active state */
  &:active {
    transform: translateY(0);
    box-shadow: none;
  }

  /* Focus state for accessibility */
  &:focus-visible {
    outline: 2px solid ${colors.primary};
    outline-offset: 2px;
  }

  /* Primary variant */
  &.primary {
    background: ${colors.primary};
    border-color: ${colors.primary};
    color: ${colors.background};

    &:hover {
      background: ${colors.primaryHover};
      border-color: ${colors.primaryHover};
      color: ${colors.background};
    }
  }

  /* Ghost variant */
  &.ghost {
    background: transparent;
    border-color: transparent;
    color: ${colors.textSecondary};

    &:hover {
      background: ${colors.backgroundSecondary};
      color: ${colors.textPrimary};
    }
  }

  /* Disabled state */
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
`;
