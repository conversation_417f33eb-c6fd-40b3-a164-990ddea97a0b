/**
 * CategorizedSidebar Styled Components
 *
 * This module contains all styled-components used in the CategorizedSidebar.
 * Extracted from the main component for better maintainability and reusability.
 *
 * Features:
 * - Modern sidebar styling with enterprise-grade design
 * - Responsive layout with smooth animations
 * - Custom scrollbar styling for better UX
 * - Accessibility-compliant color contrasts
 * - Consistent theming with the design system
 *
 * Components:
 * - SidebarContainer: Main container with flex layout
 * - SidebarHeader: Header section with gradient separator
 * - SearchContainer: Search input styling with focus states
 * - CategoryHeader: Category panel headers with icons and counts
 * - StyledCollapse: Custom Ant Design collapse styling
 * - StatsCard: Bottom statistics card with gradient background
 */

import styled from 'styled-components';
import { Collapse, Card } from 'antd';
import { colors } from '../../../styles/theme';

/**
 * Main container for the categorized sidebar
 *
 * Features:
 * - Full height layout with flex column
 * - Consistent background and border styling
 * - Overflow handling for scrollable content
 * - Modern design system integration
 */
export const SidebarContainer = styled.div`
  height: 100%;
  background: ${colors.background};
  border-right: 1px solid ${colors.borderLight};
  display: flex;
  flex-direction: column;
  overflow: hidden;

  /* Ensure proper z-index for drag operations */
  position: relative;
  z-index: 1;
`;

/**
 * Header section of the sidebar
 *
 * Features:
 * - Gradient separator line at bottom
 * - Consistent padding and spacing
 * - Secondary background for visual hierarchy
 * - Smooth transition effects
 */
export const SidebarHeader = styled.div`
  padding: 12px 12px 8px;
  background: ${colors.backgroundSecondary};
  border-bottom: 1px solid ${colors.borderLight};
  position: relative;

  /* Modern gradient separator line */
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 12px;
    right: 12px;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      ${colors.primary}40 50%,
      transparent 100%
    );
  }
`;

/**
 * Search input container with enhanced styling
 *
 * Features:
 * - Custom border radius and padding
 * - Focus state with primary color accent
 * - Smooth transitions for all interactions
 * - Proper icon and placeholder styling
 */
export const SearchContainer = styled.div`
  margin-bottom: 12px;

  /* Enhanced Ant Design input styling */
  .ant-input-affix-wrapper {
    border-radius: 8px;
    border: 1px solid ${colors.borderLight};
    padding: 6px 10px;
    transition: all 0.2s ease;
    height: 32px;

    /* Focus state with primary color accent */
    &:focus-within {
      border-color: ${colors.primary};
      box-shadow: 0 0 0 2px ${colors.primary}20;
    }

    /* Input field styling */
    .ant-input {
      border: none;
      padding: 0;
      font-size: 13px;

      /* Placeholder styling with italic text */
      &::placeholder {
        color: ${colors.textTertiary};
        font-style: italic;
      }
    }

    /* Search icon styling */
    .anticon {
      color: ${colors.textSecondary};
      font-size: 12px;
    }
  }
`;

/**
 * Category header component for collapse panels
 *
 * Features:
 * - Flex layout with proper alignment
 * - Icon and text styling with consistent spacing
 * - Category count badge with primary color theming
 * - Responsive design for different screen sizes
 */
export const CategoryHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  font-size: 13px;
  color: ${colors.textPrimary};

  /* Category icon styling */
  .category-icon {
    font-size: 14px;
    color: ${colors.primary};
  }

  /* Category count badge */
  .category-count {
    background: ${colors.primary}15;
    color: ${colors.primary};
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    margin-left: auto;
    min-width: 18px;
    text-align: center;

    /* Smooth transition for count changes */
    transition: all 0.2s ease;
  }
`;

/**
 * Custom styled Ant Design Collapse component
 *
 * Features:
 * - Transparent background with custom borders
 * - Rounded corners and consistent spacing
 * - Custom header and content styling
 * - Smooth hover and active state transitions
 * - Custom scrollbar for content areas
 * - Proper z-index for drag-and-drop operations
 */
export const StyledCollapse = styled(Collapse)`
  background: transparent !important;
  border: none !important;

  /* Individual collapse items */
  .ant-collapse-item {
    margin-bottom: 4px !important;
    border: 1px solid ${colors.borderLight} !important;
    border-radius: 6px !important;
    background: ${colors.background} !important;

    /* Remove margin from last item */
    &:last-child {
      margin-bottom: 0 !important;
    }

    /* Collapse header styling */
    .ant-collapse-header {
      padding: 8px 12px !important;
      background: ${colors.backgroundSecondary} !important;
      border-radius: 6px 6px 0 0 !important;
      border-bottom: none !important;
      transition: all 0.2s ease !important;
      min-height: 36px !important;

      /* Hover state for better interactivity */
      &:hover {
        background: ${colors.surfaceHover} !important;
      }

      /* Arrow icon styling */
      .ant-collapse-arrow {
        color: ${colors.textSecondary} !important;
        font-size: 12px !important;
      }
    }

    /* Collapse content area */
    .ant-collapse-content {
      border-top: 1px solid ${colors.borderLight} !important;
      background: ${colors.background} !important;
      border-radius: 0 0 6px 6px !important;

      /* Content box with scrolling */
      .ant-collapse-content-box {
        padding: 8px !important;
        max-height: 400px;
        overflow-y: auto;

        /* Custom scrollbar styling */
        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: ${colors.gray300};
          border-radius: 2px;

          /* Hover state for scrollbar */
          &:hover {
            background: ${colors.gray400};
          }
        }
      }
    }

    /* Active state styling */
    &.ant-collapse-item-active {
      .ant-collapse-header {
        border-radius: 6px 6px 0 0 !important;
        background: ${colors.backgroundSecondary} !important;
      }
    }
  }
`;

/**
 * Statistics card at the bottom of the sidebar
 *
 * Features:
 * - Rounded corners with subtle shadow
 * - Gradient background for visual appeal
 * - Consistent spacing and typography
 * - Responsive design for different content
 */
export const StatsCard = styled(Card)`
  margin-top: 16px;
  border-radius: 12px !important;
  border: 1px solid ${colors.borderLight} !important;
  box-shadow: ${colors.shadowLight};

  /* Card body with gradient background */
  .ant-card-body {
    padding: 16px !important;
    background: linear-gradient(
      135deg,
      ${colors.primary}05 0%,
      ${colors.primary}02 100%
    );
  }
`;
