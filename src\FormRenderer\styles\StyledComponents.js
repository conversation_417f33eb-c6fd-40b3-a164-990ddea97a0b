/**
 * FormRenderer Styled Components
 * 
 * Styled components for consistent form styling across the FormRenderer module.
 * Uses styled-components for maintainable and themeable styling.
 */

import styled from 'styled-components';

/**
 * Main form wrapper component
 * 
 * Provides consistent styling for the form container with proper spacing
 * and responsive behavior.
 */
export const FormWrapper = styled.div`
  width: 100%;
  
  /* Responsive padding */
  padding: ${props => props.padding || '0'};
  
  /* Custom styling support */
  ${props => props.customStyles && props.customStyles}
  
  /* Ensure proper form layout */
  .ant-form {
    width: 100%;
  }
  
  /* Form item spacing */
  .ant-form-item {
    margin-bottom: ${props => props.itemSpacing || '16px'};
  }
  
  /* Last form item (buttons) spacing */
  .ant-form-item:last-child {
    margin-bottom: 0;
    margin-top: ${props => props.buttonSpacing || '24px'};
  }
`;

/**
 * Form actions container for submit/reset buttons
 * 
 * Provides consistent button layout and spacing.
 */
export const FormActions = styled.div`
  display: flex;
  gap: ${props => props.gap || '8px'};
  justify-content: ${props => props.justify || 'flex-start'};
  align-items: center;
  
  /* Responsive behavior */
  @media (max-width: 576px) {
    flex-direction: ${props => props.mobileDirection || 'column'};
    gap: ${props => props.mobileGap || '12px'};
    
    .ant-btn {
      width: ${props => props.mobileButtonWidth || 'auto'};
    }
  }
`;

/**
 * Form section wrapper for grouped form elements
 * 
 * Provides consistent spacing and visual separation between form sections.
 */
export const FormSection = styled.div`
  margin-bottom: ${props => props.sectionSpacing || '32px'};
  
  /* Section border styling */
  ${props => props.bordered && `
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
  `}
  
  /* Section title styling */
  .form-section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #262626;
  }
  
  /* Section description styling */
  .form-section-description {
    color: #8c8c8c;
    margin-bottom: 16px;
    font-size: 14px;
  }
`;

/**
 * Error boundary wrapper for form error states
 * 
 * Provides consistent error display styling.
 */
export const ErrorWrapper = styled.div`
  padding: 16px;
  border: 1px solid #ff4d4f;
  border-radius: 6px;
  background-color: #fff2f0;
  color: #ff4d4f;
  
  .error-title {
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .error-message {
    font-size: 14px;
    line-height: 1.5;
  }
`;

/**
 * Loading overlay for form submission states
 * 
 * Provides consistent loading state styling.
 */
export const LoadingOverlay = styled.div`
  position: relative;
  
  ${props => props.loading && `
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  `}
`;
