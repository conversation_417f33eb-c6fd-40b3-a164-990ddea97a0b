/**
 * @fileoverview Custom hook for comprehensive event handling and cleanup
 *
 * This hook provides comprehensive event handling for the properties panel
 * including keyboard navigation, outside clicks, focus management, and cleanup.
 *
 * @module useEventHandling
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useCallback, useEffect, useRef } from 'react';

/**
 * Custom hook for comprehensive event handling
 *
 * Provides keyboard navigation, outside click detection, focus management,
 * and proper cleanup for the properties panel.
 *
 * @param {Object} params - Hook parameters
 * @param {boolean} params.visible - Whether the panel is visible
 * @param {Function} params.onClose - Close callback
 * @param {boolean} params.isMobile - Mobile detection state
 * @param {HTMLElement} params.targetElement - Element that triggered the panel
 *
 * @returns {Object} Event handling utilities
 * @returns {Function} returns.handleBackdropClick - Backdrop click handler
 * @returns {Function} returns.handleKeyDown - Keyboard event handler
 * @returns {React.RefObject} returns.panelRef - Ref for the panel container
 * @returns {Function} returns.cleanup - Manual cleanup function
 *
 * @example
 * ```jsx
 * const { handleBackdropClick, panelRef } = useEventHandling({
 *   visible,
 *   onClose: handleClose,
 *   isMobile,
 *   targetElement
 * });
 * ```
 */
export const useEventHandling = ({
  visible,
  onClose,
  isMobile,
  targetElement,
}) => {
  const panelRef = useRef(null);
  const previousActiveElementRef = useRef(null);
  const isClosingRef = useRef(false);

  /**
   * Handles backdrop click events
   *
   * @param {MouseEvent} event - Click event
   */
  const handleBackdropClick = useCallback(
    (event) => {
      // Only close if clicking directly on the backdrop
      if (event.target === event.currentTarget && !isClosingRef.current) {
        console.log('🖱️ [useEventHandling] Backdrop click detected');
        isClosingRef.current = true;
        onClose();
        
        // Reset closing flag after a delay
        setTimeout(() => {
          isClosingRef.current = false;
        }, 300);
      }
    },
    [onClose],
  );

  /**
   * Handles outside click detection for desktop
   *
   * @param {MouseEvent} event - Click event
   */
  const handleOutsideClick = useCallback(
    (event) => {
      if (!visible || isMobile || isClosingRef.current) return;

      const panelElement = panelRef.current;
      const targetEl = targetElement;

      // Check if click is outside the panel and not on the target element
      if (
        panelElement &&
        !panelElement.contains(event.target) &&
        (!targetEl || !targetEl.contains(event.target))
      ) {
        console.log('🖱️ [useEventHandling] Outside click detected');
        isClosingRef.current = true;
        onClose();
        
        // Reset closing flag after a delay
        setTimeout(() => {
          isClosingRef.current = false;
        }, 300);
      }
    },
    [visible, isMobile, onClose, targetElement],
  );

  /**
   * Handles keyboard events
   *
   * @param {KeyboardEvent} event - Keyboard event
   */
  const handleKeyDown = useCallback(
    (event) => {
      if (!visible) return;

      switch (event.key) {
        case 'Escape':
          event.preventDefault();
          console.log('⌨️ [useEventHandling] Escape key pressed');
          onClose();
          break;

        case 'Tab':
          // Handle tab navigation within the panel
          if (panelRef.current) {
            const focusableElements = panelRef.current.querySelectorAll(
              'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            
            if (focusableElements.length > 0) {
              const firstElement = focusableElements[0];
              const lastElement = focusableElements[focusableElements.length - 1];
              
              if (event.shiftKey) {
                // Shift + Tab (backward)
                if (document.activeElement === firstElement) {
                  event.preventDefault();
                  lastElement.focus();
                }
              } else {
                // Tab (forward)
                if (document.activeElement === lastElement) {
                  event.preventDefault();
                  firstElement.focus();
                }
              }
            }
          }
          break;

        case 'Enter':
          // Handle Enter key on focusable elements
          if (event.target.tagName === 'BUTTON') {
            event.target.click();
          }
          break;

        default:
          break;
      }
    },
    [visible, onClose],
  );

  /**
   * Manages focus when panel opens/closes
   */
  useEffect(() => {
    if (visible) {
      // Store the previously focused element
      previousActiveElementRef.current = document.activeElement;
      
      // Focus the panel after a short delay to ensure it's rendered
      setTimeout(() => {
        if (panelRef.current) {
          const firstFocusableElement = panelRef.current.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          
          if (firstFocusableElement) {
            firstFocusableElement.focus();
          } else {
            panelRef.current.focus();
          }
        }
      }, 100);
    } else {
      // Restore focus to the previously focused element
      if (previousActiveElementRef.current && document.contains(previousActiveElementRef.current)) {
        previousActiveElementRef.current.focus();
      }
      previousActiveElementRef.current = null;
    }
  }, [visible]);

  /**
   * Set up global event listeners
   */
  useEffect(() => {
    if (!visible) return;

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown, true);
    
    if (!isMobile) {
      document.addEventListener('mousedown', handleOutsideClick, true);
    }

    // Cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
      document.removeEventListener('mousedown', handleOutsideClick, true);
    };
  }, [visible, isMobile, handleKeyDown, handleOutsideClick]);

  /**
   * Prevent body scroll when panel is open on mobile
   */
  useEffect(() => {
    if (visible && isMobile) {
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      
      return () => {
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [visible, isMobile]);

  /**
   * Manual cleanup function
   */
  const cleanup = useCallback(() => {
    // Remove all event listeners
    document.removeEventListener('keydown', handleKeyDown, true);
    document.removeEventListener('mousedown', handleOutsideClick, true);
    
    // Restore body scroll
    if (isMobile) {
      document.body.style.overflow = '';
    }
    
    // Restore focus
    if (previousActiveElementRef.current && document.contains(previousActiveElementRef.current)) {
      previousActiveElementRef.current.focus();
    }
    
    // Reset refs
    previousActiveElementRef.current = null;
    isClosingRef.current = false;
    
    console.log('🧹 [useEventHandling] Manual cleanup completed');
  }, [handleKeyDown, handleOutsideClick, isMobile]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    // Event handlers
    handleBackdropClick,
    handleKeyDown,
    
    // Refs
    panelRef,
    
    // Utilities
    cleanup,
  };
};
