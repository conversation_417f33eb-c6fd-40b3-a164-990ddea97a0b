/**
 * useMessageHandling Hook
 *
 * Custom hook for managing chat messages, AI form generation, and message processing.
 * Handles the core chat functionality including sending messages, processing AI responses,
 * and managing loading states.
 *
 * Features:
 * - Message state management with proper typing
 * - AI form generation with progress tracking
 * - Error handling and user feedback
 * - Message history management
 * - Processing stage tracking for better UX
 * - Keyboard shortcuts and input handling
 * - Auto-application of generated forms
 *
 * Performance Considerations:
 * - Uses useCallback for stable function references
 * - Optimized state updates to prevent unnecessary re-renders
 * - Efficient message array management
 *
 * @param {Object} options - Configuration options
 * @param {Function} options.onFormGenerated - Callback when form is generated
 * @param {boolean} options.aiConnected - AI connection status
 * @returns {Object} Message handling state and functions
 */

import { useState, useCallback } from 'react';
import { message as antMessage } from 'antd';
import { generateFormSchema } from '../../../services/aiService';
import {
  detectComplexity,
  suggestComponents,
} from '../../../services/promptTemplates';

/**
 * Initial welcome message for the AI chat
 * Provides users with context and examples of what the AI can do
 */
const WELCOME_MESSAGE = {
  id: 1,
  text: '👋 Welcome to AI-Powered Form Builder!\n\nI\'m your intelligent assistant, powered by Groq\'s Llama 3.3 70B. I can instantly generate complete, professional forms from simple descriptions.\n\n✨ **What I can create:**\n• Simple contact forms\n• Complex multi-step wizards\n• Enterprise onboarding flows\n• Survey forms with advanced logic\n• E-commerce checkout forms\n\n🚀 **Just tell me what you need!** For example:\n"Create a 5-step supplier registration form"\n"Build a customer feedback survey"\n"Make a job application form with file uploads"',
  isUser: false,
  timestamp: new Date(),
  type: 'welcome',
};

/**
 * Custom hook for message handling and AI form generation
 *
 * @param {Object} options - Configuration options
 * @param {Function} options.onFormGenerated - Callback when form is generated
 * @param {boolean} options.aiConnected - AI connection status
 * @returns {Object} Message handling state and functions
 */
export const useMessageHandling = ({ onFormGenerated, aiConnected }) => {
  // Message state management
  const [messages, setMessages] = useState([WELCOME_MESSAGE]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [aiStatus, setAiStatus] = useState('ready'); // ready, processing, error, success
  const [processingStage, setProcessingStage] = useState('');

  /**
   * Adds a new message to the chat
   * @param {Object} message - Message object to add
   */
  const addMessage = useCallback((message) => {
    setMessages((prev) => [
      ...prev,
      {
        ...message,
        id: message.id || Date.now(),
        timestamp: message.timestamp || new Date(),
      },
    ]);
  }, []);

  /**
   * Creates a user message object
   * @param {string} text - Message text
   * @returns {Object} User message object
   */
  const createUserMessage = useCallback(
    (text) => ({
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    }),
    [],
  );

  /**
   * Creates an AI response message object
   * @param {string} text - Response text
   * @param {Object} options - Additional message options
   * @returns {Object} AI message object
   */
  const createAIMessage = useCallback(
    (text, options = {}) => ({
      text,
      isUser: false,
      timestamp: new Date(),
      ...options,
    }),
    [],
  );

  /**
   * Handles the AI form generation process
   * @param {string} userInput - User's form description
   */
  const handleAIGeneration = useCallback(
    async (userInput) => {
      try {
        console.log('🤖 [useMessageHandling] Starting AI form generation...');

        // Detect complexity and enhance prompt
        const complexity = detectComplexity(userInput);
        const suggestedComponents = suggestComponents(userInput);

        console.log(
          `📊 [useMessageHandling] Detected complexity: ${complexity}`,
        );
        console.log(
          `🧩 [useMessageHandling] Suggested components:`,
          suggestedComponents,
        );

        // Show processing stages for better UX
        setProcessingStage('Analyzing your request...');
        await new Promise((resolve) => setTimeout(resolve, 500));

        setProcessingStage('Generating form schema...');

        // Generate form schema using AI
        const result = await generateFormSchema(userInput, {
          includeExamples: true,
          includeValidation: true,
          includeStyling: true,
        });

        if (result.success) {
          console.log('✅ [useMessageHandling] AI generation successful');
          console.log(
            '🔍 [useMessageHandling] Generated schema:',
            result.schema,
          );

          setProcessingStage('Validating schema...');
          await new Promise((resolve) => setTimeout(resolve, 300));

          setProcessingStage('Applying to builder...');
          setAiStatus('success');

          // Auto-apply the schema to the builder
          if (onFormGenerated) {
            console.log('🚀 [useMessageHandling] Applying form to builder...');
            onFormGenerated(result.schema);
            antMessage.success(
              '🚀 Form automatically applied to builder and preview!',
            );
          } else {
            console.error(
              '❌ [useMessageHandling] onFormGenerated callback not available',
            );
          }

          // Create success response message
          const successMessage = createAIMessage(
            `✅ I've generated and applied a ${complexity.toLowerCase()} form to your builder!

🎯 **The form is now live in your Builder and Preview tabs!**

**Form Details:**
• **Components:** ${
              result.schema.components
                ? Object.keys(result.schema.components).length
                : 0
            }
• **Layout:** ${result.schema.layout ? result.schema.layout.length : 0} sections
• **Complexity:** ${complexity}

You can immediately start editing with drag-and-drop or test the form functionality.`,
            {
              type: 'form-generated',
              schema: result.schema,
              complexity: complexity,
              suggestedComponents: suggestedComponents,
              metadata: result.metadata,
              autoApplied: true,
            },
          );

          addMessage(successMessage);
        } else {
          throw new Error(result.error || 'Failed to generate form');
        }
      } catch (error) {
        console.error('❌ [useMessageHandling] AI Generation Error:', error);
        setAiStatus('error');

        // Create error response message
        const errorMessage = createAIMessage(
          `❌ Sorry, I encountered an error: ${error.message}. Please try rephrasing your request or check your connection.`,
          { type: 'error' },
        );

        addMessage(errorMessage);
      } finally {
        setIsLoading(false);
        setProcessingStage('');
      }
    },
    [onFormGenerated, addMessage, createAIMessage],
  );

  /**
   * Handles sending a message and triggering AI response
   */
  const handleSendMessage = useCallback(async () => {
    if (!inputValue.trim() || isLoading) {
      console.log(
        '⚠️ [useMessageHandling] Cannot send message: empty input or loading',
      );
      return;
    }

    if (!aiConnected) {
      console.log(
        '⚠️ [useMessageHandling] Cannot send message: AI not connected',
      );
      antMessage.error(
        'AI service is not connected. Please check your configuration.',
      );
      return;
    }

    console.log('📤 [useMessageHandling] Sending message:', inputValue);

    // Add user message
    const userMessage = createUserMessage(inputValue);
    addMessage(userMessage);

    // Clear input and set loading state
    setInputValue('');
    setIsLoading(true);
    setAiStatus('processing');

    // Process AI generation
    await handleAIGeneration(inputValue);
  }, [
    inputValue,
    isLoading,
    aiConnected,
    createUserMessage,
    addMessage,
    handleAIGeneration,
  ]);

  /**
   * Handles keyboard input for message sending
   * @param {KeyboardEvent} e - Keyboard event
   */
  const handleKeyPress = useCallback(
    (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSendMessage();
      }
    },
    [handleSendMessage],
  );

  /**
   * Clears all messages except welcome message
   */
  const clearMessages = useCallback(() => {
    console.log('🗑️ [useMessageHandling] Clearing messages');
    setMessages([WELCOME_MESSAGE]);
  }, []);

  /**
   * Sets input value for modify functionality
   * @param {string} value - New input value
   */
  const setModifyInput = useCallback((value) => {
    setInputValue(value);
  }, []);

  return {
    // Message state
    messages,
    inputValue,
    isLoading,
    aiStatus,
    processingStage,

    // Message actions
    handleSendMessage,
    handleKeyPress,
    setInputValue,
    addMessage,
    clearMessages,
    setModifyInput,

    // Status helpers
    canSendMessage: !isLoading && inputValue.trim() && aiConnected,
    isProcessing: isLoading && aiStatus === 'processing',
    hasError: aiStatus === 'error',
    isSuccess: aiStatus === 'success',

    // Processing progress (for progress bars)
    processingProgress: processingStage.includes('Analyzing')
      ? 30
      : processingStage.includes('Generating')
      ? 70
      : processingStage.includes('Validating')
      ? 90
      : 15,
  };
};
