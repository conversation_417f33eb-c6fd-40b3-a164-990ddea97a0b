import styled from 'styled-components';
import { colors } from '../theme';

// Enhanced page container with advanced visual indicators
export const PageContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  background: linear-gradient(
    135deg,
    ${colors.backgroundTertiary} 0%,
    ${colors.background} 100%
  );
  height: 100%;
  overflow: hidden;
  position: relative;

  /* Advanced grid background pattern */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(
        rgba(0, 102, 204, 0.03) 1px,
        transparent 1px
      ),
      linear-gradient(90deg, rgba(0, 102, 204, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
  }

  /* Subtle radial gradient overlay */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 50% 20%,
      ${colors.primary}02 0%,
      transparent 70%
    );
    pointer-events: none;
    z-index: 1;
  }
`;

// Enhanced main page with advanced visual guides and indicators
export const Page = styled.div`
  flex: 1 1 auto;
  padding: 24px;
  margin: 0;
  background: transparent;
  overflow-y: auto;
  height: calc(100% - 80px);
  position: relative;
  z-index: 2;

  /* Enhanced visual guides when empty */
  &:empty::before {
    content: '🎨 Start building your form by dragging components from the sidebar';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: ${colors.textTertiary};
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 32px 48px;
    border-radius: 16px;
    border: 2px dashed ${colors.borderLight};
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    max-width: 400px;
    line-height: 1.5;
  }

  /* Visual alignment guides */
  &.show-guides::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 1px;
    height: 100%;
    background: linear-gradient(
      180deg,
      transparent 0%,
      ${colors.primary}40 50%,
      transparent 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  /* Modern scrollbar design */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(
      180deg,
      ${colors.primary}60 0%,
      ${colors.primary}40 100%
    );
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(
        180deg,
        ${colors.primary}80 0%,
        ${colors.primary}60 100%
      );
    }

    &:active {
      background: linear-gradient(
        180deg,
        ${colors.primary} 0%,
        ${colors.primaryDark} 100%
      );
    }
  }

  /* Component spacing and layout indicators */
  .component-wrapper + .component-wrapper {
    margin-top: 12px;
  }

  /* Responsive design */
  @media (max-width: 1024px) {
    padding: 20px;
  }

  @media (max-width: 768px) {
    padding: 16px;

    &:empty::before {
      font-size: 14px;
      padding: 24px 32px;
      max-width: 300px;
    }
  }

  @media (max-width: 480px) {
    padding: 12px;

    &:empty::before {
      font-size: 13px;
      padding: 20px 24px;
      max-width: 250px;
    }
  }
`;
