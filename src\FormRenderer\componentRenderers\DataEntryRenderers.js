/**
 * Data Entry Component Renderers
 * 
 * Renders basic data entry components like inputs, textareas, selects, etc.
 * Each function handles the rendering logic for a specific component type.
 */

import React from 'react';
import {
  Input,
  Select,
  Radio,
  Checkbox,
  DatePicker,
  InputNumber,
  Switch,
  Rate,
  Slider,
  Upload,
  Button,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;

/**
 * Renders a text input component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered input component
 */
export const renderInput = (component, commonProps) => {
  return (
    <Input
      {...commonProps}
      allowClear={component.styling?.allowClear}
      prefix={component.styling?.prefix}
      suffix={component.styling?.suffix}
      addonBefore={component.styling?.addonBefore}
      addonAfter={component.styling?.addonAfter}
      maxLength={component.validation?.max}
      showCount={component.styling?.showCount}
    />
  );
};

/**
 * Renders an email input component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered email input component
 */
export const renderEmail = (component, commonProps) => {
  return (
    <Input
      {...commonProps}
      type="email"
      allowClear={component.styling?.allowClear}
      prefix={component.styling?.prefix}
      suffix={component.styling?.suffix}
      addonBefore={component.styling?.addonBefore}
      addonAfter={component.styling?.addonAfter}
      maxLength={component.validation?.max}
      showCount={component.styling?.showCount}
    />
  );
};

/**
 * Renders a textarea component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered textarea component
 */
export const renderTextarea = (component, commonProps) => {
  return (
    <TextArea
      {...commonProps}
      rows={component.styling?.rows || 4}
      autoSize={component.styling?.autoSize}
      allowClear={component.styling?.allowClear}
      maxLength={component.validation?.max}
      showCount={component.styling?.showCount}
    />
  );
};

/**
 * Renders a select component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered select component
 */
export const renderSelect = (component, commonProps) => {
  return (
    <Select
      {...commonProps}
      mode={component.styling?.mode}
      allowClear={component.styling?.allowClear}
      showSearch={component.styling?.showSearch}
      filterOption={component.styling?.filterOption}
      loading={component.styling?.loading}
      maxTagCount={component.styling?.maxTagCount}
    >
      {(component.options || []).map((option) => (
        <Option
          key={option.value}
          value={option.value}
          disabled={option.disabled}
        >
          {option.label}
        </Option>
      ))}
    </Select>
  );
};

/**
 * Renders a radio group component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered radio group component
 */
export const renderRadio = (component, commonProps) => {
  return (
    <Radio.Group
      {...commonProps}
      buttonStyle={component.styling?.buttonStyle}
      optionType={component.styling?.optionType}
    >
      {(component.options || []).map((option) => (
        <Radio
          key={option.value}
          value={option.value}
          disabled={option.disabled}
          style={{
            display:
              component.styling?.direction === 'horizontal'
                ? 'inline-block'
                : 'block',
            marginBottom:
              component.styling?.direction === 'horizontal' ? 0 : '8px',
          }}
        >
          {option.label}
        </Radio>
      ))}
    </Radio.Group>
  );
};

/**
 * Renders a checkbox component (single or group)
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @param {*} value - Current field value
 * @param {Function} onChange - Change handler
 * @returns {JSX.Element} Rendered checkbox component
 */
export const renderCheckbox = (component, commonProps, value, onChange) => {
  if (component.options && component.options.length > 0) {
    // Checkbox group
    return (
      <Checkbox.Group {...commonProps} options={component.options} />
    );
  } else {
    // Single checkbox
    return (
      <Checkbox
        {...commonProps}
        checked={value}
        onChange={(e) => onChange && onChange(e.target.checked)}
      >
        {component.label || component.text}
      </Checkbox>
    );
  }
};

/**
 * Renders a number input component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered number input component
 */
export const renderNumber = (component, commonProps) => {
  return (
    <InputNumber
      {...commonProps}
      min={component.validation?.min}
      max={component.validation?.max}
      step={component.styling?.step}
      precision={component.styling?.precision}
      formatter={component.styling?.formatter}
      parser={component.styling?.parser}
      controls={component.styling?.controls}
      keyboard={component.styling?.keyboard}
    />
  );
};

/**
 * Renders a password input component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered password input component
 */
export const renderPassword = (component, commonProps) => {
  return (
    <Input.Password
      {...commonProps}
      allowClear={component.styling?.allowClear}
      visibilityToggle={component.styling?.visibilityToggle !== false}
      iconRender={component.styling?.iconRender}
      maxLength={component.validation?.max}
      showCount={component.styling?.showCount}
    />
  );
};

/**
 * Renders a date picker component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered date picker component
 */
export const renderDatePicker = (component, commonProps) => {
  return (
    <DatePicker
      {...commonProps}
      format={component.styling?.format}
      picker={component.styling?.picker}
      showTime={component.styling?.showTime}
      allowClear={component.styling?.allowClear}
      disabledDate={component.styling?.disabledDate}
    />
  );
};

/**
 * Renders a date range picker component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered date range picker component
 */
export const renderRangePicker = (component, commonProps) => {
  return (
    <DatePicker.RangePicker
      {...commonProps}
      format={component.styling?.format}
      picker={component.styling?.picker}
      showTime={component.styling?.showTime}
      allowClear={component.styling?.allowClear}
      disabledDate={component.styling?.disabledDate}
    />
  );
};

/**
 * Renders a switch component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered switch component
 */
export const renderSwitch = (component, commonProps) => {
  return (
    <Switch
      {...commonProps}
      checkedChildren={component.styling?.checkedChildren}
      unCheckedChildren={component.styling?.unCheckedChildren}
      loading={component.styling?.loading}
    />
  );
};

/**
 * Renders a rate component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered rate component
 */
export const renderRate = (component, commonProps) => {
  return (
    <Rate
      {...commonProps}
      count={component.styling?.count || 5}
      allowHalf={component.styling?.allowHalf}
      allowClear={component.styling?.allowClear}
      character={component.styling?.character}
      tooltips={component.styling?.tooltips}
    />
  );
};

/**
 * Renders a slider component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered slider component
 */
export const renderSlider = (component, commonProps) => {
  return (
    <Slider
      {...commonProps}
      min={component.validation?.min || 0}
      max={component.validation?.max || 100}
      step={component.styling?.step}
      range={component.styling?.range}
      marks={component.styling?.marks}
      dots={component.styling?.dots}
      included={component.styling?.included}
      reverse={component.styling?.reverse}
      vertical={component.styling?.vertical}
      tooltipVisible={component.styling?.tooltipVisible}
      tooltipPlacement={component.styling?.tooltipPlacement}
    />
  );
};

/**
 * Renders an upload component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered upload component
 */
export const renderUpload = (component, commonProps) => {
  return (
    <Upload
      {...commonProps}
      action={component.styling?.action}
      listType={component.styling?.listType}
      multiple={component.styling?.multiple}
      accept={component.styling?.accept}
      beforeUpload={component.styling?.beforeUpload}
      customRequest={component.styling?.customRequest}
      showUploadList={component.styling?.showUploadList}
      maxCount={component.styling?.maxCount}
    >
      <Button icon={<UploadOutlined />}>
        {component.styling?.uploadText || 'Click to Upload'}
      </Button>
    </Upload>
  );
};
