/**
 * useComponentActions Hook
 * 
 * Custom hook for managing component action handlers including edit, delete,
 * copy, and selection functionality. Provides consistent action handling
 * across all component types with proper event management and callbacks.
 * 
 * Features:
 * - Component selection state management
 * - Edit, delete, and copy action handlers
 * - Event propagation control for nested interactions
 * - Hover state management for visual feedback
 * - Action validation and error handling
 * - Extensible action system for custom actions
 * 
 * @module useComponentActions
 */

import { useState, useCallback } from 'react';

/**
 * Custom hook for component action handling
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.componentId - Component ID for actions
 * @param {Object} options.componentData - Component data object
 * @param {Function} options.onEdit - Callback for edit action
 * @param {Function} options.onDelete - Callback for delete action
 * @param {Function} options.onCopy - Callback for copy action
 * @param {Function} options.onSelect - Callback for selection changes
 * @returns {Object} Action handlers and state
 * 
 * @example
 * const {
 *   isSelected,
 *   isHovered,
 *   handleEdit,
 *   handleDelete,
 *   handleCopy,
 *   handleSelect,
 *   setIsHovered
 * } = useComponentActions({
 *   componentId: 'comp-123',
 *   componentData: component,
 *   onEdit: (id) => console.log('Edit:', id),
 *   onDelete: (id) => console.log('Delete:', id),
 *   onCopy: (id) => console.log('Copy:', id)
 * });
 */
export const useComponentActions = ({
  componentId,
  componentData,
  onEdit,
  onDelete,
  onCopy,
  onSelect,
}) => {
  // Component interaction state
  const [isSelected, setIsSelected] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  /**
   * Handles component edit action
   * 
   * Triggers edit mode or opens component editor based on component type.
   * Prevents event bubbling to avoid triggering parent handlers.
   * 
   * @param {Event} e - Click event
   */
  const handleEdit = useCallback(
    (e) => {
      e.stopPropagation();
      
      console.log('📝 [useComponentActions] Edit component:', {
        id: componentId,
        type: componentData?.type,
        label: componentData?.label,
      });
      
      if (onEdit) {
        onEdit(componentId, componentData);
      } else {
        // Default edit behavior - could open a properties panel
        console.log('🔧 [useComponentActions] No edit handler provided, using default behavior');
        // TODO: Implement default edit behavior (e.g., open properties panel)
      }
    },
    [componentId, componentData, onEdit],
  );

  /**
   * Handles component delete action
   * 
   * Removes component from the form builder with confirmation if needed.
   * Prevents accidental deletions through event handling.
   * 
   * @param {Event} e - Click event
   */
  const handleDelete = useCallback(
    (e) => {
      e.stopPropagation();
      
      console.log('🗑️ [useComponentActions] Delete component:', {
        id: componentId,
        type: componentData?.type,
        label: componentData?.label,
      });
      
      if (onDelete) {
        onDelete(componentId, componentData);
      } else {
        // Default delete behavior with confirmation
        console.log('⚠️ [useComponentActions] No delete handler provided, using default behavior');
        const confirmed = window.confirm(
          `Are you sure you want to delete this ${componentData?.type || 'component'}?`
        );
        
        if (confirmed) {
          console.log('✅ [useComponentActions] Delete confirmed by user');
          // TODO: Implement default delete behavior
        } else {
          console.log('❌ [useComponentActions] Delete cancelled by user');
        }
      }
    },
    [componentId, componentData, onDelete],
  );

  /**
   * Handles component copy action
   * 
   * Creates a duplicate of the component with new ID and positioning.
   * Maintains all properties and styling of the original component.
   * 
   * @param {Event} e - Click event
   */
  const handleCopy = useCallback(
    (e) => {
      e.stopPropagation();
      
      console.log('📋 [useComponentActions] Copy component:', {
        id: componentId,
        type: componentData?.type,
        label: componentData?.label,
      });
      
      if (onCopy) {
        onCopy(componentId, componentData);
      } else {
        // Default copy behavior
        console.log('📄 [useComponentActions] No copy handler provided, using default behavior');
        
        try {
          // Copy component data to clipboard as JSON
          const componentJson = JSON.stringify(componentData, null, 2);
          navigator.clipboard.writeText(componentJson);
          console.log('✅ [useComponentActions] Component data copied to clipboard');
        } catch (error) {
          console.error('❌ [useComponentActions] Failed to copy to clipboard:', error);
        }
      }
    },
    [componentId, componentData, onCopy],
  );

  /**
   * Handles component selection toggle
   * 
   * Manages component selection state for multi-select operations
   * and visual feedback. Prevents event bubbling to parent components.
   * 
   * @param {Event} e - Click event
   */
  const handleSelect = useCallback(
    (e) => {
      e.stopPropagation();
      
      const newSelectedState = !isSelected;
      setIsSelected(newSelectedState);
      
      console.log('🎯 [useComponentActions] Component selection changed:', {
        id: componentId,
        selected: newSelectedState,
        type: componentData?.type,
      });
      
      if (onSelect) {
        onSelect(componentId, newSelectedState, componentData);
      }
    },
    [isSelected, componentId, componentData, onSelect],
  );

  /**
   * Handles mouse enter for hover effects
   * 
   * @param {Event} e - Mouse enter event
   */
  const handleMouseEnter = useCallback(
    (e) => {
      setIsHovered(true);
    },
    [],
  );

  /**
   * Handles mouse leave for hover effects
   * 
   * @param {Event} e - Mouse leave event
   */
  const handleMouseLeave = useCallback(
    (e) => {
      setIsHovered(false);
    },
    [],
  );

  /**
   * Programmatically selects the component
   * 
   * @param {boolean} selected - Selection state
   */
  const selectComponent = useCallback(
    (selected = true) => {
      setIsSelected(selected);
      
      if (onSelect) {
        onSelect(componentId, selected, componentData);
      }
    },
    [componentId, componentData, onSelect],
  );

  /**
   * Programmatically deselects the component
   */
  const deselectComponent = useCallback(() => {
    selectComponent(false);
  }, [selectComponent]);

  /**
   * Toggles component selection state
   */
  const toggleSelection = useCallback(() => {
    selectComponent(!isSelected);
  }, [selectComponent, isSelected]);

  /**
   * Checks if component has any active actions
   * 
   * @returns {boolean} True if any actions are available
   */
  const hasActions = useCallback(() => {
    return !!(onEdit || onDelete || onCopy || onSelect);
  }, [onEdit, onDelete, onCopy, onSelect]);

  /**
   * Gets available actions for the component
   * 
   * @returns {Array} Array of available action names
   */
  const getAvailableActions = useCallback(() => {
    const actions = [];
    
    if (onEdit) actions.push('edit');
    if (onDelete) actions.push('delete');
    if (onCopy) actions.push('copy');
    if (onSelect) actions.push('select');
    
    return actions;
  }, [onEdit, onDelete, onCopy, onSelect]);

  /**
   * Validates component data for actions
   * 
   * @returns {Object} Validation result
   */
  const validateComponentForActions = useCallback(() => {
    const errors = [];
    
    if (!componentId) {
      errors.push('Component ID is required for actions');
    }
    
    if (!componentData) {
      errors.push('Component data is required for actions');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }, [componentId, componentData]);

  return {
    // State
    isSelected,
    isHovered,
    
    // Action handlers
    handleEdit,
    handleDelete,
    handleCopy,
    handleSelect,
    handleMouseEnter,
    handleMouseLeave,
    
    // Programmatic controls
    selectComponent,
    deselectComponent,
    toggleSelection,
    setIsSelected,
    setIsHovered,
    
    // Utility functions
    hasActions,
    getAvailableActions,
    validateComponentForActions,
    
    // CSS classes for styling
    selectionClassName: isSelected ? 'selected' : '',
    hoverClassName: isHovered ? 'hovered' : '',
    
    // Accessibility props
    actionAriaLabel: `${componentData?.type || 'Component'} actions`,
    selectionAriaLabel: `${isSelected ? 'Deselect' : 'Select'} ${componentData?.type || 'component'}`,
  };
};
