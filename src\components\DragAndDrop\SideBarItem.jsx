import { memo, useMemo, useState, useRef, useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { Tooltip } from 'antd';
import {
  DragOutlined,
  FormOutlined,
  EyeOutlined,
  LayoutOutlined,
  MessageOutlined,
  MenuOutlined,
  ToolOutlined,
  ContainerOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';
import { colors } from '../../styles/theme';

// Note: Typography.Text available but not currently used

// Compact sidebar item styled like the first screenshot - user-friendly design
const AdvancedSideBarItem = styled.div`
  position: relative;
  margin: 4px 0;
  border-radius: 12px;
  background: ${colors.surface};
  border: 2px solid ${colors.borderLight};
  cursor: grab;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  user-select: none;
  min-height: 60px;
  max-height: 80px;

  /* Subtle modern effect - compact and clean */
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04);

  &:hover {
    border-color: ${colors.gray400};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
    background: ${colors.surfaceHover};

    .drag-indicator {
      opacity: 0;
      transform: translateX(8px);
    }

    .drag-label {
      opacity: 1;
      transform: translateY(-50%) translateX(0);
    }

    .component-icon {
      background: ${colors.gray100};

      .anticon {
        color: ${colors.gray600};
      }
    }

    .component-title {
      color: ${colors.textPrimary};
      font-weight: 600;
    }
  }

  &:active {
    cursor: grabbing;
    transform: translateY(-1px) scale(1.01);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16), 0 4px 16px rgba(0, 0, 0, 0.12);
  }

  &.dragging {
    opacity: 0.8;
    transform: rotate(3deg) scale(1.05);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2), 0 6px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border-color: ${colors.primary};
    background: ${colors.primaryLight};
  }
`;

const ComponentHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  position: relative;
  height: 60px;
`;

const ComponentIcon = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background: ${(props) => props.color || colors.primary}15;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: all 0.3s ease;
  flex-shrink: 0;

  .anticon {
    font-size: 12px;
    color: ${(props) => props.color || colors.primary};
    transition: all 0.3s ease;
  }
`;

const ComponentInfo = styled.div`
  flex: 1;
  min-width: 0;
  overflow: hidden;
`;

const ComponentTitle = styled.div`
  font-size: 13px;
  font-weight: 600;
  color: ${colors.textPrimary};
  margin-bottom: 1px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
`;

const ComponentType = styled.div`
  font-size: 10px;
  color: ${colors.textTertiary};
  text-transform: uppercase;
  letter-spacing: 0.3px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const DragIndicator = styled.div`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%) translateX(4px);
  opacity: 0;
  transition: all 0.2s ease;
  color: ${colors.textSecondary};
  font-size: 12px;
  flex-shrink: 0;
`;

const DragLabel = styled.div`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%) translateX(8px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: ${colors.gray700};
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: 500;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  z-index: 3;
  pointer-events: none;
`;

// Component category icons mapping
const categoryIcons = {
  'Data Entry': FormOutlined,
  'Data Display': EyeOutlined,
  Layout: LayoutOutlined,
  Feedback: MessageOutlined,
  Navigation: MenuOutlined,
  General: ToolOutlined,
  Containers: ContainerOutlined,
};

// Component category colors (available for future styling enhancements)
// const categoryColors = {
//   'Data Entry': colors.primary,
//   'Data Display': colors.info,
//   Layout: colors.textSecondary,
//   Feedback: colors.warning,
//   Navigation: colors.success,
//   General: colors.textTertiary,
//   Containers: colors.aiPrimary,
// };

// Enhanced SideBarItem component with rich visual indicators
const SideBarItem = memo(({ data, category = 'General' }) => {
  const [, setIsHovered] = useState(false); // isHovered state available for future use
  const itemRef = useRef(null);

  // Memoized drag item to prevent unnecessary re-creations
  const dragItem = useMemo(() => data, [data]);

  const [{ isDragging }, drag] = useDrag({
    type: data.type,
    item: dragItem,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  // Combine refs
  useEffect(() => {
    if (itemRef.current) {
      drag(itemRef.current);
    }
  }, [drag]);

  // Memoized component info
  const componentInfo = useMemo(() => {
    const content = data.component?.content || 'Component';
    const label = data.component?.label || content;
    const type = data.component?.type || 'element';

    return { content, label, type };
  }, [data.component]);

  // Get category icon and color
  const CategoryIcon = categoryIcons[category] || ToolOutlined;
  // Note: categoryColor available for future styling enhancements

  // Component preview text
  const previewText = useMemo(() => {
    const previews = {
      Input: 'Text input field for user data entry',
      Button: 'Interactive button for form actions',
      Select: 'Dropdown selection component',
      Checkbox: 'Multiple choice selection',
      Radio: 'Single choice selection',
      TextArea: 'Multi-line text input',
      DatePicker: 'Date selection component',
      Upload: 'File upload component',
      Card: 'Content container with styling',
      Tabs: 'Tabbed content organization',
      Divider: 'Visual content separator',
      Space: 'Layout spacing component',
    };

    return (
      previews[componentInfo.content] ||
      `${componentInfo.content} component for form building`
    );
  }, [componentInfo.content]);

  return (
    <Tooltip
      title={
        <div>
          <div style={{ fontWeight: 600, marginBottom: 4 }}>
            {componentInfo.label}
          </div>
          <div style={{ fontSize: 12, opacity: 0.8 }}>{previewText}</div>
          <div style={{ fontSize: 11, marginTop: 4, opacity: 0.6 }}>
            Category: {category} • Type: {componentInfo.type}
          </div>
        </div>
      }
      placement='right'
      mouseEnterDelay={0.5}
    >
      <AdvancedSideBarItem
        ref={itemRef}
        className={isDragging ? 'dragging' : ''}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{ opacity: isDragging ? 0.5 : 1 }}
      >
        <ComponentHeader>
          <ComponentIcon className='component-icon'>
            <CategoryIcon />
          </ComponentIcon>

          <ComponentInfo>
            <ComponentTitle className='component-title'>
              {componentInfo.label}
            </ComponentTitle>
            <ComponentType>{category}</ComponentType>
          </ComponentInfo>

          <DragIndicator className='drag-indicator'>
            <DragOutlined />
          </DragIndicator>

          <DragLabel className='drag-label'>DRAG</DragLabel>
        </ComponentHeader>
      </AdvancedSideBarItem>
    </Tooltip>
  );
});

// Set display name for debugging
SideBarItem.displayName = 'SideBarItem';

export default SideBarItem;
