/**
 * Component Default Configurations
 * 
 * Default values and configurations for different component types.
 * Provides consistent defaults across the component rendering system.
 */

/**
 * Default props for data entry components
 */
export const DATA_ENTRY_DEFAULTS = {
  input: {
    allowClear: true,
    placeholder: 'Enter text...',
    size: 'middle',
  },
  email: {
    allowClear: true,
    placeholder: 'Enter email address...',
    type: 'email',
    size: 'middle',
  },
  textarea: {
    allowClear: true,
    placeholder: 'Enter text...',
    rows: 4,
    size: 'middle',
  },
  select: {
    allowClear: true,
    placeholder: 'Please select...',
    showSearch: true,
    size: 'middle',
  },
  radio: {
    size: 'middle',
    direction: 'vertical',
  },
  checkbox: {
    size: 'middle',
  },
  inputnumber: {
    size: 'middle',
    controls: true,
    keyboard: true,
  },
  password: {
    allowClear: true,
    placeholder: 'Enter password...',
    visibilityToggle: true,
    size: 'middle',
  },
  datepicker: {
    allowClear: true,
    placeholder: 'Select date',
    size: 'middle',
  },
  rangepicker: {
    allowClear: true,
    placeholder: ['Start date', 'End date'],
    size: 'middle',
  },
  switch: {
    size: 'default',
  },
  rate: {
    count: 5,
    allowHalf: false,
    allowClear: true,
  },
  slider: {
    min: 0,
    max: 100,
    step: 1,
    included: true,
  },
  upload: {
    listType: 'text',
    multiple: false,
    showUploadList: true,
  },
};

/**
 * Default props for advanced data entry components
 */
export const ADVANCED_DATA_ENTRY_DEFAULTS = {
  autocomplete: {
    allowClear: true,
    placeholder: 'Enter text...',
    size: 'middle',
  },
  cascader: {
    allowClear: true,
    placeholder: 'Please select...',
    showSearch: true,
    size: 'middle',
  },
  colorpicker: {
    allowClear: true,
    showText: true,
    size: 'middle',
  },
  mentions: {
    allowClear: true,
    placeholder: 'Enter text...',
    rows: 4,
    size: 'middle',
  },
  timepicker: {
    allowClear: true,
    placeholder: 'Select time',
    format: 'HH:mm:ss',
    size: 'middle',
  },
  transfer: {
    showSearch: true,
    showSelectAll: true,
    oneWay: false,
  },
  treeselect: {
    allowClear: true,
    placeholder: 'Please select...',
    showSearch: true,
    size: 'middle',
  },
};

/**
 * Default props for display components
 */
export const DISPLAY_DEFAULTS = {
  avatar: {
    size: 'default',
    shape: 'circle',
  },
  badge: {
    showZero: false,
    overflowCount: 99,
  },
  image: {
    preview: true,
  },
  tag: {
    closable: false,
    bordered: true,
  },
  button: {
    type: 'default',
    size: 'middle',
    shape: 'default',
    block: false,
    danger: false,
    ghost: false,
    loading: false,
  },
  typography: {
    level: 1,
    type: 'default',
    disabled: false,
    mark: false,
    code: false,
    keyboard: false,
    underline: false,
    delete: false,
    strong: false,
    italic: false,
  },
  statistic: {
    precision: 0,
  },
};

/**
 * Default props for data display components
 */
export const DATA_DISPLAY_DEFAULTS = {
  table: {
    bordered: false,
    size: 'default',
    pagination: true,
  },
  list: {
    bordered: false,
    split: true,
    size: 'default',
    itemLayout: 'horizontal',
  },
  calendar: {
    fullscreen: true,
    mode: 'month',
  },
  carousel: {
    autoplay: false,
    dots: true,
    infinite: true,
    speed: 500,
  },
  descriptions: {
    bordered: false,
    column: 3,
    size: 'default',
    layout: 'horizontal',
  },
  empty: {
    image: 'default',
  },
  timeline: {
    mode: 'left',
    pending: false,
    reverse: false,
  },
  tree: {
    showLine: false,
    showIcon: false,
    checkable: false,
    selectable: true,
    multiple: false,
  },
};

/**
 * Default props for navigation components
 */
export const NAVIGATION_DEFAULTS = {
  breadcrumb: {
    separator: '/',
  },
  menu: {
    mode: 'vertical',
    theme: 'light',
  },
  pagination: {
    current: 1,
    total: 0,
    pageSize: 10,
    size: 'default',
    showSizeChanger: false,
    showQuickJumper: false,
    showTotal: false,
  },
  steps: {
    current: 0,
    type: 'default',
    size: 'default',
    direction: 'horizontal',
    labelPlacement: 'horizontal',
  },
};

/**
 * Default props for layout components
 */
export const LAYOUT_DEFAULTS = {
  divider: {
    type: 'horizontal',
    orientation: 'center',
    dashed: false,
    plain: false,
  },
  space: {
    direction: 'horizontal',
    size: 'small',
    align: 'center',
    wrap: false,
  },
};

/**
 * Default props for feedback components
 */
export const FEEDBACK_DEFAULTS = {
  alert: {
    type: 'info',
    showIcon: true,
    closable: false,
    banner: false,
  },
  progress: {
    type: 'line',
    size: 'default',
    showInfo: true,
    status: 'normal',
    percent: 0,
  },
  skeleton: {
    active: true,
    avatar: false,
    paragraph: true,
    title: true,
    loading: true,
  },
  spin: {
    size: 'default',
    spinning: true,
  },
};

/**
 * Default props for container components
 */
export const CONTAINER_DEFAULTS = {
  TAB_CONTAINER: {
    type: 'line',
    size: 'default',
    tabPosition: 'top',
  },
  CARD_CONTAINER: {
    size: 'default',
    bordered: true,
    hoverable: false,
  },
  FORM_SECTION: {
    bordered: true,
    collapsible: false,
    defaultCollapsed: false,
  },
  ACCORDION_CONTAINER: {
    bordered: true,
    ghost: false,
  },
  STEPS_CONTAINER: {
    current: 0,
    direction: 'horizontal',
    size: 'default',
  },
  GRID_CONTAINER: {
    columns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: '16px',
  },
  FLEX_CONTAINER: {
    direction: 'row',
    justify: 'flex-start',
    align: 'stretch',
    wrap: 'nowrap',
    gap: '16px',
  },
};

/**
 * Gets default props for a specific component type
 * 
 * @param {string} componentType - Type of component
 * @returns {Object} Default props for the component type
 */
export const getComponentDefaults = (componentType) => {
  // Check each category for the component type
  if (DATA_ENTRY_DEFAULTS[componentType]) {
    return DATA_ENTRY_DEFAULTS[componentType];
  }
  
  if (ADVANCED_DATA_ENTRY_DEFAULTS[componentType]) {
    return ADVANCED_DATA_ENTRY_DEFAULTS[componentType];
  }
  
  if (DISPLAY_DEFAULTS[componentType]) {
    return DISPLAY_DEFAULTS[componentType];
  }
  
  if (DATA_DISPLAY_DEFAULTS[componentType]) {
    return DATA_DISPLAY_DEFAULTS[componentType];
  }
  
  if (NAVIGATION_DEFAULTS[componentType]) {
    return NAVIGATION_DEFAULTS[componentType];
  }
  
  if (LAYOUT_DEFAULTS[componentType]) {
    return LAYOUT_DEFAULTS[componentType];
  }
  
  if (FEEDBACK_DEFAULTS[componentType]) {
    return FEEDBACK_DEFAULTS[componentType];
  }
  
  if (CONTAINER_DEFAULTS[componentType]) {
    return CONTAINER_DEFAULTS[componentType];
  }
  
  return {};
};

/**
 * Merges component configuration with defaults
 * 
 * @param {Object} component - Component configuration
 * @returns {Object} Component with merged defaults
 */
export const mergeWithDefaults = (component) => {
  if (!component || !component.type) {
    return component;
  }
  
  const defaults = getComponentDefaults(component.type);
  
  return {
    ...component,
    styling: {
      ...defaults,
      ...component.styling,
    },
    componentProps: {
      ...defaults,
      ...component.componentProps,
    },
  };
};
