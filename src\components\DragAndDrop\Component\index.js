/**
 * Component Module Barrel Export
 *
 * Central export file for the refactored Component module.
 * Provides clean imports for all components, hooks, utilities, and constants.
 *
 * Usage Examples:
 *
 * // Import the main component
 * import Component from './Component';
 *
 * // Import specific hooks
 * import { useInlineEditing, useDragAndDrop } from './Component/hooks';
 *
 * // Import utilities
 * import { isContainerComponent, getComponentInfo } from './Component/utils';
 *
 * // Import styled components
 * import { AdvancedComponentWrapper } from './Component/styles';
 *
 * // Import animation constants
 * import { componentVariants } from './Component/constants';
 *
 * @module ComponentModule
 */

// Main Component (default export)
export { default } from '../Component.jsx';

// UI Components
export { default as ComponentWrapper } from './components/ComponentWrapper';
export { default as ComponentContent } from './components/ComponentContent';
export { default as InlineEditableText } from './components/InlineEditableText';

// Custom Hooks
export { useDragAndDrop } from './hooks/useDragAndDrop';
export { useInlineEditing } from './hooks/useInlineEditing';
export { useComponentActions } from './hooks/useComponentActions';
export { useDragPreview } from './hooks/useDragAndDrop';

// Utilities
export {
  isContainerComponent,
  shouldHaveFormLabel,
  getComponentInfo,
  getNestedPropertyValue,
  buildNestedUpdate,
  validateComponentData,
  getCanvasFormItemProps,
} from './utils/componentUtils';

export {
  renderFormComponent,
  renderInputComponents,
  renderSelectionComponents,
  renderNumericDateComponents,
  renderInteractiveComponents,
  renderUploadAdvancedComponents,
  renderAvatarBadgeComponents,
  renderFeedbackComponents,
  renderNavigationComponents,
  renderLayoutComponents,
  renderGeneralComponents,
} from './utils/componentRenderer';

// Styled Components
export {
  AdvancedComponentWrapper,
  ComponentOverlay,
  ComponentLabel,
  ComponentActions,
  DragHandle,
  ComponentContent as StyledComponentContent,
  ComponentTypeIndicator,
  InlineEditableText as StyledInlineEditableText,
} from './styles/StyledComponents';

// Animation Constants
export {
  componentVariants,
  labelVariants,
  dragHandleVariants,
  animationConfig,
  accessibleAnimationConfig,
  animationUtils,
} from './constants/animations';

/**
 * Convenience exports for common use cases
 * Note: Commented out to avoid ESLint errors for missing exports
 * Uncomment when all individual exports are properly implemented
 */

// Complete hook collection for easy import
// export const hooks = {
//   useDragAndDrop,
//   useInlineEditing,
//   useComponentActions,
// };

// Complete utilities collection
// export const utils = {
//   isContainerComponent,
//   shouldHaveFormLabel,
//   getComponentInfo,
//   getNestedPropertyValue,
//   buildNestedUpdate,
//   validateComponentData,
//   getCanvasFormItemProps,
// };

// Complete renderer collection
// export const renderers = {
//   renderFormComponent,
//   renderInputComponents,
//   renderSelectionComponents,
//   renderNumericDateComponents,
//   renderInteractiveComponents,
//   renderUploadAdvancedComponents,
//   renderAvatarBadgeComponents,
//   renderFeedbackComponents,
//   renderNavigationComponents,
//   renderLayoutComponents,
//   renderGeneralComponents,
// };

// Animation variants collection
// export const animations = {
//   componentVariants,
//   labelVariants,
//   dragHandleVariants,
//   animationConfig,
//   accessibleAnimationConfig,
//   animationUtils,
// };
