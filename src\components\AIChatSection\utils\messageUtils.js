/**
 * Message Utilities
 * 
 * Utility functions for handling chat messages, formatting, and processing.
 * Provides helper functions for message creation, validation, and manipulation.
 * 
 * Features:
 * - Message creation and validation utilities
 * - Text formatting and processing helpers
 * - Message type detection and classification
 * - Timestamp and ID generation utilities
 * - Message filtering and sorting functions
 */

/**
 * Generates a unique message ID
 * @returns {number} Unique timestamp-based ID
 */
export const generateMessageId = () => {
  return Date.now() + Math.random() * 1000;
};

/**
 * Creates a standardized message object
 * @param {Object} options - Message options
 * @param {string} options.text - Message text content
 * @param {boolean} options.isUser - Whether message is from user
 * @param {string} options.type - Message type (welcome, error, form-generated, etc.)
 * @param {Object} options.metadata - Additional message metadata
 * @returns {Object} Standardized message object
 */
export const createMessage = ({
  text,
  isUser = false,
  type = 'default',
  metadata = {},
}) => {
  return {
    id: generateMessageId(),
    text: text.trim(),
    isUser,
    type,
    timestamp: new Date(),
    ...metadata,
  };
};

/**
 * Creates a user message object
 * @param {string} text - Message text
 * @param {Object} metadata - Additional metadata
 * @returns {Object} User message object
 */
export const createUserMessage = (text, metadata = {}) => {
  return createMessage({
    text,
    isUser: true,
    type: 'user',
    ...metadata,
  });
};

/**
 * Creates an AI response message object
 * @param {string} text - Response text
 * @param {Object} options - Additional options
 * @returns {Object} AI message object
 */
export const createAIMessage = (text, options = {}) => {
  return createMessage({
    text,
    isUser: false,
    type: 'ai',
    ...options,
  });
};

/**
 * Creates a form generation success message
 * @param {Object} formData - Generated form data
 * @param {string} complexity - Form complexity level
 * @param {Array} suggestedComponents - Suggested components
 * @returns {Object} Form generation message object
 */
export const createFormGeneratedMessage = (formData, complexity, suggestedComponents = []) => {
  const componentCount = formData.components ? Object.keys(formData.components).length : 0;
  const layoutCount = formData.layout ? formData.layout.length : 0;

  const text = `✅ I've generated and applied a ${complexity.toLowerCase()} form to your builder!

🎯 **The form is now live in your Builder and Preview tabs!**

**Form Details:**
• **Components:** ${componentCount}
• **Layout:** ${layoutCount} sections
• **Complexity:** ${complexity}

You can immediately start editing with drag-and-drop or test the form functionality.`;

  return createMessage({
    text,
    isUser: false,
    type: 'form-generated',
    schema: formData,
    complexity,
    suggestedComponents,
    autoApplied: true,
    metadata: {
      componentCount,
      layoutCount,
      generatedAt: new Date(),
    },
  });
};

/**
 * Creates an error message object
 * @param {string} errorMessage - Error message text
 * @param {Error} error - Original error object
 * @returns {Object} Error message object
 */
export const createErrorMessage = (errorMessage, error = null) => {
  return createMessage({
    text: `❌ Sorry, I encountered an error: ${errorMessage}. Please try rephrasing your request or check your connection.`,
    isUser: false,
    type: 'error',
    error: error ? {
      message: error.message,
      stack: error.stack,
      timestamp: new Date(),
    } : null,
  });
};

/**
 * Validates a message object
 * @param {Object} message - Message to validate
 * @returns {boolean} Whether message is valid
 */
export const isValidMessage = (message) => {
  if (!message || typeof message !== 'object') {
    return false;
  }

  // Required fields
  if (!message.id || !message.text || typeof message.isUser !== 'boolean') {
    return false;
  }

  // Text should not be empty after trimming
  if (!message.text.trim()) {
    return false;
  }

  // Timestamp should be a valid date
  if (!message.timestamp || !(message.timestamp instanceof Date)) {
    return false;
  }

  return true;
};

/**
 * Filters messages by type
 * @param {Array} messages - Array of messages
 * @param {string} type - Message type to filter by
 * @returns {Array} Filtered messages
 */
export const filterMessagesByType = (messages, type) => {
  return messages.filter(message => message.type === type);
};

/**
 * Gets the last message of a specific type
 * @param {Array} messages - Array of messages
 * @param {string} type - Message type
 * @returns {Object|null} Last message of specified type or null
 */
export const getLastMessageByType = (messages, type) => {
  const filtered = filterMessagesByType(messages, type);
  return filtered.length > 0 ? filtered[filtered.length - 1] : null;
};

/**
 * Sorts messages by timestamp
 * @param {Array} messages - Array of messages
 * @param {string} order - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted messages
 */
export const sortMessagesByTimestamp = (messages, order = 'asc') => {
  return [...messages].sort((a, b) => {
    const timeA = a.timestamp.getTime();
    const timeB = b.timestamp.getTime();
    return order === 'asc' ? timeA - timeB : timeB - timeA;
  });
};

/**
 * Formats message timestamp for display
 * @param {Date} timestamp - Message timestamp
 * @param {Object} options - Formatting options
 * @returns {string} Formatted timestamp
 */
export const formatMessageTimestamp = (timestamp, options = {}) => {
  const {
    includeDate = false,
    includeSeconds = false,
    format12Hour = true,
  } = options;

  const now = new Date();
  const isToday = timestamp.toDateString() === now.toDateString();

  let formatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: format12Hour,
  };

  if (includeSeconds) {
    formatOptions.second = '2-digit';
  }

  if (includeDate || !isToday) {
    formatOptions.month = 'short';
    formatOptions.day = 'numeric';
  }

  return timestamp.toLocaleString('en-US', formatOptions);
};

/**
 * Truncates message text to specified length
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @param {string} suffix - Suffix to add when truncated
 * @returns {string} Truncated text
 */
export const truncateMessageText = (text, maxLength = 100, suffix = '...') => {
  if (text.length <= maxLength) {
    return text;
  }

  return text.substring(0, maxLength - suffix.length) + suffix;
};

/**
 * Extracts mentions from message text
 * @param {string} text - Message text
 * @returns {Array} Array of mentioned usernames
 */
export const extractMentions = (text) => {
  const mentionRegex = /@(\w+)/g;
  const mentions = [];
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1]);
  }

  return mentions;
};

/**
 * Counts words in message text
 * @param {string} text - Message text
 * @returns {number} Word count
 */
export const countWords = (text) => {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
};

/**
 * Estimates reading time for message text
 * @param {string} text - Message text
 * @param {number} wordsPerMinute - Reading speed (default: 200 WPM)
 * @returns {number} Estimated reading time in seconds
 */
export const estimateReadingTime = (text, wordsPerMinute = 200) => {
  const wordCount = countWords(text);
  const minutes = wordCount / wordsPerMinute;
  return Math.max(1, Math.ceil(minutes * 60)); // At least 1 second
};
