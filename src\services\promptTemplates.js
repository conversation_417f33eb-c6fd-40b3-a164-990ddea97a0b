/**
 * Universal AI Prompt Templates for Form Generation
 * Comprehensive prompt system that handles all form complexities
 */

// Form complexity levels and their characteristics
export const FORM_COMPLEXITY_LEVELS = {
  SIMPLE: {
    name: 'Simple Form',
    description: 'Basic forms with 3-8 fields, single column layout',
    examples: ['Contact form', 'Newsletter signup', 'Feedback form'],
    maxFields: 8,
    layoutComplexity: 'single-column',
    components: ['input', 'textarea', 'select', 'button'],
  },
  INTERMEDIATE: {
    name: 'Intermediate Form',
    description: 'Multi-section forms with 8-20 fields, some grouping',
    examples: ['Registration form', 'Survey form', 'Application form'],
    maxFields: 20,
    layoutComplexity: 'multi-section',
    components: [
      'input',
      'select',
      'radio',
      'checkbox',
      'datePicker',
      'upload',
    ],
  },
  COMPLEX: {
    name: 'Complex Form',
    description:
      'Advanced forms with 20+ fields, multiple sections, conditional logic',
    examples: [
      'Employee onboarding',
      'Insurance application',
      'Loan application',
    ],
    maxFields: 50,
    layoutComplexity: 'tabbed-sections',
    components: ['all-basic', 'cascader', 'treeSelect', 'transfer'],
  },
  ENTERPRISE: {
    name: 'Enterprise Form',
    description: 'Multi-step wizards with unlimited complexity, nested layouts',
    examples: ['Supplier onboarding', 'ERP configuration', 'Compliance forms'],
    maxFields: 'unlimited',
    layoutComplexity: 'multi-step-wizard',
    components: ['all-components', 'nested-containers', 'dynamic-sections'],
  },
};

// Comprehensive component library with exact Ant Design specifications
export const COMPONENT_LIBRARY = {
  // Input Components
  INPUT: {
    type: 'input',
    antdComponent: 'Input',
    props: [
      'placeholder',
      'size',
      'disabled',
      'maxLength',
      'showCount',
      'allowClear',
    ],
    validation: ['required', 'minLength', 'maxLength', 'pattern'],
    styling: ['size', 'variant', 'status'],
  },
  TEXTAREA: {
    type: 'textarea',
    antdComponent: 'Input.TextArea',
    props: ['placeholder', 'rows', 'autoSize', 'maxLength', 'showCount'],
    validation: ['required', 'minLength', 'maxLength'],
    styling: ['size', 'variant', 'status'],
  },
  PASSWORD: {
    type: 'password',
    antdComponent: 'Input.Password',
    props: ['placeholder', 'visibilityToggle', 'iconRender'],
    validation: ['required', 'minLength', 'pattern'],
    styling: ['size', 'variant', 'status'],
  },

  // Selection Components
  SELECT: {
    type: 'select',
    antdComponent: 'Select',
    props: ['placeholder', 'mode', 'allowClear', 'showSearch', 'options'],
    validation: ['required'],
    styling: ['size', 'variant', 'status'],
  },
  RADIO: {
    type: 'radio',
    antdComponent: 'Radio.Group',
    props: ['options', 'optionType', 'buttonStyle', 'size'],
    validation: ['required'],
    styling: ['size', 'variant'],
  },
  CHECKBOX: {
    type: 'checkbox',
    antdComponent: 'Checkbox.Group',
    props: ['options', 'disabled'],
    validation: ['required', 'min', 'max'],
    styling: ['size'],
  },

  // Date/Time Components
  DATE_PICKER: {
    type: 'datePicker',
    antdComponent: 'DatePicker',
    props: ['format', 'picker', 'showTime', 'disabledDate'],
    validation: ['required'],
    styling: ['size', 'variant', 'status'],
  },
  TIME_PICKER: {
    type: 'timePicker',
    antdComponent: 'TimePicker',
    props: ['format', 'hourStep', 'minuteStep', 'secondStep'],
    validation: ['required'],
    styling: ['size', 'variant', 'status'],
  },

  // Advanced Components
  CASCADER: {
    type: 'cascader',
    antdComponent: 'Cascader',
    props: ['options', 'placeholder', 'expandTrigger', 'multiple'],
    validation: ['required'],
    styling: ['size', 'variant', 'status'],
  },
  TREE_SELECT: {
    type: 'treeSelect',
    antdComponent: 'TreeSelect',
    props: ['treeData', 'placeholder', 'multiple', 'treeCheckable'],
    validation: ['required'],
    styling: ['size', 'variant', 'status'],
  },

  // Upload Components
  UPLOAD: {
    type: 'upload',
    antdComponent: 'Upload',
    props: ['accept', 'multiple', 'maxCount', 'listType', 'beforeUpload'],
    validation: ['required', 'fileSize', 'fileType'],
    styling: ['listType'],
  },

  // Layout Components
  TAB_CONTAINER: {
    type: 'tabContainer',
    antdComponent: 'Tabs',
    props: ['type', 'size', 'tabPosition', 'animated'],
    children: 'tabs',
    styling: ['type', 'size'],
  },
  CARD_CONTAINER: {
    type: 'cardContainer',
    antdComponent: 'Card',
    props: ['title', 'bordered', 'hoverable', 'size'],
    children: 'components',
    styling: ['size', 'bordered'],
  },
  STEPS_CONTAINER: {
    type: 'stepsContainer',
    antdComponent: 'Steps',
    props: ['current', 'direction', 'size', 'status', 'type'],
    children: 'steps',
    styling: ['size', 'direction'],
  },
};

// Pre-built form templates for common scenarios
export const FORM_TEMPLATES = {
  CONTACT_FORM: {
    prompt:
      'Create a professional contact form with name, email, phone, subject, and message fields',
    complexity: 'SIMPLE',
    expectedFields: ['name', 'email', 'phone', 'subject', 'message'],
    layout: 'single-column',
  },

  REGISTRATION_FORM: {
    prompt:
      'Create a user registration form with personal details, account information, and preferences',
    complexity: 'INTERMEDIATE',
    expectedFields: [
      'firstName',
      'lastName',
      'email',
      'password',
      'confirmPassword',
      'dateOfBirth',
      'gender',
      'country',
      'terms',
    ],
    layout: 'multi-section',
  },

  SUPPLIER_ONBOARDING: {
    prompt:
      'Create a comprehensive 5-step supplier onboarding form with company info, contacts, financial details, compliance, and review',
    complexity: 'ENTERPRISE',
    expectedSteps: [
      'Company Information',
      'Contact Details',
      'Financial Information',
      'Compliance & Certifications',
      'Review & Submit',
    ],
    layout: 'multi-step-wizard',
  },

  EMPLOYEE_ONBOARDING: {
    prompt:
      'Create a multi-step employee onboarding form with personal info, employment details, benefits, and documentation',
    complexity: 'ENTERPRISE',
    expectedSteps: [
      'Personal Information',
      'Employment Details',
      'Benefits Selection',
      'Documentation Upload',
      'Final Review',
    ],
    layout: 'multi-step-wizard',
  },
};

// Removed unused enhancePromptWithContext function

// Detect form complexity from user prompt
export const detectComplexity = (prompt) => {
  const lowerPrompt = prompt.toLowerCase();

  // Enterprise indicators
  if (
    lowerPrompt.includes('multi-step') ||
    lowerPrompt.includes('wizard') ||
    lowerPrompt.includes('onboarding') ||
    lowerPrompt.includes('enterprise') ||
    lowerPrompt.includes('complex')
  ) {
    return 'ENTERPRISE';
  }

  // Complex indicators
  if (
    lowerPrompt.includes('application') ||
    lowerPrompt.includes('registration') ||
    lowerPrompt.includes('survey') ||
    lowerPrompt.includes('detailed')
  ) {
    return 'COMPLEX';
  }

  // Intermediate indicators
  if (
    lowerPrompt.includes('form') &&
    (lowerPrompt.includes('multiple') ||
      lowerPrompt.includes('section') ||
      lowerPrompt.includes('group'))
  ) {
    return 'INTERMEDIATE';
  }

  // Default to simple
  return 'SIMPLE';
};

// Generate component suggestions based on prompt
export const suggestComponents = (prompt) => {
  const lowerPrompt = prompt.toLowerCase();
  const suggestions = [];

  // Basic input detection
  if (lowerPrompt.includes('name') || lowerPrompt.includes('title')) {
    suggestions.push('input');
  }

  if (lowerPrompt.includes('email')) {
    suggestions.push('input');
  }

  if (lowerPrompt.includes('phone') || lowerPrompt.includes('mobile')) {
    suggestions.push('input');
  }

  if (
    lowerPrompt.includes('message') ||
    lowerPrompt.includes('comment') ||
    lowerPrompt.includes('description')
  ) {
    suggestions.push('textarea');
  }

  if (lowerPrompt.includes('password')) {
    suggestions.push('password');
  }

  // Selection components
  if (
    lowerPrompt.includes('select') ||
    lowerPrompt.includes('choose') ||
    lowerPrompt.includes('dropdown')
  ) {
    suggestions.push('select');
  }

  if (lowerPrompt.includes('radio') || lowerPrompt.includes('option')) {
    suggestions.push('radio');
  }

  if (
    lowerPrompt.includes('checkbox') ||
    lowerPrompt.includes('multiple choice')
  ) {
    suggestions.push('checkbox');
  }

  // Date/time components
  if (
    lowerPrompt.includes('date') ||
    lowerPrompt.includes('birthday') ||
    lowerPrompt.includes('birth')
  ) {
    suggestions.push('datePicker');
  }

  if (lowerPrompt.includes('time')) {
    suggestions.push('timePicker');
  }

  // Upload components
  if (
    lowerPrompt.includes('upload') ||
    lowerPrompt.includes('file') ||
    lowerPrompt.includes('document')
  ) {
    suggestions.push('upload');
  }

  // Advanced components
  if (lowerPrompt.includes('tree') || lowerPrompt.includes('hierarchy')) {
    suggestions.push('treeSelect');
  }

  if (lowerPrompt.includes('cascade') || lowerPrompt.includes('dependent')) {
    suggestions.push('cascader');
  }

  // Layout components
  if (lowerPrompt.includes('tab') || lowerPrompt.includes('section')) {
    suggestions.push('tabContainer');
  }

  if (lowerPrompt.includes('card') || lowerPrompt.includes('group')) {
    suggestions.push('cardContainer');
  }

  if (
    lowerPrompt.includes('step') ||
    lowerPrompt.includes('wizard') ||
    lowerPrompt.includes('multi-step')
  ) {
    suggestions.push('stepsContainer');
  }

  return [...new Set(suggestions)]; // Remove duplicates
};

// Validation rule templates
export const VALIDATION_TEMPLATES = {
  EMAIL: {
    type: 'email',
    message: 'Please enter a valid email address',
  },
  PHONE: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    message: 'Please enter a valid phone number',
  },
  PASSWORD: {
    min: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    message:
      'Password must be at least 8 characters with uppercase, lowercase, and number',
  },
  REQUIRED: {
    required: true,
    message: 'This field is required',
  },
  URL: {
    type: 'url',
    message: 'Please enter a valid URL',
  },
};

// Export all templates and utilities
export default {
  FORM_COMPLEXITY_LEVELS,
  COMPONENT_LIBRARY,
  FORM_TEMPLATES,
  detectComplexity,
  suggestComponents,
  VALIDATION_TEMPLATES,
};
