import styled from 'styled-components';
import { colors, elevation, motionCurves } from '../theme';

// Enhanced Component styling with advanced visual indicators (Legacy compatibility)
// Note: This is kept for backward compatibility. New components use AdvancedComponentWrapper.
export const Component = styled.div`
  position: relative;
  margin: 2px 0;
  border-radius: 8px;
  background: ${colors.surface};
  border: 1px solid transparent;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-height: 80px;
  padding: 12px;
  cursor: move;
  user-select: none;
  font-family: 'Segoe UI', sans-serif;

  /* Clean default state - no shadow */
  backdrop-filter: blur(5px);

  /* Focused hover effects with shadow */
  &:hover:not(.dragging) {
    border-color: rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 4px rgba(0, 0, 0, 0.06);
    background: ${colors.surfaceHover};
  }

  &:active:not(.dragging) {
    transform: translateY(0);
    transition: all 0.1s ${motionCurves.accelerate};
  }

  /* Enhanced dragging state */
  &.dragging {
    opacity: 0.8;
    transform: rotate(2deg) scale(1.03);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16), 0 4px 16px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    border-color: ${colors.primary};
    background: ${colors.surface};
    transition: none;
  }

  /* Container component styling */
  &.container-component {
    border-style: dashed;
    border-color: ${colors.aiPrimary}40;
    background: linear-gradient(
      135deg,
      ${colors.aiPrimary}05 0%,
      ${colors.surface} 100%
    );
  }

  /* Selected state */
  &.selected {
    border-color: ${colors.primary};
    box-shadow: 0 0 0 3px ${colors.primary}20;
  }

  .ant-form-item {
    margin-bottom: 16px;
    pointer-events: none;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-form-item-label {
    padding-bottom: 8px;

    > label {
      font-size: 14px;
      font-weight: 400;
      color: ${colors.textPrimary};
      line-height: 1.4;
      font-family: 'Segoe UI', sans-serif;
    }
  }

  .ant-input,
  .ant-select-selector,
  .ant-input-affix-wrapper,
  .ant-input-number,
  .ant-picker {
    border-radius: 4px;
    border-color: ${colors.border};
    transition: all 0.15s ${motionCurves.decelerate};
    pointer-events: none;
    font-family: 'Segoe UI', sans-serif;

    &:hover {
      border-color: ${colors.borderHover};
    }

    &:focus,
    &.ant-select-focused .ant-select-selector,
    &.ant-picker-focused {
      border-color: ${colors.primary};
      box-shadow: 0 0 0 2px ${colors.primaryLight};
    }
  }

  .ant-radio-group,
  .ant-checkbox-group {
    pointer-events: none;

    .ant-radio-wrapper,
    .ant-checkbox-wrapper {
      margin-bottom: 8px;
      font-size: 14px;
      color: ${colors.textPrimary};
      font-family: 'Segoe UI', sans-serif;
    }
  }

  /* Re-enable interactions when not dragging */
  &:not(.dragging) {
    .ant-form-item,
    .ant-input,
    .ant-select-selector,
    .ant-input-affix-wrapper,
    .ant-input-number,
    .ant-picker,
    .ant-radio-group,
    .ant-checkbox-group {
      pointer-events: auto;
    }
  }

  /* Microsoft Fluent Design focus ring */
  &:focus {
    outline: 2px solid ${colors.accent};
    outline-offset: 2px;
  }
`;
