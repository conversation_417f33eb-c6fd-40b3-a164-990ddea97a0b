{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6", "es2017", "es2018", "es2019", "es2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "baseUrl": "src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/hooks/*": ["hooks/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/constants/*": ["constants/*"], "@/styles/*": ["styles/*"]}}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "ts-node": {"compilerOptions": {"module": "CommonJS"}}}