/**
 * Form Helper Utilities
 * 
 * General utility functions for form handling, processing, and manipulation.
 * These functions provide common form operations used throughout the FormRenderer.
 */

/**
 * Checks if a value is empty or null
 * 
 * @param {*} value - The value to check
 * @returns {boolean} True if the value is empty, null, or undefined
 */
export const isEmpty = (value) => {
  return value === null || value === undefined || value === '';
};

/**
 * Safely gets a nested property from an object
 * 
 * @param {Object} obj - The object to get the property from
 * @param {string} path - The dot-notation path to the property
 * @param {*} defaultValue - Default value if property doesn't exist
 * @returns {*} The property value or default value
 */
export const getNestedProperty = (obj, path, defaultValue = undefined) => {
  if (!obj || !path) return defaultValue;
  
  const keys = path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result === null || result === undefined || !(key in result)) {
      return defaultValue;
    }
    result = result[key];
  }
  
  return result;
};

/**
 * Merges form configuration objects with proper precedence
 * 
 * @param {Object} defaultConfig - Default configuration object
 * @param {Object} userConfig - User-provided configuration object
 * @returns {Object} Merged configuration object
 */
export const mergeFormConfig = (defaultConfig = {}, userConfig = {}) => {
  return {
    ...defaultConfig,
    ...userConfig,
    // Handle nested objects specially
    style: {
      ...defaultConfig.style,
      ...userConfig.style,
    },
  };
};

/**
 * Validates form schema structure
 * 
 * @param {Object} schema - Form schema to validate
 * @returns {Object} Validation result with isValid flag and errors array
 */
export const validateFormSchema = (schema) => {
  const errors = [];
  
  if (!schema) {
    errors.push('Schema is required');
    return { isValid: false, errors };
  }
  
  if (!schema.layout || !Array.isArray(schema.layout)) {
    errors.push('Schema must have a layout array');
  }
  
  if (!schema.components || typeof schema.components !== 'object') {
    errors.push('Schema must have a components object');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Extracts form field names from layout structure
 * 
 * @param {Array} layout - Form layout array
 * @param {Object} components - Components object
 * @returns {Array} Array of field names
 */
export const extractFieldNames = (layout = [], components = {}) => {
  const fieldNames = [];
  
  const processLayoutItem = (item) => {
    if (item.type === 'component' && components[item.id]) {
      const component = components[item.id];
      if (component.name || component.id) {
        fieldNames.push(component.name || component.id);
      }
    }
    
    if (item.children && Array.isArray(item.children)) {
      item.children.forEach(processLayoutItem);
    }
  };
  
  layout.forEach(processLayoutItem);
  return fieldNames;
};
