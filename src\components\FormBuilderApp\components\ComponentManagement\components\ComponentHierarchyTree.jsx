/**
 * @fileoverview Component Hierarchy Tree component
 *
 * This component provides a visual tree representation of form components
 * with drag-and-drop reordering, selection, and management capabilities.
 *
 * @module ComponentHierarchyTree
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Tree, Input, Button, Space, Tooltip, Badge, Dropdown } from 'antd';
import {
  SearchOutlined,
  MoreOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  DeleteOutlined,
  CopyOutlined,
  EditOutlined,
  FolderOutlined,
  FileOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import {
  TREE_NODE_TYPES,
  COMPONENT_CATEGORIES,
  MANAGEMENT_ANIMATIONS,
} from '../constants/managementConstants';

const { Search } = Input;

/**
 * Styled components
 */
const TreeContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;

  .ant-tree {
    background: transparent;

    .ant-tree-node-content-wrapper {
      padding: 4px 8px;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(24, 144, 255, 0.06);
      }

      &.ant-tree-node-selected {
        background: rgba(24, 144, 255, 0.12);
        border: 1px solid rgba(24, 144, 255, 0.3);
      }
    }

    .ant-tree-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
  }
`;

const TreeHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);

  .header-title {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

const TreeContent = styled.div`
  flex: 1;
  padding: 12px;
  overflow-y: auto;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
`;

const NodeTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;

  .node-icon {
    font-size: 14px;
    color: ${(props) => props.iconColor || '#666'};
  }

  .node-label {
    font-size: 13px;
    color: #262626;
    flex: 1;
  }

  .node-meta {
    display: flex;
    align-items: center;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover .node-meta {
    opacity: 1;
  }
`;

const NodeActions = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

/**
 * Component Hierarchy Tree component
 *
 * @param {Object} props - Component props
 * @param {Array} props.components - Array of form components
 * @param {string} props.selectedComponentId - Currently selected component ID
 * @param {Function} props.onComponentSelect - Component selection callback
 * @param {Function} props.onComponentUpdate - Component update callback
 * @param {Function} props.onComponentDelete - Component delete callback
 * @param {Function} props.onComponentDuplicate - Component duplicate callback
 * @param {boolean} props.showSearch - Whether to show search functionality
 * @returns {React.ReactNode} Component hierarchy tree JSX
 */
const ComponentHierarchyTree = ({
  components = [],
  selectedComponentId,
  onComponentSelect,
  onComponentUpdate,
  onComponentDelete,
  onComponentDuplicate,
  showSearch = true,
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState(['root']);

  /**
   * Get appropriate icon for node type
   */
  const getNodeIcon = useCallback((component, nodeType) => {
    if (component.type === 'root') {
      return <FolderOutlined style={{ color: TREE_NODE_TYPES.ROOT.color }} />;
    }

    if (component.children && component.children.length > 0) {
      return (
        <FolderOutlined style={{ color: TREE_NODE_TYPES.CONTAINER.color }} />
      );
    }

    return (
      <AppstoreOutlined style={{ color: TREE_NODE_TYPES.COMPONENT.color }} />
    );
  }, []);

  /**
   * Render node title with actions
   */
  const renderNodeTitle = useCallback(
    (component, category, nodeType) => {
      const isVisible = component.visible !== false;
      const hasErrors = component.errors && component.errors.length > 0;

      const actionItems = [
        {
          key: 'edit',
          label: 'Edit Component',
          icon: <EditOutlined />,
          onClick: () => onComponentSelect?.(component.id),
        },
        {
          key: 'duplicate',
          label: 'Duplicate',
          icon: <CopyOutlined />,
          onClick: () => onComponentDuplicate?.(component.id),
        },
        {
          key: 'toggle-visibility',
          label: isVisible ? 'Hide' : 'Show',
          icon: isVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />,
          onClick: () =>
            onComponentUpdate?.(component.id, { visible: !isVisible }),
        },
        {
          type: 'divider',
        },
        {
          key: 'delete',
          label: 'Delete',
          icon: <DeleteOutlined />,
          danger: true,
          onClick: () => onComponentDelete?.(component.id),
        },
      ];

      return (
        <NodeTitle iconColor={category?.color || nodeType.color}>
          <span className='node-icon'>{getNodeIcon(component, nodeType)}</span>
          <span className='node-label'>
            {component.label || component.type || 'Unnamed Component'}
          </span>
          <div className='node-meta'>
            {hasErrors && (
              <Tooltip title={`${component.errors.length} validation errors`}>
                <Badge count={component.errors.length} size='small' />
              </Tooltip>
            )}
            {!isVisible && (
              <Tooltip title='Component is hidden'>
                <EyeInvisibleOutlined
                  style={{ color: '#bfbfbf', fontSize: '12px' }}
                />
              </Tooltip>
            )}
            {component.type !== 'root' && (
              <Dropdown
                menu={{ items: actionItems }}
                trigger={['click']}
                placement='bottomRight'
              >
                <Button
                  type='text'
                  size='small'
                  icon={<MoreOutlined />}
                  style={{ padding: '2px 4px' }}
                  onClick={(e) => e.stopPropagation()}
                />
              </Dropdown>
            )}
          </div>
        </NodeTitle>
      );
    },
    [
      onComponentSelect,
      onComponentUpdate,
      onComponentDelete,
      onComponentDuplicate,
      getNodeIcon,
    ],
  );

  /**
   * Convert components to tree data structure
   */
  const treeData = useMemo(() => {
    const buildTreeNode = (component, index) => {
      const category =
        COMPONENT_CATEGORIES[component.category?.toUpperCase()] ||
        COMPONENT_CATEGORIES.DATA_ENTRY;
      const nodeType = component.children
        ? TREE_NODE_TYPES.CONTAINER
        : TREE_NODE_TYPES.COMPONENT;

      return {
        key: component.id || `component-${index}`,
        title: renderNodeTitle(component, category, nodeType),
        icon: getNodeIcon(component, nodeType),
        children: component.children
          ? component.children.map(buildTreeNode)
          : undefined,
        data: component,
      };
    };

    return [
      {
        key: 'root',
        title: renderNodeTitle(
          { label: 'Form Root', type: 'root' },
          null,
          TREE_NODE_TYPES.ROOT,
        ),
        icon: <FolderOutlined style={{ color: TREE_NODE_TYPES.ROOT.color }} />,
        children: components.map(buildTreeNode),
      },
    ];
  }, [components, renderNodeTitle, getNodeIcon]);

  /**
   * Handle tree node selection
   */
  const handleSelect = useCallback(
    (selectedKeys, info) => {
      if (selectedKeys.length > 0 && selectedKeys[0] !== 'root') {
        onComponentSelect?.(selectedKeys[0]);
      }
    },
    [onComponentSelect],
  );

  /**
   * Handle tree expansion
   */
  const handleExpand = useCallback((expandedKeys) => {
    setExpandedKeys(expandedKeys);
  }, []);

  /**
   * Handle search
   */
  const handleSearch = useCallback(
    (value) => {
      setSearchValue(value);

      if (value) {
        // Auto-expand all nodes when searching
        const getAllKeys = (nodes) => {
          let keys = [];
          nodes.forEach((node) => {
            keys.push(node.key);
            if (node.children) {
              keys = keys.concat(getAllKeys(node.children));
            }
          });
          return keys;
        };

        setExpandedKeys(getAllKeys(treeData));
      }
    },
    [treeData],
  );

  /**
   * Filter tree data based on search
   */
  const filteredTreeData = useMemo(() => {
    if (!searchValue) return treeData;

    const filterNodes = (nodes) => {
      return nodes
        .map((node) => {
          const matchesSearch = node.title.props.children[1].props.children
            .toLowerCase()
            .includes(searchValue.toLowerCase());

          const filteredChildren = node.children
            ? filterNodes(node.children)
            : [];
          const hasMatchingChildren = filteredChildren.length > 0;

          if (matchesSearch || hasMatchingChildren) {
            return {
              ...node,
              children:
                filteredChildren.length > 0 ? filteredChildren : node.children,
            };
          }

          return null;
        })
        .filter(Boolean);
    };

    return filterNodes(treeData);
  }, [treeData, searchValue]);

  return (
    <TreeContainer>
      <TreeHeader>
        <div className='header-title'>
          <AppstoreOutlined />
          Component Hierarchy
          <Badge
            count={components.length}
            size='small'
            style={{ marginLeft: 'auto' }}
          />
        </div>

        {showSearch && (
          <Search
            placeholder='Search components...'
            value={searchValue}
            onChange={(e) => handleSearch(e.target.value)}
            prefix={<SearchOutlined />}
            allowClear
            size='small'
          />
        )}
      </TreeHeader>

      <TreeContent>
        <AnimatePresence>
          <motion.div
            variants={MANAGEMENT_ANIMATIONS.TREE_EXPAND}
            initial='hidden'
            animate='visible'
            exit='exit'
          >
            <Tree
              treeData={filteredTreeData}
              selectedKeys={selectedComponentId ? [selectedComponentId] : []}
              expandedKeys={expandedKeys}
              onSelect={handleSelect}
              onExpand={handleExpand}
              showIcon={false}
              blockNode
              draggable={{
                icon: false,
                nodeDraggable: (node) => node.key !== 'root',
              }}
              onDrop={(info) => {
                // Handle drag and drop reordering
                console.log('Tree drop:', info);
              }}
            />
          </motion.div>
        </AnimatePresence>
      </TreeContent>
    </TreeContainer>
  );
};

export default ComponentHierarchyTree;
