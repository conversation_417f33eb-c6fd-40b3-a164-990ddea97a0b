/**
 * @fileoverview Custom hook for Properties Panel state management
 *
 * This hook encapsulates all properties panel state management including
 * tab navigation, loading states, mobile detection, and component updates.
 *
 * @module usePropertiesPanel
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { message } from 'antd';
import {
  PANEL_TABS,
  PANEL_DEFAULTS,
  RESPONSIVE_BREAKPOINTS,
} from '../constants/propertiesPanelConstants';

/**
 * Custom hook for properties panel state management
 *
 * Provides comprehensive state management for the properties panel,
 * including tab navigation, loading states, and component updates.
 *
 * @param {Object} params - Hook parameters
 * @param {Object} params.componentData - Current component data
 * @param {string} params.componentId - Component ID
 * @param {Function} params.onUpdateComponent - Component update callback
 * @param {Function} params.onClose - Panel close callback
 *
 * @returns {Object} Properties panel state and handlers
 * @returns {string} returns.activeTab - Currently active tab
 * @returns {Function} returns.setActiveTab - Tab setter function
 * @returns {boolean} returns.isLoading - Loading state
 * @returns {boolean} returns.isMobile - Mobile detection state
 * @returns {Function} returns.handleTabChange - Tab change handler
 * @returns {Function} returns.handlePropertyUpdate - Property update handler
 * @returns {Function} returns.handleClose - Panel close handler
 *
 * @example
 * ```jsx
 * const {
 *   activeTab,
 *   isLoading,
 *   isMobile,
 *   handleTabChange,
 *   handlePropertyUpdate,
 *   handleClose
 * } = usePropertiesPanel({
 *   componentData,
 *   componentId,
 *   onUpdateComponent,
 *   onClose
 * });
 * ```
 */
export const usePropertiesPanel = ({
  componentData,
  componentId,
  onUpdateComponent,
  onClose,
}) => {
  // Core state
  const [activeTab, setActiveTab] = useState(PANEL_DEFAULTS.ACTIVE_TAB);
  const [isLoading, setIsLoading] = useState(false);
  const [pendingUpdates, setPendingUpdates] = useState({});

  // Refs for debouncing
  const debounceTimeoutRef = useRef(null);
  const lastUpdateTimeRef = useRef(0);

  // Enhanced device detection with orientation support
  const [deviceInfo, setDeviceInfo] = useState(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const isLandscape = width > height;

    return {
      isMobile: width <= RESPONSIVE_BREAKPOINTS.MOBILE,
      isTablet:
        width > RESPONSIVE_BREAKPOINTS.MOBILE &&
        width <= RESPONSIVE_BREAKPOINTS.TABLET,
      isDesktop: width > RESPONSIVE_BREAKPOINTS.TABLET,
      isLandscape,
      isPortrait: !isLandscape,
      width,
      height,
      // Touch device detection
      isTouchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    };
  });

  // Handle window resize and orientation changes
  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isLandscape = width > height;

      setDeviceInfo({
        isMobile: width <= RESPONSIVE_BREAKPOINTS.MOBILE,
        isTablet:
          width > RESPONSIVE_BREAKPOINTS.MOBILE &&
          width <= RESPONSIVE_BREAKPOINTS.TABLET,
        isDesktop: width > RESPONSIVE_BREAKPOINTS.TABLET,
        isLandscape,
        isPortrait: !isLandscape,
        width,
        height,
        isTouchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      });
    };

    // Debounce resize events for better performance
    let timeoutId;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateDeviceInfo, 150);
    };

    window.addEventListener('resize', debouncedUpdate);
    window.addEventListener('orientationchange', debouncedUpdate);

    return () => {
      window.removeEventListener('resize', debouncedUpdate);
      window.removeEventListener('orientationchange', debouncedUpdate);
      clearTimeout(timeoutId);
    };
  }, []);

  // Reset tab when component changes
  useEffect(() => {
    if (componentId) {
      setActiveTab(PANEL_DEFAULTS.ACTIVE_TAB);
      setPendingUpdates({});
    }
  }, [componentId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear any pending debounce timeouts
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
        debounceTimeoutRef.current = null;
      }

      console.log('🧹 [usePropertiesPanel] Cleanup on unmount');
    };
  }, []);

  /**
   * Handles tab change with validation
   *
   * @param {string} newTab - New tab to activate
   */
  const handleTabChange = useCallback(
    (newTab) => {
      if (Object.values(PANEL_TABS).includes(newTab)) {
        setActiveTab(newTab);

        console.log('🔄 [usePropertiesPanel] Tab changed:', {
          from: activeTab,
          to: newTab,
          componentId,
        });
      }
    },
    [activeTab, componentId],
  );

  /**
   * Handles property updates with validation and batching
   *
   * @param {string} propertyPath - Property path (supports dot notation)
   * @param {*} value - New property value
   * @param {Object} options - Update options
   */
  const handlePropertyUpdate = useCallback(
    async (propertyPath, value, options = {}) => {
      const { immediate = false, validate = true, debounce = true } = options;
      const currentTime = Date.now();

      try {
        console.log('📝 [usePropertiesPanel] Property update:', {
          componentId,
          propertyPath,
          value,
          immediate,
          debounce,
        });

        // Add to pending updates if not immediate
        if (!immediate) {
          setPendingUpdates((prev) => ({
            ...prev,
            [propertyPath]: value,
          }));

          // Handle debounced updates
          if (debounce) {
            // Clear existing timeout
            if (debounceTimeoutRef.current) {
              clearTimeout(debounceTimeoutRef.current);
            }

            // Set new timeout for debounced update
            debounceTimeoutRef.current = setTimeout(() => {
              handlePropertyUpdate(propertyPath, value, {
                immediate: true,
                validate,
                debounce: false,
              });
            }, 500); // 500ms debounce delay

            return;
          }
        }

        // Prevent too frequent updates
        if (currentTime - lastUpdateTimeRef.current < 100 && !immediate) {
          return;
        }

        lastUpdateTimeRef.current = currentTime;
        setIsLoading(true);

        // Create updated component data
        const updatedComponent = { ...componentData };

        // Handle nested property paths
        const pathParts = propertyPath.split('.');
        let current = updatedComponent;

        for (let i = 0; i < pathParts.length - 1; i++) {
          const part = pathParts[i];
          if (!current[part]) {
            current[part] = {};
          }
          current = current[part];
        }

        // Set the final value
        const finalKey = pathParts[pathParts.length - 1];
        current[finalKey] = value;

        // Apply any pending updates
        if (Object.keys(pendingUpdates).length > 0) {
          Object.entries(pendingUpdates).forEach(([path, pendingValue]) => {
            const parts = path.split('.');
            let target = updatedComponent;

            for (let i = 0; i < parts.length - 1; i++) {
              const part = parts[i];
              if (!target[part]) {
                target[part] = {};
              }
              target = target[part];
            }

            target[parts[parts.length - 1]] = pendingValue;
          });

          setPendingUpdates({});
        }

        // Call the update callback
        if (onUpdateComponent) {
          await onUpdateComponent(componentId, updatedComponent);
        }

        // Show success message for significant updates (but not for rapid changes)
        if (validate && immediate) {
          message.success('Property updated successfully');
        }
      } catch (error) {
        console.error('❌ [usePropertiesPanel] Property update failed:', error);
        message.error('Failed to update property');
      } finally {
        setIsLoading(false);
      }
    },
    [componentData, componentId, onUpdateComponent, pendingUpdates],
  );

  /**
   * Handles panel close with cleanup
   */
  const handleClose = useCallback(() => {
    console.log('🔒 [usePropertiesPanel] Panel closing:', {
      componentId,
      pendingUpdates: Object.keys(pendingUpdates).length,
    });

    // Apply any pending updates before closing
    if (Object.keys(pendingUpdates).length > 0) {
      Object.entries(pendingUpdates).forEach(([path, value]) => {
        handlePropertyUpdate(path, value, { immediate: true, validate: false });
      });
    }

    // Reset state
    setActiveTab(PANEL_DEFAULTS.ACTIVE_TAB);
    setPendingUpdates({});
    setIsLoading(false);

    // Call close callback
    if (onClose) {
      onClose();
    }
  }, [componentId, pendingUpdates, handlePropertyUpdate, onClose]);

  // Memoized component type information
  const componentTypeInfo = useMemo(() => {
    if (!componentData) return null;

    return {
      type: componentData.type,
      label: componentData.label || componentData.name || 'Unnamed Component',
      isContainer: componentData.children !== undefined,
      hasValidation: componentData.validation !== undefined,
      hasCustomStyling: componentData.styling !== undefined,
    };
  }, [componentData]);

  return {
    // Core state
    activeTab,
    setActiveTab,
    isLoading,

    // Device information
    isMobile: deviceInfo.isMobile,
    isTablet: deviceInfo.isTablet,
    isDesktop: deviceInfo.isDesktop,
    isLandscape: deviceInfo.isLandscape,
    isPortrait: deviceInfo.isPortrait,
    isTouchDevice: deviceInfo.isTouchDevice,
    deviceInfo,

    // Computed state
    componentTypeInfo,
    hasPendingUpdates: Object.keys(pendingUpdates).length > 0,

    // Handlers
    handleTabChange,
    handlePropertyUpdate,
    handleClose,
  };
};
