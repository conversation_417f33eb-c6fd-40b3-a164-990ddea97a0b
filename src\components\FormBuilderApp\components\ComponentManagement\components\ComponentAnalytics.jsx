/**
 * @fileoverview Component Analytics component
 *
 * This component provides comprehensive analytics and insights about
 * component usage, performance, and trends in the form builder.
 *
 * @module ComponentAnalytics
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Select,
  Space,
  Table,
  Tag,
} from 'antd';
import {
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  AppstoreOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import {
  COMPONENT_CATEGORIES,
  USAGE_METRICS,
  ANALYTICS_PERIODS,
  COMPLEXITY_SCORING,
  MANAGEMENT_ANIMATIONS,
} from '../constants/managementConstants';

const { Option } = Select;

/**
 * Styled components
 */
const AnalyticsContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow-y: auto;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
`;

const AnalyticsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: #262626;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
  }
`;

const MetricCard = styled(Card)`
  .ant-card-body {
    padding: 20px;
  }

  .metric-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }

  .trend-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    margin-top: 4px;

    &.positive {
      color: #52c41a;
    }

    &.negative {
      color: #ff4d4f;
    }

    &.neutral {
      color: #666;
    }
  }
`;

const ChartCard = styled(Card)`
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
  }

  .chart-placeholder {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    border-radius: 8px;
    color: #666;
    font-size: 14px;
  }
`;

/**
 * Component Analytics component
 *
 * @param {Object} props - Component props
 * @param {Array} props.components - Array of form components
 * @param {Object} props.analyticsData - Analytics data object
 * @param {Function} props.onPeriodChange - Period change callback
 * @param {string} props.selectedPeriod - Currently selected time period
 * @returns {React.ReactNode} Component analytics JSX
 */
const ComponentAnalytics = ({
  components = [],
  analyticsData = {},
  onPeriodChange,
  selectedPeriod = ANALYTICS_PERIODS.LAST_30_DAYS.key,
}) => {
  const [selectedMetric, setSelectedMetric] = useState(
    USAGE_METRICS.TOTAL_USAGE.key,
  );

  /**
   * Calculate component statistics
   */
  const componentStats = useMemo(() => {
    const totalComponents = components.length;
    const activeComponents = components.filter(
      (c) => c.status !== 'disabled',
    ).length;
    const categoryDistribution = {};
    const complexityDistribution = {};

    components.forEach((component) => {
      const category = component.category || 'unknown';
      const complexity = component.complexity || 'simple';

      categoryDistribution[category] =
        (categoryDistribution[category] || 0) + 1;
      complexityDistribution[complexity] =
        (complexityDistribution[complexity] || 0) + 1;
    });

    return {
      totalComponents,
      activeComponents,
      categoryDistribution,
      complexityDistribution,
      utilizationRate:
        totalComponents > 0 ? (activeComponents / totalComponents) * 100 : 0,
    };
  }, [components]);

  /**
   * Generate mock usage data for demonstration
   */
  const usageData = useMemo(() => {
    return components.map((component, index) => ({
      key: component.id || index,
      name: component.label || component.type || 'Unnamed',
      type: component.type,
      category: component.category,
      usage: Math.floor(Math.random() * 100),
      errors: Math.floor(Math.random() * 10),
      performance: Math.floor(Math.random() * 500) + 100,
      complexity: component.complexity || 'simple',
      lastUsed: new Date(
        Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
      ).toLocaleDateString(),
    }));
  }, [components]);

  /**
   * Table columns for component usage
   */
  const usageColumns = [
    {
      title: 'Component',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <AppstoreOutlined
            style={{
              color:
                COMPONENT_CATEGORIES[record.category?.toUpperCase()]?.color ||
                '#666',
            }}
          />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => <Tag color='blue'>{type}</Tag>,
    },
    {
      title: 'Usage',
      dataIndex: 'usage',
      key: 'usage',
      render: (usage) => (
        <div style={{ width: '80px' }}>
          <Progress percent={usage} size='small' showInfo={false} />
          <span style={{ fontSize: '12px', color: '#666' }}>{usage}%</span>
        </div>
      ),
      sorter: (a, b) => a.usage - b.usage,
    },
    {
      title: 'Complexity',
      dataIndex: 'complexity',
      key: 'complexity',
      render: (complexity) => {
        const config =
          COMPLEXITY_SCORING[complexity.toUpperCase()] ||
          COMPLEXITY_SCORING.SIMPLE;
        return <Tag color={config.color}>{config.label}</Tag>;
      },
    },
    {
      title: 'Performance',
      dataIndex: 'performance',
      key: 'performance',
      render: (time) => (
        <Space>
          <ClockCircleOutlined
            style={{ color: time > 300 ? '#ff4d4f' : '#52c41a' }}
          />
          <span>{time}ms</span>
        </Space>
      ),
      sorter: (a, b) => a.performance - b.performance,
    },
    {
      title: 'Last Used',
      dataIndex: 'lastUsed',
      key: 'lastUsed',
    },
  ];

  /**
   * Handle period change
   */
  const handlePeriodChange = useCallback(
    (period) => {
      onPeriodChange?.(period);
    },
    [onPeriodChange],
  );

  /**
   * Render trend indicator
   */
  const renderTrendIndicator = useCallback((value, previousValue) => {
    if (!previousValue) return null;

    const change = ((value - previousValue) / previousValue) * 100;
    const isPositive = change > 0;
    const isNeutral = Math.abs(change) < 1;

    return (
      <div
        className={`trend-indicator ${
          isNeutral ? 'neutral' : isPositive ? 'positive' : 'negative'
        }`}
      >
        {isNeutral ? null : isPositive ? <RiseOutlined /> : <FallOutlined />}
        {Math.abs(change).toFixed(1)}%
      </div>
    );
  }, []);

  return (
    <AnalyticsContainer>
      <AnalyticsHeader>
        <div className='header-title'>
          <BarChartOutlined />
          Component Analytics
        </div>
        <div className='header-controls'>
          <Select
            value={selectedPeriod}
            onChange={handlePeriodChange}
            style={{ width: 150 }}
            size='small'
          >
            {Object.values(ANALYTICS_PERIODS).map((period) => (
              <Option key={period.key} value={period.key}>
                {period.label}
              </Option>
            ))}
          </Select>
        </div>
      </AnalyticsHeader>

      {/* Key Metrics */}
      <motion.div
        variants={MANAGEMENT_ANIMATIONS.ANALYTICS_UPDATE}
        initial='hidden'
        animate='visible'
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <MetricCard>
              <Statistic
                title='Total Components'
                value={componentStats.totalComponents}
                prefix={
                  <AppstoreOutlined
                    className='metric-icon'
                    style={{ color: '#1890ff' }}
                  />
                }
              />
              {renderTrendIndicator(componentStats.totalComponents, 45)}
            </MetricCard>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <MetricCard>
              <Statistic
                title='Active Components'
                value={componentStats.activeComponents}
                prefix={
                  <TrophyOutlined
                    className='metric-icon'
                    style={{ color: '#52c41a' }}
                  />
                }
              />
              {renderTrendIndicator(componentStats.activeComponents, 38)}
            </MetricCard>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <MetricCard>
              <Statistic
                title='Utilization Rate'
                value={componentStats.utilizationRate}
                precision={1}
                suffix='%'
                prefix={
                  <PieChartOutlined
                    className='metric-icon'
                    style={{ color: '#722ed1' }}
                  />
                }
              />
              {renderTrendIndicator(componentStats.utilizationRate, 82.3)}
            </MetricCard>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <MetricCard>
              <Statistic
                title='Avg Performance'
                value={245}
                suffix='ms'
                prefix={
                  <LineChartOutlined
                    className='metric-icon'
                    style={{ color: '#faad14' }}
                  />
                }
              />
              {renderTrendIndicator(245, 267)}
            </MetricCard>
          </Col>
        </Row>
      </motion.div>

      {/* Charts Section */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <ChartCard title='Category Distribution' size='small'>
            <div className='chart-placeholder'>
              <div style={{ textAlign: 'center' }}>
                <PieChartOutlined
                  style={{
                    fontSize: '32px',
                    color: '#d9d9d9',
                    marginBottom: '8px',
                  }}
                />
                <div>Category distribution chart would be rendered here</div>
                <div
                  style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}
                >
                  Data Entry:{' '}
                  {componentStats.categoryDistribution.dataEntry || 0}{' '}
                  components
                </div>
              </div>
            </div>
          </ChartCard>
        </Col>

        <Col xs={24} lg={12}>
          <ChartCard title='Usage Trends' size='small'>
            <div className='chart-placeholder'>
              <div style={{ textAlign: 'center' }}>
                <LineChartOutlined
                  style={{
                    fontSize: '32px',
                    color: '#d9d9d9',
                    marginBottom: '8px',
                  }}
                />
                <div>Usage trends chart would be rendered here</div>
                <div
                  style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}
                >
                  Showing{' '}
                  {ANALYTICS_PERIODS[selectedPeriod]?.label ||
                    'selected period'}
                </div>
              </div>
            </div>
          </ChartCard>
        </Col>
      </Row>

      {/* Component Usage Table */}
      <Card title='Component Usage Details' size='small'>
        <Table
          columns={usageColumns}
          dataSource={usageData}
          size='small'
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} components`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>
    </AnalyticsContainer>
  );
};

export default ComponentAnalytics;
