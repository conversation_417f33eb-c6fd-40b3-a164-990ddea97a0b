import { useState, useCallback } from 'react';
import { message } from 'antd';

/**
 * Custom hook for handling form submission logic
 * 
 * Manages form submission state, loading indicators, and error handling.
 * Provides a consistent interface for form submission across different form types.
 * 
 * @param {Function} onSubmit - Optional callback function to handle form submission
 * @returns {Object} Form submission utilities and state
 * @returns {boolean} returns.loading - Whether form is currently submitting
 * @returns {Function} returns.handleSubmit - Function to handle form submission
 * @returns {Function} returns.handleFinishFailed - Function to handle validation failures
 */
export const useFormSubmission = (onSubmit) => {
  const [loading, setLoading] = useState(false);

  /**
   * Handles form submission with loading state and error handling
   * 
   * @param {Object} values - Form values from Ant Design form
   * @returns {Promise<void>}
   */
  const handleSubmit = useCallback(
    async (values) => {
      // If no onSubmit callback provided, show default success message
      if (!onSubmit) {
        message.info('Form submitted successfully!');
        console.log('Form values:', values);
        return;
      }

      try {
        setLoading(true);
        await onSubmit(values);
        message.success('Form submitted successfully!');
      } catch (error) {
        console.error('Form submission error:', error);
        message.error('Failed to submit form. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    [onSubmit]
  );

  /**
   * Handles form validation failures
   * 
   * @param {Object} errorInfo - Error information from Ant Design form validation
   */
  const handleFinishFailed = useCallback((errorInfo) => {
    console.log('Form validation failed:', errorInfo);
    message.error('Please check the form and fix any errors');
  }, []);

  return {
    loading,
    handleSubmit,
    handleFinishFailed,
  };
};
