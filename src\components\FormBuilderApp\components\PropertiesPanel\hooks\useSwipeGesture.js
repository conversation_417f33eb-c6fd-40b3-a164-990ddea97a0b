/**
 * @fileoverview Custom hook for swipe gesture handling on mobile devices
 *
 * This hook provides swipe gesture detection for mobile interactions,
 * specifically for closing the properties panel with a downward swipe.
 *
 * @module useSwipeGesture
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useCallback, useRef, useEffect } from 'react';

/**
 * Custom hook for swipe gesture handling
 *
 * Provides swipe gesture detection with configurable thresholds
 * and direction handling for mobile touch interactions.
 *
 * @param {Object} params - Hook parameters
 * @param {Function} params.onSwipeDown - Callback for downward swipe
 * @param {Function} params.onSwipeUp - Callback for upward swipe
 * @param {boolean} params.enabled - Whether gesture detection is enabled
 * @param {number} params.threshold - Minimum distance for swipe detection
 * @param {number} params.velocityThreshold - Minimum velocity for swipe
 *
 * @returns {Object} Swipe gesture handlers
 * @returns {Function} returns.onTouchStart - Touch start handler
 * @returns {Function} returns.onTouchMove - Touch move handler
 * @returns {Function} returns.onTouchEnd - Touch end handler
 * @returns {Object} returns.swipeProps - Props to spread on element
 *
 * @example
 * ```jsx
 * const { swipeProps } = useSwipeGesture({
 *   onSwipeDown: handleClose,
 *   enabled: isMobile,
 *   threshold: 50
 * });
 * 
 * return <div {...swipeProps}>Content</div>;
 * ```
 */
export const useSwipeGesture = ({
  onSwipeDown,
  onSwipeUp,
  enabled = true,
  threshold = 50,
  velocityThreshold = 0.3,
}) => {
  const touchStartRef = useRef(null);
  const touchMoveRef = useRef(null);
  const startTimeRef = useRef(null);

  /**
   * Handles touch start event
   *
   * @param {TouchEvent} event - Touch event
   */
  const handleTouchStart = useCallback(
    (event) => {
      if (!enabled || event.touches.length !== 1) return;

      const touch = event.touches[0];
      touchStartRef.current = {
        x: touch.clientX,
        y: touch.clientY,
      };
      startTimeRef.current = Date.now();
      touchMoveRef.current = null;

      console.log('🤏 [useSwipeGesture] Touch start:', {
        x: touch.clientX,
        y: touch.clientY,
      });
    },
    [enabled],
  );

  /**
   * Handles touch move event
   *
   * @param {TouchEvent} event - Touch event
   */
  const handleTouchMove = useCallback(
    (event) => {
      if (!enabled || !touchStartRef.current || event.touches.length !== 1) return;

      const touch = event.touches[0];
      touchMoveRef.current = {
        x: touch.clientX,
        y: touch.clientY,
      };

      // Calculate current delta
      const deltaY = touch.clientY - touchStartRef.current.y;
      const deltaX = touch.clientX - touchStartRef.current.x;

      // Prevent default scrolling if this is a vertical swipe
      if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 10) {
        event.preventDefault();
      }
    },
    [enabled],
  );

  /**
   * Handles touch end event
   *
   * @param {TouchEvent} event - Touch event
   */
  const handleTouchEnd = useCallback(
    (event) => {
      if (!enabled || !touchStartRef.current || !touchMoveRef.current) {
        // Reset refs
        touchStartRef.current = null;
        touchMoveRef.current = null;
        startTimeRef.current = null;
        return;
      }

      const deltaX = touchMoveRef.current.x - touchStartRef.current.x;
      const deltaY = touchMoveRef.current.y - touchStartRef.current.y;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      const duration = Date.now() - startTimeRef.current;
      const velocity = distance / duration; // pixels per millisecond

      console.log('🤏 [useSwipeGesture] Touch end:', {
        deltaX,
        deltaY,
        distance,
        duration,
        velocity,
        threshold,
        velocityThreshold,
      });

      // Check if this qualifies as a swipe
      if (distance >= threshold && velocity >= velocityThreshold) {
        // Determine swipe direction (prioritize vertical over horizontal)
        if (Math.abs(deltaY) > Math.abs(deltaX)) {
          if (deltaY > 0 && onSwipeDown) {
            console.log('⬇️ [useSwipeGesture] Swipe down detected');
            onSwipeDown(event);
          } else if (deltaY < 0 && onSwipeUp) {
            console.log('⬆️ [useSwipeGesture] Swipe up detected');
            onSwipeUp(event);
          }
        }
      }

      // Reset refs
      touchStartRef.current = null;
      touchMoveRef.current = null;
      startTimeRef.current = null;
    },
    [enabled, threshold, velocityThreshold, onSwipeDown, onSwipeUp],
  );

  /**
   * Cleanup function to reset gesture state
   */
  const cleanup = useCallback(() => {
    touchStartRef.current = null;
    touchMoveRef.current = null;
    startTimeRef.current = null;
  }, []);

  // Cleanup on unmount or when disabled
  useEffect(() => {
    if (!enabled) {
      cleanup();
    }
  }, [enabled, cleanup]);

  // Props to spread on the target element
  const swipeProps = {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    style: {
      touchAction: enabled ? 'pan-y' : 'auto',
    },
  };

  return {
    // Individual handlers
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    
    // Combined props
    swipeProps,
    
    // Utilities
    cleanup,
  };
};
