/**
 * Component Animation Constants
 * 
 * Framer Motion animation variants and configurations for the Component module.
 * Provides smooth, professional animations for drag-and-drop interactions,
 * hover states, and component transitions.
 * 
 * Features:
 * - Premium UX animations with optimized easing curves
 * - GPU-accelerated transforms for 60fps performance
 * - Consistent animation timing across all component states
 * - Accessibility-friendly motion with reduced motion support
 * 
 * @module ComponentAnimations
 */

/**
 * Main component animation variants
 * 
 * Handles the primary component states including hover, tap, and dragging.
 * Uses hardware acceleration and optimized transforms for smooth performance.
 * 
 * @type {Object}
 */
export const componentVariants = {
  /**
   * Initial state - neutral position and scale
   */
  initial: {
    opacity: 1,
    scale: 1,
    y: 0,
  },
  
  /**
   * Hover state - subtle lift effect for premium feel
   * Uses minimal transform to maintain performance
   */
  hover: {
    y: -1,
    scale: 1.005,
    transition: {
      duration: 0.2,
      ease: [0.25, 0.46, 0.45, 0.94], // Custom cubic-bezier for smooth motion
    },
  },
  
  /**
   * Tap state - brief scale down for tactile feedback
   */
  tap: {
    scale: 0.998,
    transition: {
      duration: 0.1,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  
  /**
   * Dragging state - enhanced scale and rotation for clear drag feedback
   * Higher z-index ensures dragged element appears above others
   */
  dragging: {
    scale: 1.02,
    rotate: 2,
    opacity: 0.8,
    zIndex: 1000,
    transition: {
      duration: 0.2,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

/**
 * Component label animation variants
 * 
 * Controls the appearance and disappearance of component labels on hover.
 * Uses staggered animation with delay for polished UX.
 * 
 * @type {Object}
 */
export const labelVariants = {
  /**
   * Hidden state - off-screen with reduced scale
   */
  hidden: {
    opacity: 0,
    x: 8,
    scale: 0.95,
  },
  
  /**
   * Visible state - smooth entrance with spring-like easing
   * Includes delay to prevent flickering on quick hover
   */
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.2,
      delay: 0.1, // Prevents label flicker on quick hover
      ease: [0.16, 1, 0.3, 1], // Spring-like easing for natural feel
    },
  },
};

/**
 * Drag handle animation variants
 * 
 * Controls the drag handle visibility and hover states.
 * Provides clear visual feedback for draggable areas.
 * 
 * @type {Object}
 */
export const dragHandleVariants = {
  /**
   * Hidden state - invisible with reduced scale
   */
  hidden: {
    opacity: 0,
    scale: 0.8,
  },
  
  /**
   * Visible state - subtle appearance for non-intrusive presence
   */
  visible: {
    opacity: 0.7,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: [0.16, 1, 0.3, 1],
    },
  },
  
  /**
   * Hover state - enhanced visibility and scale for clear interaction
   */
  hover: {
    scale: 1.1,
    opacity: 1,
    transition: {
      duration: 0.15,
      ease: [0.16, 1, 0.3, 1],
    },
  },
};

/**
 * Animation configuration constants
 * 
 * Centralized timing and easing configurations for consistent animations
 * across the component system.
 */
export const animationConfig = {
  /**
   * Standard animation durations (in seconds)
   */
  durations: {
    fast: 0.1,
    normal: 0.2,
    slow: 0.3,
  },
  
  /**
   * Easing curves for different animation types
   */
  easings: {
    // Standard easing for most interactions
    standard: [0.25, 0.46, 0.45, 0.94],
    // Spring-like easing for entrances
    spring: [0.16, 1, 0.3, 1],
    // Sharp easing for exits
    sharp: [0.4, 0, 0.6, 1],
  },
  
  /**
   * Delay configurations for staggered animations
   */
  delays: {
    short: 0.05,
    medium: 0.1,
    long: 0.15,
  },
};

/**
 * Accessibility-aware animation preferences
 * 
 * Respects user's motion preferences for inclusive design.
 * Automatically reduces motion for users who prefer reduced motion.
 */
export const accessibleAnimationConfig = {
  /**
   * Reduced motion variants for accessibility compliance
   */
  reducedMotion: {
    initial: { opacity: 1 },
    hover: { opacity: 1 },
    tap: { opacity: 0.8 },
    dragging: { opacity: 0.7 },
  },
  
  /**
   * Check if user prefers reduced motion
   * @returns {boolean} True if reduced motion is preferred
   */
  prefersReducedMotion: () => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }
    return false;
  },
};

/**
 * Animation utility functions
 */
export const animationUtils = {
  /**
   * Get appropriate animation variants based on user preferences
   * @param {Object} normalVariants - Standard animation variants
   * @param {Object} reducedVariants - Reduced motion variants
   * @returns {Object} Appropriate variants for user's preferences
   */
  getVariants: (normalVariants, reducedVariants) => {
    return accessibleAnimationConfig.prefersReducedMotion() 
      ? reducedVariants 
      : normalVariants;
  },
  
  /**
   * Create a transition with fallback for reduced motion
   * @param {Object} transition - Standard transition config
   * @returns {Object} Appropriate transition for user's preferences
   */
  createTransition: (transition) => {
    if (accessibleAnimationConfig.prefersReducedMotion()) {
      return { duration: 0.01 }; // Near-instant for reduced motion
    }
    return transition;
  },
};
