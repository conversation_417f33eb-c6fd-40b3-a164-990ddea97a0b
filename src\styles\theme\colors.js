// Modern Enterprise Design System - 2024+ Standards
// Inspired by Microsoft Fluent Design with AI-first enhancements
export const colors = {
  // Primary AI-focused brand colors
  primary: '#0066cc',
  primaryHover: '#0052a3',
  primaryActive: '#003d7a',
  primaryLight: '#e6f2ff',
  primaryLighter: '#f0f8ff',
  primaryGradient: 'linear-gradient(135deg, #0066cc 0%, #0052a3 100%)',

  // AI-specific accent colors
  aiPrimary: '#6366f1', // Modern indigo for AI features
  aiSecondary: '#8b5cf6', // Purple for AI processing
  aiSuccess: '#10b981', // Green for AI success states
  aiWarning: '#f59e0b', // Amber for AI warnings
  aiGradient: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',

  // Enhanced neutral palette (Modern gray scale)
  gray50: '#fafafa',
  gray100: '#f5f5f5',
  gray200: '#eeeeee',
  gray300: '#e0e0e0',
  gray400: '#bdbdbd',
  gray500: '#9e9e9e',
  gray600: '#757575',
  gray700: '#616161',
  gray800: '#424242',
  gray900: '#212121',
  gray950: '#0a0a0a',

  // Semantic colors (Enhanced for modern UI)
  success: '#10b981',
  successLight: '#d1fae5',
  successDark: '#047857',
  warning: '#f59e0b',
  warningLight: '#fef3c7',
  warningDark: '#d97706',
  error: '#ef4444',
  errorLight: '#fee2e2',
  errorDark: '#dc2626',
  info: '#3b82f6',
  infoLight: '#dbeafe',
  infoDark: '#1d4ed8',

  // Background system (Layered approach)
  background: '#ffffff',
  backgroundSecondary: '#fafafa',
  backgroundTertiary: '#f5f5f5',
  backgroundQuaternary: '#eeeeee',
  backgroundOverlay: 'rgba(255, 255, 255, 0.95)',
  backgroundBlur: 'rgba(255, 255, 255, 0.8)',
  backgroundGlass: 'rgba(255, 255, 255, 0.1)',

  // Border system (UX-optimized hierarchy)
  border: '#e0e0e0',
  borderLight: '#f5f5f5', // Subtle boundaries for low-priority elements
  borderActive: '#0066cc',
  borderInactive: '#e8e8e8', // Clearer inactive state
  borderHover: '#c0c0c0', // More noticeable hover feedback
  borderFocus: '#0066cc', // Consistent with primary brand
  borderError: '#ef4444',
  borderSuccess: '#10b981',
  borderInteractive: '#d1d5db', // Clear interactive element boundaries

  // Text hierarchy (Enhanced readability)
  textPrimary: '#212121',
  textSecondary: '#616161',
  textTertiary: '#757575',
  textDisabled: '#bdbdbd',
  textOnPrimary: '#ffffff',
  textOnDark: '#ffffff',
  textLink: '#0066cc',
  textLinkHover: '#0052a3',

  // Surface colors (Modern elevation system)
  surface: '#ffffff',
  surfaceElevated: '#ffffff',
  surfaceHover: '#fafafa',
  surfaceActive: '#f5f5f5',
  surfaceDisabled: '#eeeeee',

  // Shadow system (Modern depth)
  shadowLight: '0 1px 3px rgba(0, 0, 0, 0.1)',
  shadowMedium: '0 4px 6px rgba(0, 0, 0, 0.1)',
  shadowLarge: '0 10px 15px rgba(0, 0, 0, 0.1)',
  shadowXLarge: '0 20px 25px rgba(0, 0, 0, 0.1)',

  // Interactive states
  hover: 'rgba(0, 102, 204, 0.04)',
  active: 'rgba(0, 102, 204, 0.08)',
  focus: 'rgba(99, 102, 241, 0.12)',
  selected: 'rgba(0, 102, 204, 0.12)',
  disabled: 'rgba(189, 189, 189, 0.12)',
};
