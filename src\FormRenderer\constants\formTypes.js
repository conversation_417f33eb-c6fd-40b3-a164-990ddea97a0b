/**
 * Form Type Definitions and Enums
 * 
 * Type definitions, enums, and constants for form components and configurations.
 * Provides type safety and consistency across the FormRenderer system.
 */

/**
 * Form component types
 * 
 * Enumeration of all supported form component types.
 */
export const FORM_COMPONENT_TYPES = {
  // Input Components
  INPUT: 'input',
  TEXTAREA: 'textarea',
  EMAIL: 'email',
  PASSWORD: 'password',
  NUMBER: 'number',
  PHONE: 'phone',
  URL: 'url',
  
  // Selection Components
  SELECT: 'select',
  RADIO: 'radio',
  CHECKBOX: 'checkbox',
  SWITCH: 'switch',
  
  // Date/Time Components
  DATE: 'date',
  TIME: 'time',
  DATETIME: 'datetime',
  DATE_RANGE: 'dateRange',
  
  // Advanced Input Components
  AUTOCOMPLETE: 'autocomplete',
  CASCADER: 'cascader',
  COLOR_PICKER: 'colorPicker',
  MENTIONS: 'mentions',
  TREE_SELECT: 'treeSelect',
  TRANSFER: 'transfer',
  
  // Rating and Slider
  RATE: 'rate',
  SLIDER: 'slider',
  
  // File Upload
  UPLOAD: 'upload',
  
  // Display Components
  AVATAR: 'avatar',
  BADGE: 'badge',
  TAG: 'tag',
  IMAGE: 'image',
  
  // Layout Components
  DIVIDER: 'divider',
  SPACE: 'space',
  TYPOGRAPHY: 'typography',
  
  // Container Components
  CARD: 'card',
  COLLAPSE: 'collapse',
  TABS: 'tabs',
  
  // Feedback Components
  ALERT: 'alert',
  PROGRESS: 'progress',
  SKELETON: 'skeleton',
  SPIN: 'spin',
  
  // Navigation Components
  BREADCRUMB: 'breadcrumb',
  MENU: 'menu',
  PAGINATION: 'pagination',
  STEPS: 'steps',
  
  // Data Display Components
  TABLE: 'table',
  LIST: 'list',
  DESCRIPTIONS: 'descriptions',
  CALENDAR: 'calendar',
  CAROUSEL: 'carousel',
  EMPTY: 'empty',
  TIMELINE: 'timeline',
  TREE: 'tree',
  
  // Action Components
  BUTTON: 'button',
};

/**
 * Form validation types
 * 
 * Types of validation that can be applied to form fields.
 */
export const VALIDATION_TYPES = {
  REQUIRED: 'required',
  EMAIL: 'email',
  URL: 'url',
  NUMBER: 'number',
  INTEGER: 'integer',
  PHONE: 'phone',
  PATTERN: 'pattern',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength',
  MIN_VALUE: 'minValue',
  MAX_VALUE: 'maxValue',
  RANGE: 'range',
  DATE: 'date',
  TIME: 'time',
  FILE_SIZE: 'fileSize',
  FILE_TYPE: 'fileType',
  CUSTOM: 'custom',
};

/**
 * Form layout types
 * 
 * Available form layout configurations.
 */
export const FORM_LAYOUT_TYPES = {
  VERTICAL: 'vertical',
  HORIZONTAL: 'horizontal',
  INLINE: 'inline',
};

/**
 * Form size types
 * 
 * Available form size options.
 */
export const FORM_SIZE_TYPES = {
  SMALL: 'small',
  MIDDLE: 'middle',
  LARGE: 'large',
};

/**
 * Button types
 * 
 * Available button type configurations.
 */
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  DEFAULT: 'default',
  DASHED: 'dashed',
  TEXT: 'text',
  LINK: 'link',
};

/**
 * Form submission states
 * 
 * Possible states during form submission process.
 */
export const SUBMISSION_STATES = {
  IDLE: 'idle',
  SUBMITTING: 'submitting',
  SUCCESS: 'success',
  ERROR: 'error',
};

/**
 * Form validation states
 * 
 * Validation states for form fields.
 */
export const VALIDATION_STATES = {
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  VALIDATING: 'validating',
};

/**
 * Form field states
 * 
 * Possible states for individual form fields.
 */
export const FIELD_STATES = {
  NORMAL: 'normal',
  DISABLED: 'disabled',
  READONLY: 'readonly',
  HIDDEN: 'hidden',
};

/**
 * Form events
 * 
 * Events that can be triggered during form lifecycle.
 */
export const FORM_EVENTS = {
  SUBMIT: 'submit',
  RESET: 'reset',
  CHANGE: 'change',
  BLUR: 'blur',
  FOCUS: 'focus',
  VALIDATE: 'validate',
  ERROR: 'error',
};
