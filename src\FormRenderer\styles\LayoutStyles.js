/**
 * Layout Styling Components
 * 
 * Styled components for form layout and spacing utilities.
 * Provides consistent layout patterns across the FormRenderer.
 */

import styled from 'styled-components';
import { formTheme, mediaQueries } from './FormStyles';

/**
 * Form container with responsive layout
 * 
 * Main container for form content with responsive behavior.
 */
export const FormContainer = styled.div`
  width: 100%;
  max-width: ${props => props.maxWidth || '100%'};
  margin: 0 auto;
  padding: ${props => props.padding || formTheme.spacing.md};
  
  ${mediaQueries.mobile} {
    padding: ${formTheme.spacing.sm};
  }
`;

/**
 * Form row for horizontal layouts
 * 
 * Provides consistent row spacing and responsive behavior.
 */
export const FormRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${props => props.gap || formTheme.spacing.md};
  margin-bottom: ${props => props.marginBottom || formTheme.spacing.lg};
  
  ${mediaQueries.mobile} {
    flex-direction: column;
    gap: ${formTheme.spacing.sm};
  }
`;

/**
 * Form column for grid layouts
 * 
 * Flexible column component with responsive behavior.
 */
export const FormColumn = styled.div`
  flex: ${props => props.flex || '1'};
  min-width: ${props => props.minWidth || '200px'};
  
  ${mediaQueries.mobile} {
    flex: 1 1 100%;
    min-width: auto;
  }
`;

/**
 * Form field wrapper with consistent spacing
 * 
 * Wraps individual form fields with proper spacing.
 */
export const FieldWrapper = styled.div`
  margin-bottom: ${props => props.spacing || formTheme.spacing.md};
  
  &:last-child {
    margin-bottom: 0;
  }
  
  /* Field label styling */
  .field-label {
    display: block;
    margin-bottom: ${formTheme.spacing.xs};
    font-weight: 500;
    color: ${formTheme.colors.text};
    font-size: 14px;
  }
  
  /* Field description styling */
  .field-description {
    margin-top: ${formTheme.spacing.xs};
    font-size: 12px;
    color: ${formTheme.colors.textSecondary};
    line-height: 1.4;
  }
  
  /* Field error styling */
  .field-error {
    margin-top: ${formTheme.spacing.xs};
    font-size: 12px;
    color: ${formTheme.colors.error};
    line-height: 1.4;
  }
`;

/**
 * Divider component for form sections
 * 
 * Provides visual separation between form sections.
 */
export const FormDivider = styled.div`
  height: 1px;
  background-color: ${formTheme.colors.border};
  margin: ${props => props.margin || `${formTheme.spacing.lg} 0`};
  
  /* Dashed variant */
  ${props => props.dashed && `
    background: none;
    border-top: 1px dashed ${formTheme.colors.border};
  `}
  
  /* With text */
  ${props => props.withText && `
    display: flex;
    align-items: center;
    text-align: center;
    
    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1px;
      background-color: ${formTheme.colors.border};
    }
    
    .divider-text {
      padding: 0 ${formTheme.spacing.md};
      color: ${formTheme.colors.textSecondary};
      font-size: 14px;
      background-color: white;
    }
  `}
`;

/**
 * Card-like container for form sections
 * 
 * Provides a card-style container for grouping form elements.
 */
export const FormCard = styled.div`
  background-color: ${formTheme.colors.background};
  border: 1px solid ${formTheme.colors.border};
  border-radius: ${formTheme.borderRadius.md};
  padding: ${formTheme.spacing.lg};
  margin-bottom: ${formTheme.spacing.lg};
  box-shadow: ${formTheme.shadows.sm};
  
  /* Card header */
  .card-header {
    margin-bottom: ${formTheme.spacing.md};
    padding-bottom: ${formTheme.spacing.md};
    border-bottom: 1px solid ${formTheme.colors.border};
    
    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: ${formTheme.colors.text};
      margin: 0;
    }
    
    .card-description {
      font-size: 14px;
      color: ${formTheme.colors.textSecondary};
      margin: ${formTheme.spacing.xs} 0 0 0;
    }
  }
  
  /* Card body */
  .card-body {
    /* Remove margin from last child */
    > *:last-child {
      margin-bottom: 0;
    }
  }
`;

/**
 * Responsive grid system for forms
 * 
 * CSS Grid-based layout system for complex form layouts.
 */
export const FormGrid = styled.div`
  display: grid;
  grid-template-columns: ${props => props.columns || 'repeat(auto-fit, minmax(250px, 1fr))'};
  gap: ${props => props.gap || formTheme.spacing.md};
  
  ${mediaQueries.mobile} {
    grid-template-columns: 1fr;
    gap: ${formTheme.spacing.sm};
  }
`;
