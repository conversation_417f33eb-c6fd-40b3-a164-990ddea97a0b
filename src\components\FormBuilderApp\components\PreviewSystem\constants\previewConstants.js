/**
 * @fileoverview Preview System constants
 *
 * This module contains constants, device configurations, and default values
 * for the professional preview system.
 *
 * @module previewConstants
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

/**
 * Device configurations for preview
 *
 * @constant {Object} DEVICE_CONFIGS
 */
export const DEVICE_CONFIGS = {
  DESKTOP: {
    key: 'desktop',
    name: 'Desktop',
    icon: 'DesktopOutlined',
    width: 1200,
    height: 800,
    scale: 1,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    category: 'desktop',
  },
  LAPTOP: {
    key: 'laptop',
    name: 'Laptop',
    icon: 'LaptopOutlined',
    width: 1024,
    height: 768,
    scale: 0.85,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    category: 'desktop',
  },
  TABLET_LANDSCAPE: {
    key: 'tablet-landscape',
    name: '<PERSON>t (Landscape)',
    icon: 'TabletOutlined',
    width: 1024,
    height: 768,
    scale: 0.7,
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
    category: 'tablet',
  },
  TABLET_PORTRAIT: {
    key: 'tablet-portrait',
    name: 'Tablet (Portrait)',
    icon: 'TabletOutlined',
    width: 768,
    height: 1024,
    scale: 0.6,
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
    category: 'tablet',
  },
  MOBILE_LANDSCAPE: {
    key: 'mobile-landscape',
    name: 'Mobile (Landscape)',
    icon: 'MobileOutlined',
    width: 667,
    height: 375,
    scale: 0.8,
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
    category: 'mobile',
  },
  MOBILE_PORTRAIT: {
    key: 'mobile-portrait',
    name: 'Mobile (Portrait)',
    icon: 'MobileOutlined',
    width: 375,
    height: 667,
    scale: 0.7,
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
    category: 'mobile',
  },
};

/**
 * Preview modes
 *
 * @constant {Object} PREVIEW_MODES
 */
export const PREVIEW_MODES = {
  STATIC: {
    key: 'static',
    name: 'Static Preview',
    description: 'View form layout and styling',
    icon: 'EyeOutlined',
  },
  INTERACTIVE: {
    key: 'interactive',
    name: 'Interactive Test',
    description: 'Test form functionality and interactions',
    icon: 'PlayCircleOutlined',
  },
  VALIDATION: {
    key: 'validation',
    name: 'Validation Test',
    description: 'Test form validation rules and error handling',
    icon: 'CheckCircleOutlined',
  },
  RESPONSIVE: {
    key: 'responsive',
    name: 'Responsive Test',
    description: 'Test responsive behavior across devices',
    icon: 'MobileOutlined',
  },
  PERFORMANCE: {
    key: 'performance',
    name: 'Performance Analysis',
    description: 'Analyze form performance and optimization',
    icon: 'DashboardOutlined',
  },
};

/**
 * Zoom levels for preview
 *
 * @constant {Array} ZOOM_LEVELS
 */
export const ZOOM_LEVELS = [
  { value: 0.25, label: '25%' },
  { value: 0.5, label: '50%' },
  { value: 0.75, label: '75%' },
  { value: 1, label: '100%' },
  { value: 1.25, label: '125%' },
  { value: 1.5, label: '150%' },
  { value: 2, label: '200%' },
];

/**
 * Performance metrics thresholds
 *
 * @constant {Object} PERFORMANCE_THRESHOLDS
 */
export const PERFORMANCE_THRESHOLDS = {
  RENDER_TIME: {
    EXCELLENT: 100, // ms
    GOOD: 300,
    FAIR: 500,
    POOR: 1000,
  },
  BUNDLE_SIZE: {
    EXCELLENT: 50, // KB
    GOOD: 100,
    FAIR: 200,
    POOR: 500,
  },
  COMPONENT_COUNT: {
    EXCELLENT: 10,
    GOOD: 25,
    FAIR: 50,
    POOR: 100,
  },
  MEMORY_USAGE: {
    EXCELLENT: 10, // MB
    GOOD: 25,
    FAIR: 50,
    POOR: 100,
  },
};

/**
 * Validation test scenarios
 *
 * @constant {Object} VALIDATION_SCENARIOS
 */
export const VALIDATION_SCENARIOS = {
  EMPTY_FORM: {
    name: 'Empty Form Submission',
    description: 'Test required field validation',
    data: {},
  },
  INVALID_EMAIL: {
    name: 'Invalid Email Format',
    description: 'Test email validation',
    data: { email: 'invalid-email' },
  },
  INVALID_PHONE: {
    name: 'Invalid Phone Number',
    description: 'Test phone number validation',
    data: { phone: '123' },
  },
  MINIMUM_LENGTH: {
    name: 'Minimum Length Validation',
    description: 'Test minimum length requirements',
    data: { password: '123' },
  },
  MAXIMUM_LENGTH: {
    name: 'Maximum Length Validation',
    description: 'Test maximum length limits',
    data: { description: 'a'.repeat(1000) },
  },
  PATTERN_MISMATCH: {
    name: 'Pattern Validation',
    description: 'Test regex pattern validation',
    data: { zipCode: 'invalid' },
  },
  NUMERIC_VALIDATION: {
    name: 'Numeric Validation',
    description: 'Test numeric field validation',
    data: { age: 'not-a-number' },
  },
  DATE_VALIDATION: {
    name: 'Date Validation',
    description: 'Test date field validation',
    data: { birthDate: 'invalid-date' },
  },
};

/**
 * Interactive test scenarios
 *
 * @constant {Object} INTERACTIVE_SCENARIOS
 */
export const INTERACTIVE_SCENARIOS = {
  FORM_SUBMISSION: {
    name: 'Form Submission Flow',
    description: 'Test complete form submission process',
    steps: [
      'Fill all required fields',
      'Submit form',
      'Verify success message',
      'Check data handling',
    ],
  },
  FIELD_INTERACTIONS: {
    name: 'Field Interactions',
    description: 'Test individual field behaviors',
    steps: [
      'Focus/blur events',
      'Input validation',
      'Error display',
      'Help text visibility',
    ],
  },
  CONDITIONAL_LOGIC: {
    name: 'Conditional Logic',
    description: 'Test conditional field visibility',
    steps: [
      'Trigger conditions',
      'Verify field visibility',
      'Test dependent validations',
      'Check form state',
    ],
  },
  MULTI_STEP_NAVIGATION: {
    name: 'Multi-step Navigation',
    description: 'Test step-by-step form navigation',
    steps: [
      'Navigate between steps',
      'Validate step completion',
      'Test back/forward buttons',
      'Check progress indicators',
    ],
  },
};

/**
 * Animation configurations for preview
 *
 * @constant {Object} PREVIEW_ANIMATIONS
 */
export const PREVIEW_ANIMATIONS = {
  DEVICE_SWITCH: {
    duration: 0.4,
    ease: 'easeInOut',
    scale: [1, 0.95, 1],
    opacity: [1, 0.8, 1],
  },
  ZOOM_CHANGE: {
    duration: 0.3,
    ease: 'easeOut',
  },
  MODE_TRANSITION: {
    duration: 0.2,
    ease: 'easeInOut',
    opacity: [0, 1],
  },
  TOOLBAR_SLIDE: {
    duration: 0.3,
    ease: 'easeOut',
    y: [-20, 0],
    opacity: [0, 1],
  },
};

/**
 * Preview panel dimensions
 *
 * @constant {Object} PANEL_DIMENSIONS
 */
export const PANEL_DIMENSIONS = {
  TOOLBAR_HEIGHT: 60,
  SIDEBAR_WIDTH: 280,
  MIN_PREVIEW_WIDTH: 320,
  MIN_PREVIEW_HEIGHT: 240,
  MAX_ZOOM: 3,
  MIN_ZOOM: 0.1,
};

/**
 * Test data generators
 *
 * @constant {Object} TEST_DATA_GENERATORS
 */
export const TEST_DATA_GENERATORS = {
  PERSONAL_INFO: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    dateOfBirth: '1990-01-15',
  },
  ADDRESS: {
    street: '123 Main Street',
    city: 'New York',
    state: 'NY',
    zipCode: '10001',
    country: 'United States',
  },
  BUSINESS: {
    companyName: 'Acme Corporation',
    industry: 'Technology',
    employees: '50-100',
    revenue: '$1M-$5M',
    website: 'https://acme.com',
  },
  LOREM_IPSUM: {
    short: 'Lorem ipsum dolor sit amet.',
    medium: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    long: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
  },
};
