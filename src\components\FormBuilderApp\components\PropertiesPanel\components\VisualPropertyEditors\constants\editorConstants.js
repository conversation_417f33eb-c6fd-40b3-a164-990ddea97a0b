/**
 * @fileoverview Visual Property Editor constants
 *
 * This module contains constants, configurations, and default values
 * for the visual property editors system.
 *
 * @module editorConstants
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

/**
 * Property editor types
 *
 * @constant {Object} EDITOR_TYPES
 */
export const EDITOR_TYPES = {
  COLOR: 'color',
  SPACING: 'spacing',
  TYPOGRAPHY: 'typography',
  BORDER: 'border',
  SHADOW: 'shadow',
  LAYOUT: 'layout',
  RESPONSIVE: 'responsive',
  CONDITIONAL: 'conditional',
  VALIDATION: 'validation',
  DATA_SOURCE: 'dataSource',
  ANIMATION: 'animation',
};

/**
 * Property categories for organization
 *
 * @constant {Object} PROPERTY_CATEGORIES
 */
export const PROPERTY_CATEGORIES = {
  APPEARANCE: {
    key: 'appearance',
    label: 'Appearance',
    icon: 'BgColorsOutlined',
    description: 'Visual styling and appearance settings',
  },
  LAYOUT: {
    key: 'layout',
    label: 'Layout',
    icon: 'LayoutOutlined',
    description: 'Positioning and layout properties',
  },
  TYPOGRAPHY: {
    key: 'typography',
    label: 'Typography',
    icon: 'FontSizeOutlined',
    description: 'Text styling and typography settings',
  },
  SPACING: {
    key: 'spacing',
    label: 'Spacing',
    icon: 'BorderOutlined',
    description: 'Margins, padding, and spacing controls',
  },
  BEHAVIOR: {
    key: 'behavior',
    label: 'Behavior',
    icon: 'SettingOutlined',
    description: 'Interactive behavior and logic',
  },
  DATA: {
    key: 'data',
    label: 'Data',
    icon: 'DatabaseOutlined',
    description: 'Data binding and validation',
  },
  RESPONSIVE: {
    key: 'responsive',
    label: 'Responsive',
    icon: 'MobileOutlined',
    description: 'Responsive design settings',
  },
  ADVANCED: {
    key: 'advanced',
    label: 'Advanced',
    icon: 'ExperimentOutlined',
    description: 'Advanced configuration options',
  },
};

/**
 * Color palette presets
 *
 * @constant {Object} COLOR_PRESETS
 */
export const COLOR_PRESETS = {
  PRIMARY: [
    '#1890ff', '#0066cc', '#003d7a', '#e6f2ff', '#f0f8ff',
  ],
  SEMANTIC: [
    '#52c41a', '#faad14', '#ff4d4f', '#722ed1', '#13c2c2',
  ],
  NEUTRAL: [
    '#ffffff', '#fafafa', '#f5f5f5', '#e8e8e8', '#d9d9d9',
    '#bfbfbf', '#8c8c8c', '#595959', '#262626', '#000000',
  ],
  GRADIENTS: [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  ],
};

/**
 * Spacing scale values
 *
 * @constant {Object} SPACING_SCALE
 */
export const SPACING_SCALE = {
  NONE: { value: 0, label: 'None' },
  XS: { value: 4, label: 'XS (4px)' },
  SM: { value: 8, label: 'SM (8px)' },
  MD: { value: 16, label: 'MD (16px)' },
  LG: { value: 24, label: 'LG (24px)' },
  XL: { value: 32, label: 'XL (32px)' },
  XXL: { value: 48, label: 'XXL (48px)' },
  CUSTOM: { value: null, label: 'Custom' },
};

/**
 * Typography presets
 *
 * @constant {Object} TYPOGRAPHY_PRESETS
 */
export const TYPOGRAPHY_PRESETS = {
  HEADINGS: [
    { size: 32, weight: 700, lineHeight: 1.2, label: 'Heading 1' },
    { size: 24, weight: 600, lineHeight: 1.3, label: 'Heading 2' },
    { size: 20, weight: 600, lineHeight: 1.4, label: 'Heading 3' },
    { size: 16, weight: 600, lineHeight: 1.4, label: 'Heading 4' },
  ],
  BODY: [
    { size: 16, weight: 400, lineHeight: 1.6, label: 'Body Large' },
    { size: 14, weight: 400, lineHeight: 1.6, label: 'Body Medium' },
    { size: 12, weight: 400, lineHeight: 1.5, label: 'Body Small' },
  ],
  SPECIAL: [
    { size: 18, weight: 500, lineHeight: 1.4, label: 'Lead Text' },
    { size: 11, weight: 500, lineHeight: 1.4, label: 'Caption' },
    { size: 10, weight: 600, lineHeight: 1.2, label: 'Label' },
  ],
};

/**
 * Border radius presets
 *
 * @constant {Object} BORDER_RADIUS_PRESETS
 */
export const BORDER_RADIUS_PRESETS = {
  NONE: { value: 0, label: 'None' },
  SM: { value: 4, label: 'Small' },
  MD: { value: 8, label: 'Medium' },
  LG: { value: 12, label: 'Large' },
  XL: { value: 16, label: 'Extra Large' },
  ROUND: { value: 9999, label: 'Round' },
};

/**
 * Shadow presets
 *
 * @constant {Object} SHADOW_PRESETS
 */
export const SHADOW_PRESETS = {
  NONE: { value: 'none', label: 'None' },
  SM: { value: '0 1px 2px rgba(0, 0, 0, 0.05)', label: 'Small' },
  MD: { value: '0 4px 6px rgba(0, 0, 0, 0.1)', label: 'Medium' },
  LG: { value: '0 10px 15px rgba(0, 0, 0, 0.1)', label: 'Large' },
  XL: { value: '0 20px 25px rgba(0, 0, 0, 0.1)', label: 'Extra Large' },
  INNER: { value: 'inset 0 2px 4px rgba(0, 0, 0, 0.06)', label: 'Inner' },
};

/**
 * Animation presets
 *
 * @constant {Object} ANIMATION_PRESETS
 */
export const ANIMATION_PRESETS = {
  ENTRANCE: [
    { name: 'fadeIn', duration: 0.3, easing: 'ease-out' },
    { name: 'slideInUp', duration: 0.3, easing: 'ease-out' },
    { name: 'slideInDown', duration: 0.3, easing: 'ease-out' },
    { name: 'slideInLeft', duration: 0.3, easing: 'ease-out' },
    { name: 'slideInRight', duration: 0.3, easing: 'ease-out' },
    { name: 'zoomIn', duration: 0.3, easing: 'ease-out' },
  ],
  HOVER: [
    { name: 'scale', scale: 1.05, duration: 0.2 },
    { name: 'lift', translateY: -2, duration: 0.2 },
    { name: 'glow', boxShadow: '0 0 20px rgba(24, 144, 255, 0.3)', duration: 0.2 },
  ],
  LOADING: [
    { name: 'pulse', duration: 1.5, iteration: 'infinite' },
    { name: 'spin', duration: 1, iteration: 'infinite' },
    { name: 'bounce', duration: 0.6, iteration: 'infinite' },
  ],
};

/**
 * Responsive breakpoints
 *
 * @constant {Object} RESPONSIVE_BREAKPOINTS
 */
export const RESPONSIVE_BREAKPOINTS = {
  XS: { min: 0, max: 575, label: 'Mobile' },
  SM: { min: 576, max: 767, label: 'Small Tablet' },
  MD: { min: 768, max: 991, label: 'Tablet' },
  LG: { min: 992, max: 1199, label: 'Desktop' },
  XL: { min: 1200, max: 1599, label: 'Large Desktop' },
  XXL: { min: 1600, max: Infinity, label: 'Extra Large' },
};

/**
 * Validation rule types
 *
 * @constant {Object} VALIDATION_TYPES
 */
export const VALIDATION_TYPES = {
  REQUIRED: { key: 'required', label: 'Required', type: 'boolean' },
  MIN_LENGTH: { key: 'minLength', label: 'Minimum Length', type: 'number' },
  MAX_LENGTH: { key: 'maxLength', label: 'Maximum Length', type: 'number' },
  PATTERN: { key: 'pattern', label: 'Pattern (Regex)', type: 'string' },
  EMAIL: { key: 'email', label: 'Email Format', type: 'boolean' },
  URL: { key: 'url', label: 'URL Format', type: 'boolean' },
  NUMBER: { key: 'number', label: 'Number Only', type: 'boolean' },
  MIN_VALUE: { key: 'min', label: 'Minimum Value', type: 'number' },
  MAX_VALUE: { key: 'max', label: 'Maximum Value', type: 'number' },
  CUSTOM: { key: 'custom', label: 'Custom Validation', type: 'function' },
};

/**
 * Conditional logic operators
 *
 * @constant {Object} LOGIC_OPERATORS
 */
export const LOGIC_OPERATORS = {
  EQUALS: { key: 'equals', label: 'Equals', symbol: '=' },
  NOT_EQUALS: { key: 'notEquals', label: 'Not Equals', symbol: '≠' },
  GREATER_THAN: { key: 'greaterThan', label: 'Greater Than', symbol: '>' },
  LESS_THAN: { key: 'lessThan', label: 'Less Than', symbol: '<' },
  CONTAINS: { key: 'contains', label: 'Contains', symbol: '⊃' },
  NOT_CONTAINS: { key: 'notContains', label: 'Not Contains', symbol: '⊅' },
  IS_EMPTY: { key: 'isEmpty', label: 'Is Empty', symbol: '∅' },
  IS_NOT_EMPTY: { key: 'isNotEmpty', label: 'Is Not Empty', symbol: '≠∅' },
};

/**
 * Data source types
 *
 * @constant {Object} DATA_SOURCE_TYPES
 */
export const DATA_SOURCE_TYPES = {
  STATIC: { key: 'static', label: 'Static Data', icon: 'FileTextOutlined' },
  API: { key: 'api', label: 'API Endpoint', icon: 'ApiOutlined' },
  DATABASE: { key: 'database', label: 'Database Query', icon: 'DatabaseOutlined' },
  FORM_DATA: { key: 'formData', label: 'Form Data', icon: 'FormOutlined' },
  LOCAL_STORAGE: { key: 'localStorage', label: 'Local Storage', icon: 'SaveOutlined' },
  SESSION_STORAGE: { key: 'sessionStorage', label: 'Session Storage', icon: 'ClockCircleOutlined' },
};
