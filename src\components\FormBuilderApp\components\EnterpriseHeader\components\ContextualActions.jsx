/**
 * @fileoverview Contextual Actions component
 *
 * Smart action visibility system that shows relevant tools based on current context
 * (building vs previewing) with progressive disclosure of advanced features.
 *
 * @module ContextualActions
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useMemo } from 'react';
import { Button, Space, Tooltip, Dropdown, Badge } from 'antd';
import { motion, AnimatePresence } from 'framer-motion';
import {
  EyeOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  EditOutlined,
  BarChartOutlined,
  SettingOutlined,
  ShareAltOutlined,
  MoreOutlined,
  MobileOutlined,
  DesktopOutlined,
  TabletOutlined,
  BugOutlined,
  RocketOutlined,
} from '@ant-design/icons';

/**
 * Contextual Actions component
 *
 * @param {Object} props - Component props
 * @param {string} props.activeTab - Currently active tab
 * @param {Function} props.onPreview - Preview action handler
 * @param {Function} props.onTest - Test action handler
 * @param {Function} props.onValidate - Validate action handler
 * @param {Function} props.onEdit - Edit action handler
 * @param {Function} props.onAnalytics - Analytics action handler
 * @param {Function} props.onSettings - Settings action handler
 * @param {Function} props.onShare - Share action handler
 * @param {Object} props.formMetrics - Form metrics for context
 * @returns {React.ReactNode} Contextual actions JSX
 */
const ContextualActions = ({
  activeTab,
  onPreview,
  onTest,
  onValidate,
  onEdit,
  onAnalytics,
  onSettings,
  onShare,
  formMetrics,
}) => {
  // Builder mode actions - Only essential ones
  const builderActions = useMemo(
    () => [
      {
        key: 'preview',
        icon: <EyeOutlined />,
        label: 'Preview',
        tooltip: 'Preview form',
        onClick: onPreview,
        type: 'primary',
        priority: 'high',
      },
    ],
    [onPreview],
  );

  // Preview mode actions - Only essential ones
  const previewActions = useMemo(
    () => [
      {
        key: 'edit',
        icon: <EditOutlined />,
        label: 'Edit',
        tooltip: 'Switch to builder mode',
        onClick: onEdit,
        type: 'primary',
        priority: 'high',
      },
    ],
    [onEdit],
  );

  // Dropdown actions - All secondary actions moved here
  const dropdownActions = useMemo(
    () => [
      // Context-specific actions
      ...(activeTab === 'builder'
        ? [
            {
              key: 'test',
              icon: <PlayCircleOutlined />,
              label: 'Test Form',
              onClick: onTest,
            },
            {
              key: 'validate',
              icon: <CheckCircleOutlined />,
              label: 'Validate',
              onClick: onValidate,
            },
          ]
        : [
            {
              key: 'test-submit',
              icon: <RocketOutlined />,
              label: 'Test Submit',
              onClick: () => console.log('Testing form submission...'),
            },
          ]),
      { type: 'divider' },
      // Device preview
      {
        key: 'device-preview',
        icon: <MobileOutlined />,
        label: 'Device Preview',
        children: [
          {
            key: 'mobile',
            icon: <MobileOutlined />,
            label: 'Mobile',
            onClick: () => console.log('Mobile preview'),
          },
          {
            key: 'tablet',
            icon: <TabletOutlined />,
            label: 'Tablet',
            onClick: () => console.log('Tablet preview'),
          },
          {
            key: 'desktop',
            icon: <DesktopOutlined />,
            label: 'Desktop',
            onClick: () => console.log('Desktop preview'),
          },
        ],
      },
      { type: 'divider' },
      // Advanced actions
      {
        key: 'analytics',
        icon: <BarChartOutlined />,
        label: 'Analytics',
        onClick: onAnalytics,
      },
      {
        key: 'share',
        icon: <ShareAltOutlined />,
        label: 'Share',
        onClick: onShare,
      },
      {
        key: 'settings',
        icon: <SettingOutlined />,
        label: 'Settings',
        onClick: onSettings,
      },
    ],
    [activeTab, onTest, onValidate, onAnalytics, onSettings, onShare],
  );

  // Get current actions based on active tab - Only primary actions now
  const primaryActions =
    activeTab === 'builder' ? builderActions : previewActions;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.2,
        staggerChildren: 0.05,
      },
    },
    exit: { opacity: 0, x: -20 },
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { opacity: 1, scale: 1 },
  };

  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key={`${activeTab}-actions`}
        variants={containerVariants}
        initial='hidden'
        animate='visible'
        exit='exit'
        style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
      >
        {/* Primary Action - Only one essential button */}
        {primaryActions.map((action) => (
          <motion.div key={action.key} variants={itemVariants}>
            <Tooltip title={action.tooltip}>
              <Button
                type={action.type}
                icon={action.icon}
                onClick={action.onClick}
                aria-label={action.tooltip}
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    action.onClick();
                  }
                }}
                style={{
                  height: '36px',
                  borderRadius: '8px',
                  fontWeight: 500,
                }}
              >
                {action.label}
              </Button>
            </Tooltip>
          </motion.div>
        ))}

        {/* More Actions Dropdown - All other actions */}
        <motion.div variants={itemVariants}>
          <Dropdown
            menu={{
              items: dropdownActions,
            }}
            placement='bottomRight'
            trigger={['click']}
          >
            <Button
              icon={<MoreOutlined />}
              aria-label='More actions'
              aria-haspopup='menu'
              tabIndex={0}
              style={{
                height: '36px',
                borderRadius: '8px',
                border: 'none',
                background: 'transparent',
                color: '#666',
              }}
            />
          </Dropdown>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ContextualActions;
