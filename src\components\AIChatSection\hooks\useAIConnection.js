/**
 * useAIConnection Hook
 * 
 * Custom hook for managing AI service connection state and testing.
 * Handles connection status, error states, and automatic connection testing.
 * 
 * Features:
 * - Automatic connection testing on mount
 * - Connection status management
 * - Error handling and reporting
 * - Retry mechanisms for failed connections
 * 
 * @returns {Object} Connection state and utilities
 */

import { useState, useEffect, useCallback } from 'react';
import { testAIConnection } from '../../../services/aiService';

/**
 * Custom hook for AI connection management
 * 
 * @returns {Object} Object containing:
 *   - aiConnected: Boolean indicating connection status
 *   - connectionError: String with error message if connection failed
 *   - isTestingConnection: <PERSON><PERSON>an indicating if connection test is in progress
 *   - retryConnection: Function to manually retry connection
 */
export const useAIConnection = () => {
  // Connection state management
  const [aiConnected, setAiConnected] = useState(false);
  const [connectionError, setConnectionError] = useState(null);
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  /**
   * Tests the AI service connection
   * Updates connection state based on test results
   */
  const testConnection = useCallback(async () => {
    setIsTestingConnection(true);
    setConnectionError(null);

    try {
      console.log('🔍 [useAIConnection] Testing AI service connection...');
      const result = await testAIConnection();
      
      if (result.success) {
        console.log('✅ [useAIConnection] AI connection successful');
        setAiConnected(true);
        setConnectionError(null);
      } else {
        console.warn('⚠️ [useAIConnection] AI connection failed:', result.error);
        setAiConnected(false);
        setConnectionError(result.error || 'Connection test failed');
      }
    } catch (error) {
      console.error('❌ [useAIConnection] AI connection test error:', error);
      setAiConnected(false);
      setConnectionError(error.message || 'Connection test failed');
    } finally {
      setIsTestingConnection(false);
    }
  }, []);

  /**
   * Retry connection with exponential backoff
   * Useful for handling temporary network issues
   */
  const retryConnection = useCallback(async (maxRetries = 3, baseDelay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`🔄 [useAIConnection] Connection retry attempt ${attempt}/${maxRetries}`);
      
      await testConnection();
      
      // If connection successful, break out of retry loop
      if (aiConnected) {
        console.log('✅ [useAIConnection] Connection retry successful');
        break;
      }
      
      // If not the last attempt, wait before retrying
      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
        console.log(`⏳ [useAIConnection] Waiting ${delay}ms before next retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }, [testConnection, aiConnected]);

  /**
   * Test connection on component mount
   * Ensures connection status is known when component loads
   */
  useEffect(() => {
    console.log('🚀 [useAIConnection] Initializing AI connection test...');
    testConnection();
  }, [testConnection]);

  /**
   * Periodic connection health check
   * Runs every 5 minutes to ensure connection remains stable
   */
  useEffect(() => {
    const healthCheckInterval = setInterval(() => {
      if (aiConnected) {
        console.log('🔍 [useAIConnection] Running periodic health check...');
        testConnection();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(healthCheckInterval);
    };
  }, [aiConnected, testConnection]);

  /**
   * Connection status logging for debugging
   */
  useEffect(() => {
    console.log(`📊 [useAIConnection] Connection status changed: ${aiConnected ? 'CONNECTED' : 'DISCONNECTED'}`);
    if (connectionError) {
      console.error(`❌ [useAIConnection] Connection error: ${connectionError}`);
    }
  }, [aiConnected, connectionError]);

  return {
    // Connection state
    aiConnected,
    connectionError,
    isTestingConnection,
    
    // Connection utilities
    testConnection,
    retryConnection,
    
    // Connection status helpers
    isConnected: aiConnected && !connectionError,
    hasError: !!connectionError,
    
    // Status text for UI display
    statusText: aiConnected 
      ? 'Ready to help build forms' 
      : connectionError 
        ? 'Connection required' 
        : 'Connecting...',
    
    // Status color for UI indicators
    statusColor: aiConnected ? '#10b981' : '#f59e0b',
  };
};
