/**
 * @fileoverview Component Management constants
 *
 * This module contains constants, configurations, and default values
 * for the advanced component management system.
 *
 * @module managementConstants
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

/**
 * Component categories for organization
 *
 * @constant {Object} COMPONENT_CATEGORIES
 */
export const COMPONENT_CATEGORIES = {
  DATA_ENTRY: {
    key: 'dataEntry',
    label: 'Data Entry',
    icon: 'FormOutlined',
    color: '#1890ff',
    description: 'Input fields and form controls',
  },
  LAYOUT: {
    key: 'layout',
    label: 'Layout',
    icon: 'LayoutOutlined',
    color: '#722ed1',
    description: 'Containers and layout components',
  },
  DISPLAY: {
    key: 'display',
    label: 'Display',
    icon: 'EyeOutlined',
    color: '#13c2c2',
    description: 'Text, images, and display elements',
  },
  NAVIGATION: {
    key: 'navigation',
    label: 'Navigation',
    icon: 'MenuOutlined',
    color: '#52c41a',
    description: 'Tabs, steps, and navigation elements',
  },
  FEEDBACK: {
    key: 'feedback',
    label: 'Feedback',
    icon: 'MessageOutlined',
    color: '#faad14',
    description: 'Alerts, notifications, and feedback',
  },
  UTILITY: {
    key: 'utility',
    label: 'Utility',
    icon: 'ToolOutlined',
    color: '#f5222d',
    description: 'Dividers, spacers, and utility components',
  },
};

/**
 * Component usage metrics types
 *
 * @constant {Object} USAGE_METRICS
 */
export const USAGE_METRICS = {
  TOTAL_USAGE: {
    key: 'totalUsage',
    label: 'Total Usage',
    description: 'Number of times component is used across all forms',
  },
  RECENT_USAGE: {
    key: 'recentUsage',
    label: 'Recent Usage',
    description: 'Usage in the last 30 days',
  },
  POPULARITY_SCORE: {
    key: 'popularityScore',
    label: 'Popularity Score',
    description: 'Calculated popularity based on usage patterns',
  },
  ERROR_RATE: {
    key: 'errorRate',
    label: 'Error Rate',
    description: 'Percentage of forms with validation errors on this component',
  },
  COMPLETION_RATE: {
    key: 'completionRate',
    label: 'Completion Rate',
    description: 'Percentage of users who complete forms with this component',
  },
};

/**
 * Search filter types
 *
 * @constant {Object} SEARCH_FILTERS
 */
export const SEARCH_FILTERS = {
  CATEGORY: {
    key: 'category',
    label: 'Category',
    type: 'select',
    options: Object.values(COMPONENT_CATEGORIES),
  },
  TYPE: {
    key: 'type',
    label: 'Component Type',
    type: 'select',
    options: [], // Populated dynamically
  },
  USAGE: {
    key: 'usage',
    label: 'Usage Frequency',
    type: 'range',
    min: 0,
    max: 100,
  },
  COMPLEXITY: {
    key: 'complexity',
    label: 'Complexity Level',
    type: 'select',
    options: [
      { key: 'simple', label: 'Simple', value: 1 },
      { key: 'moderate', label: 'Moderate', value: 2 },
      { key: 'complex', label: 'Complex', value: 3 },
      { key: 'advanced', label: 'Advanced', value: 4 },
    ],
  },
  STATUS: {
    key: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { key: 'active', label: 'Active', value: 'active' },
      { key: 'deprecated', label: 'Deprecated', value: 'deprecated' },
      { key: 'experimental', label: 'Experimental', value: 'experimental' },
    ],
  },
};

/**
 * Template categories
 *
 * @constant {Object} TEMPLATE_CATEGORIES
 */
export const TEMPLATE_CATEGORIES = {
  FORMS: {
    key: 'forms',
    label: 'Complete Forms',
    icon: 'FileTextOutlined',
    description: 'Ready-to-use form templates',
  },
  SECTIONS: {
    key: 'sections',
    label: 'Form Sections',
    icon: 'BlockOutlined',
    description: 'Reusable form sections and groups',
  },
  PATTERNS: {
    key: 'patterns',
    label: 'UI Patterns',
    icon: 'AppstoreOutlined',
    description: 'Common UI patterns and layouts',
  },
  CUSTOM: {
    key: 'custom',
    label: 'Custom Templates',
    icon: 'StarOutlined',
    description: 'User-created custom templates',
  },
};

/**
 * Hierarchy tree node types
 *
 * @constant {Object} TREE_NODE_TYPES
 */
export const TREE_NODE_TYPES = {
  ROOT: {
    key: 'root',
    label: 'Form Root',
    icon: 'HomeOutlined',
    color: '#1890ff',
  },
  CONTAINER: {
    key: 'container',
    label: 'Container',
    icon: 'FolderOutlined',
    color: '#722ed1',
  },
  COMPONENT: {
    key: 'component',
    label: 'Component',
    icon: 'ComponentOutlined',
    color: '#52c41a',
  },
  GROUP: {
    key: 'group',
    label: 'Group',
    icon: 'GroupOutlined',
    color: '#faad14',
  },
};

/**
 * Analytics time periods
 *
 * @constant {Object} ANALYTICS_PERIODS
 */
export const ANALYTICS_PERIODS = {
  LAST_7_DAYS: {
    key: 'last7days',
    label: 'Last 7 Days',
    days: 7,
  },
  LAST_30_DAYS: {
    key: 'last30days',
    label: 'Last 30 Days',
    days: 30,
  },
  LAST_90_DAYS: {
    key: 'last90days',
    label: 'Last 90 Days',
    days: 90,
  },
  LAST_YEAR: {
    key: 'lastyear',
    label: 'Last Year',
    days: 365,
  },
  ALL_TIME: {
    key: 'alltime',
    label: 'All Time',
    days: null,
  },
};

/**
 * Component complexity scoring
 *
 * @constant {Object} COMPLEXITY_SCORING
 */
export const COMPLEXITY_SCORING = {
  SIMPLE: {
    score: 1,
    label: 'Simple',
    color: '#52c41a',
    criteria: 'Basic components with minimal configuration',
  },
  MODERATE: {
    score: 2,
    label: 'Moderate',
    color: '#faad14',
    criteria: 'Components with some configuration options',
  },
  COMPLEX: {
    score: 3,
    label: 'Complex',
    color: '#ff7a45',
    criteria: 'Components with advanced features and validation',
  },
  ADVANCED: {
    score: 4,
    label: 'Advanced',
    color: '#f5222d',
    criteria: 'Highly configurable components with complex logic',
  },
};

/**
 * Search operators for advanced search
 *
 * @constant {Object} SEARCH_OPERATORS
 */
export const SEARCH_OPERATORS = {
  CONTAINS: {
    key: 'contains',
    label: 'Contains',
    symbol: '~',
  },
  EQUALS: {
    key: 'equals',
    label: 'Equals',
    symbol: '=',
  },
  STARTS_WITH: {
    key: 'startsWith',
    label: 'Starts With',
    symbol: '^',
  },
  ENDS_WITH: {
    key: 'endsWith',
    label: 'Ends With',
    symbol: '$',
  },
  GREATER_THAN: {
    key: 'greaterThan',
    label: 'Greater Than',
    symbol: '>',
  },
  LESS_THAN: {
    key: 'lessThan',
    label: 'Less Than',
    symbol: '<',
  },
};

/**
 * Component status types
 *
 * @constant {Object} COMPONENT_STATUS
 */
export const COMPONENT_STATUS = {
  ACTIVE: {
    key: 'active',
    label: 'Active',
    color: '#52c41a',
    description: 'Component is actively used and maintained',
  },
  DEPRECATED: {
    key: 'deprecated',
    label: 'Deprecated',
    color: '#faad14',
    description: 'Component is deprecated and should be replaced',
  },
  EXPERIMENTAL: {
    key: 'experimental',
    label: 'Experimental',
    color: '#1890ff',
    description: 'Component is in experimental phase',
  },
  DISABLED: {
    key: 'disabled',
    label: 'Disabled',
    color: '#d9d9d9',
    description: 'Component is disabled and cannot be used',
  },
};

/**
 * Default analytics data structure
 *
 * @constant {Object} DEFAULT_ANALYTICS
 */
export const DEFAULT_ANALYTICS = {
  totalComponents: 0,
  activeComponents: 0,
  mostUsedComponents: [],
  categoryDistribution: {},
  usageTrends: [],
  performanceMetrics: {
    averageRenderTime: 0,
    memoryUsage: 0,
    errorRate: 0,
  },
};

/**
 * Animation configurations for component management
 *
 * @constant {Object} MANAGEMENT_ANIMATIONS
 */
export const MANAGEMENT_ANIMATIONS = {
  TREE_EXPAND: {
    hidden: {
      opacity: 0,
      x: 300,
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    exit: {
      opacity: 0,
      x: 300,
      transition: { duration: 0.3, ease: 'easeOut' },
    },
  },
  SEARCH_RESULTS: {
    hidden: {
      opacity: 0,
      y: -10,
      transition: { duration: 0.2, ease: 'easeInOut' },
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.2, ease: 'easeInOut' },
    },
    exit: {
      opacity: 0,
      y: -10,
      transition: { duration: 0.2, ease: 'easeInOut' },
    },
  },
  ANALYTICS_UPDATE: {
    hidden: {
      opacity: 0,
      scale: 0.95,
      transition: { duration: 0.4, ease: 'easeOut' },
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.4, ease: 'easeOut' },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      transition: { duration: 0.4, ease: 'easeOut' },
    },
  },
  TEMPLATE_PREVIEW: {
    hidden: {
      opacity: 0,
      scale: 0.9,
      transition: { duration: 0.3, ease: 'easeInOut' },
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3, ease: 'easeInOut' },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: { duration: 0.3, ease: 'easeInOut' },
    },
  },
};
