/**
 * @fileoverview Properties Panel constants and configuration
 *
 * This module provides constants, configuration values, and enums
 * used throughout the properties panel component.
 *
 * @module propertiesPanelConstants
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

/**
 * Panel tab configuration constants
 *
 * @constant {Object} PANEL_TABS
 */
export const PANEL_TABS = {
  PROPERTIES: 'properties',
  ADVANCED: 'advanced',
  ROLES: 'roles',
};

/**
 * Panel positioning priority order
 * Determines the order of position attempts for collision detection
 *
 * @constant {Array} POSITIONING_PRIORITY
 */
export const POSITIONING_PRIORITY = ['top', 'right', 'bottom', 'left'];

/**
 * Responsive design breakpoints
 *
 * @constant {Object} RESPONSIVE_BREAKPOINTS
 */
export const RESPONSIVE_BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1200,
};

/**
 * Z-index levels for proper layering
 *
 * @constant {Object} Z_INDEX_LEVELS
 */
export const Z_INDEX_LEVELS = {
  BACKDROP: 1000,
  PANEL: 1001,
  CARET: 1002,
  LOADING: 1003,
};

/**
 * Panel dimensions and spacing
 *
 * @constant {Object} PANEL_DIMENSIONS
 */
export const PANEL_DIMENSIONS = {
  WIDTH: {
    DESKTOP: 450, // Increased for enterprise workspace needs
    TABLET: 400, // Better tablet experience
    MOBILE: '100%',
  },
  HEIGHT: {
    MAX_DESKTOP: 720, // Increased for more content visibility
    MAX_TABLET: 640, // Better tablet content space
    MOBILE: '80vh', // More mobile screen utilization
  },
  CARET_SIZE: 12, // Reduced for subtle enterprise aesthetic
  OFFSET: 16,
  BORDER_RADIUS: 12,
  SHADOW: '0 12px 40px rgba(0, 0, 0, 0.06), 0 4px 16px rgba(0, 0, 0, 0.03)', // Softer enterprise shadow
};

/**
 * Animation variants for Framer Motion
 *
 * @constant {Object} ANIMATION_VARIANTS
 */
export const ANIMATION_VARIANTS = {
  panel: {
    hidden: {
      opacity: 0,
      scale: 0.95,
      y: 10,
      filter: 'blur(4px)',
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      filter: 'blur(0px)',
      transition: {
        duration: 0.25,
        ease: [0.25, 0.46, 0.45, 0.94], // easeOutQuart
        staggerChildren: 0.05,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: 10,
      filter: 'blur(2px)',
      transition: {
        duration: 0.2,
        ease: [0.55, 0.06, 0.68, 0.19], // easeInQuart
      },
    },
  },
  backdrop: {
    hidden: {
      opacity: 0,
      backdropFilter: 'blur(0px)',
    },
    visible: {
      opacity: 1,
      backdropFilter: 'blur(4px)',
      transition: {
        duration: 0.2,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      backdropFilter: 'blur(0px)',
      transition: {
        duration: 0.15,
        ease: 'easeIn',
      },
    },
  },
  mobileSheet: {
    hidden: {
      y: '100%',
      opacity: 0,
      scale: 0.98,
    },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 300,
        mass: 0.8,
        staggerChildren: 0.03,
      },
    },
    exit: {
      y: '100%',
      opacity: 0,
      scale: 0.98,
      transition: {
        duration: 0.3,
        ease: [0.55, 0.06, 0.68, 0.19], // easeInQuart
      },
    },
  },
  tabContent: {
    hidden: {
      opacity: 0,
      x: 20,
      filter: 'blur(2px)',
    },
    visible: {
      opacity: 1,
      x: 0,
      filter: 'blur(0px)',
      transition: {
        duration: 0.2,
        ease: [0.25, 0.46, 0.45, 0.94],
        staggerChildren: 0.02,
      },
    },
    exit: {
      opacity: 0,
      x: -20,
      filter: 'blur(2px)',
      transition: {
        duration: 0.15,
        ease: [0.55, 0.06, 0.68, 0.19],
      },
    },
  },
  formItem: {
    hidden: {
      opacity: 0,
      y: 10,
      scale: 0.98,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.15,
        ease: 'easeOut',
      },
    },
  },
  loading: {
    hidden: {
      opacity: 0,
      scale: 0.9,
      backdropFilter: 'blur(0px)',
    },
    visible: {
      opacity: 1,
      scale: 1,
      backdropFilter: 'blur(2px)',
      transition: {
        duration: 0.2,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      backdropFilter: 'blur(0px)',
      transition: {
        duration: 0.15,
        ease: 'easeIn',
      },
    },
  },
};

/**
 * Touch target minimum sizes for accessibility
 *
 * @constant {Object} TOUCH_TARGETS
 */
export const TOUCH_TARGETS = {
  MIN_SIZE: 44,
  RECOMMENDED_SIZE: 48,
};

/**
 * Component property categories for organization
 *
 * @constant {Object} PROPERTY_CATEGORIES
 */
export const PROPERTY_CATEGORIES = {
  BASIC: 'basic',
  STYLING: 'styling',
  VALIDATION: 'validation',
  BEHAVIOR: 'behavior',
  ACCESSIBILITY: 'accessibility',
  ADVANCED: 'advanced',
  ROLES: 'roles',
};

/**
 * Default panel configuration
 *
 * @constant {Object} PANEL_DEFAULTS
 */
export const PANEL_DEFAULTS = {
  ACTIVE_TAB: PANEL_TABS.PROPERTIES,
  POSITION: 'top',
  AUTO_CLOSE_DELAY: 0, // 0 means no auto-close
  COLLISION_PADDING: 16,
  ANIMATION_DURATION: 200,
};
