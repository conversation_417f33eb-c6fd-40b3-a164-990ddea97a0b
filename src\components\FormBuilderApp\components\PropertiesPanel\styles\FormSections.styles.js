/**
 * @fileoverview Enhanced form section styling for properties panel
 *
 * Modern, professional styling for form sections with improved
 * visual hierarchy, spacing, and interaction states.
 *
 * @module FormSections.styles
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import styled from 'styled-components';
import { motion } from 'framer-motion';

/**
 * Enhanced form section container
 */
export const FormSection = styled(motion.div)`
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  position: relative;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* Subtle shadow */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);

  &:hover {
    border-color: #e6f7ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.08);
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

/**
 * Section header with icon and title
 */
export const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f5f5f5;

  .section-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border-radius: 8px;
    color: #1890ff;
    font-size: 16px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: -0.01em;
    margin: 0;
  }
`;

/**
 * Enhanced form item styling
 */
export const FormItem = styled.div`
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .ant-form-item {
    margin-bottom: 0;
  }

  .ant-form-item-label {
    padding-bottom: 6px;

    > label {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
      height: auto;

      &::after {
        display: none; /* Remove default colon */
      }
    }
  }

  .ant-form-item-control {
    .ant-input,
    .ant-select-selector,
    .ant-input-number {
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }

      &:focus,
      &.ant-input-focused,
      &.ant-select-focused .ant-select-selector {
        border-color: #1890ff;
        box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.15);
      }
    }

    .ant-select-arrow {
      color: #8c8c8c;
    }
  }
`;

/**
 * Help text styling
 */
export const HelpText = styled.div`
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.4;

  .anticon {
    margin-right: 4px;
    color: #40a9ff;
  }
`;

/**
 * Property group for related settings
 */
export const PropertyGroup = styled.div`
  background: #fafbfc;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;

  .group-title {
    font-size: 13px;
    font-weight: 600;
    color: #595959;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: '';
      width: 3px;
      height: 12px;
      background: #1890ff;
      border-radius: 2px;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

/**
 * Toggle switch styling
 */
export const ToggleRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .toggle-label {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    flex: 1;
  }

  .toggle-description {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 2px;
  }

  .ant-switch {
    &.ant-switch-checked {
      background-color: #1890ff;
    }
  }
`;

/**
 * Validation indicator
 */
export const ValidationIndicator = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  margin-top: 4px;

  &.success {
    color: #52c41a;
  }

  &.error {
    color: #ff4d4f;
  }

  &.warning {
    color: #faad14;
  }
`;

/**
 * Action buttons container
 */
export const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  .ant-btn {
    border-radius: 8px;
    font-weight: 600;
    height: 36px;

    &.ant-btn-primary {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border: none;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

      &:hover {
        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
      }
    }
  }
`;

/**
 * Collapsible section
 */
export const CollapsibleSection = styled.div`
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;

  .collapse-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #fafbfc;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #f0f9ff;
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .anticon {
        color: #1890ff;
      }
    }

    .collapse-arrow {
      transition: transform 0.2s ease;
      color: #8c8c8c;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .collapse-content {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
  }
`;
