import shortid from 'shortid';

// Core component types
export const SIDEBAR_ITEM = 'sidebarItem';
export const ROW = 'row';
export const COLUMN = 'column';
export const COMPONENT = 'component';

// Advanced container component types
export const TAB_CONTAINER = 'tabContainer';
export const CARD_CONTAINER = 'cardContainer';
export const FORM_SECTION = 'formSection';

// Advanced layout component types for complex forms
export const ACCORDION_CONTAINER = 'accordionContainer';
export const COLLAPSE_CONTAINER = 'collapseContainer';
export const STEPS_CONTAINER = 'stepsContainer';
export const GRID_CONTAINER = 'gridContainer';
export const FLEX_CONTAINER = 'flexContainer';
export const MODAL_CONTAINER = 'modalContainer';
export const DRAWER_CONTAINER = 'drawerContainer';

// Component capabilities - defines what each component type can contain
export const COMPONENT_CAPABILITIES = {
  [ROW]: {
    canContain: [COLUMN],
    acceptsFromSidebar: false,
    isContainer: true,
    maxChildren: 12, // Bootstrap-like grid system
    minChildren: 1,
    nestingLevel: 'unlimited',
    allowsRowNesting: false, // Rows cannot contain other rows directly
  },
  [COLUMN]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 100, // Increased for complex forms
    minChildren: 0,
    nestingLevel: 'unlimited',
    allowsRowNesting: true, // Columns can contain rows for complex layouts
  },
  [TAB_CONTAINER]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 100, // Increased for complex forms
    minChildren: 1,
    nestingLevel: 'unlimited',
    hasTabStructure: true,
    allowsRowNesting: true,
    supportsNestedTabs: true, // Tabs can contain other tab containers
  },
  [CARD_CONTAINER]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 100, // Increased for complex forms
    minChildren: 0,
    nestingLevel: 'unlimited',
    allowsRowNesting: true,
    supportsNestedCards: true, // Cards can contain other card containers
  },
  [FORM_SECTION]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 100, // Increased for complex forms
    minChildren: 0,
    nestingLevel: 'unlimited',
    hasConditionalLogic: true,
    allowsRowNesting: true,
    supportsNestedSections: true, // Sections can contain other sections
  },
  [COMPONENT]: {
    canContain: [],
    acceptsFromSidebar: false,
    isContainer: false,
    maxChildren: 0,
    minChildren: 0,
    nestingLevel: 'leaf',
  },
  [ACCORDION_CONTAINER]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 100,
    minChildren: 1,
    nestingLevel: 'unlimited',
    hasAccordionStructure: true,
    allowsRowNesting: true,
    supportsNestedAccordions: true,
  },
  [COLLAPSE_CONTAINER]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 100,
    minChildren: 0,
    nestingLevel: 'unlimited',
    hasCollapseStructure: true,
    allowsRowNesting: true,
  },
  [STEPS_CONTAINER]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 20, // Reasonable limit for steps
    minChildren: 2,
    nestingLevel: 'unlimited',
    hasStepsStructure: true,
    allowsRowNesting: true,
    supportsValidation: true,
  },
  [GRID_CONTAINER]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 100,
    minChildren: 0,
    nestingLevel: 'unlimited',
    hasGridStructure: true,
    allowsRowNesting: true, // Allow rows for maximum flexibility
    supportsResponsiveLayout: true,
  },
  [FLEX_CONTAINER]: {
    canContain: [
      COMPONENT,
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
      ROW,
    ],
    acceptsFromSidebar: true,
    isContainer: true,
    maxChildren: 100,
    minChildren: 0,
    nestingLevel: 'unlimited',
    hasFlexStructure: true,
    allowsRowNesting: true,
    supportsResponsiveLayout: true,
  },
};

export const SIDEBAR_ITEMS = [
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'input',
      content: 'Text Input',
      label: 'Text Input',
      placeholder: 'Enter text here...',
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
        allowClear: true,
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'textarea',
      content: 'Text Area',
      label: 'Text Area',
      placeholder: 'Enter multiple lines of text...',
      validation: {
        required: false,
      },
      styling: {
        rows: 4,
        allowClear: true,
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'email',
      content: 'Email Input',
      label: 'Email Address',
      placeholder: 'Enter email address...',
      validation: {
        required: false,
        pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
        patternMessage: 'Please enter a valid email address',
      },
      styling: {
        size: 'middle',
        allowClear: true,
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'select',
      content: 'Select Dropdown',
      label: 'Select Option',
      placeholder: 'Choose an option...',
      validation: {
        required: false,
      },
      styling: {
        allowClear: true,
        showSearch: true,
      },
      options: [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'radio',
      content: 'Radio Group',
      label: 'Choose One',
      validation: {
        required: false,
      },
      styling: {
        direction: 'vertical',
      },
      options: [
        { value: 'yes', label: 'Yes' },
        { value: 'no', label: 'No' },
        { value: 'maybe', label: 'Maybe' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'checkbox',
      content: 'Checkbox',
      label: 'Checkbox Option',
      text: 'I agree to the terms and conditions',
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'inputnumber',
      content: 'Number Input',
      label: 'Number',
      placeholder: 'Enter a number...',
      validation: {
        required: false,
        min: 0,
        max: 100,
      },
      styling: {
        size: 'middle',
        controls: true,
        precision: 0,
        step: 1,
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'password',
      content: 'Password Input',
      label: 'Password',
      placeholder: 'Enter password...',
      validation: {
        required: false,
        minLength: 6,
        pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,}$',
      },
      styling: {
        size: 'middle',
        visibilityToggle: true,
        iconRender: true,
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'datepicker',
      content: 'Date Picker',
      label: 'Date',
      placeholder: 'Select a date...',
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
        allowClear: true,
        format: 'YYYY-MM-DD',
        showTime: false,
        picker: 'date',
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'rangepicker',
      content: 'Date Range Picker',
      label: 'Date Range',
      placeholder: ['Start date', 'End date'],
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
        allowClear: true,
        format: 'YYYY-MM-DD',
        showTime: false,
        picker: 'date',
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'switch',
      content: 'Switch',
      label: 'Toggle Switch',
      validation: {
        required: false,
      },
      styling: {
        size: 'default',
        checkedChildren: 'ON',
        unCheckedChildren: 'OFF',
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'upload',
      content: 'Upload',
      label: 'File Upload',
      validation: {
        required: false,
        maxCount: 5,
        maxSize: 10, // MB
        accept: '.jpg,.jpeg,.png,.pdf,.doc,.docx',
      },
      styling: {
        listType: 'text',
        multiple: true,
        showUploadList: true,
        directory: false,
      },
      uploadProps: {
        action: '/api/upload',
        method: 'POST',
        withCredentials: false,
      },
    },
  },
  // Advanced container components
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: TAB_CONTAINER,
      content: 'Tabs',
      label: 'Tab Container',
      validation: {
        required: false,
      },
      styling: {
        type: 'line',
        size: 'default',
        tabPosition: 'top',
      },
      tabs: [
        {
          id: shortid.generate(),
          key: 'tab1',
          label: 'Tab 1',
          children: [],
        },
        {
          id: shortid.generate(),
          key: 'tab2',
          label: 'Tab 2',
          children: [],
        },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: CARD_CONTAINER,
      content: 'Card',
      label: 'Card Container',
      validation: {
        required: false,
      },
      styling: {
        size: 'default',
        bordered: true,
        hoverable: false,
      },
      cardProps: {
        title: 'Card Title',
        extra: null,
        actions: [],
      },
      children: [],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: FORM_SECTION,
      content: 'Form Section',
      label: 'Form Section',
      validation: {
        required: false,
      },
      styling: {
        bordered: true,
        collapsible: false,
        defaultCollapsed: false,
      },
      sectionProps: {
        title: 'Section Title',
        description: 'Section description',
      },
      conditionalLogic: {
        enabled: false,
        conditions: [],
      },
      children: [],
    },
  },
  // Advanced Layout Containers
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: ACCORDION_CONTAINER,
      content: 'Accordion',
      label: 'Accordion Container',
      validation: {
        required: false,
      },
      styling: {
        bordered: true,
        ghost: false,
        size: 'default',
      },
      accordionProps: {
        defaultActiveKey: ['1'],
        expandIconPosition: 'start',
        collapsible: 'header',
      },
      panels: [
        {
          id: shortid.generate(),
          key: '1',
          header: 'Panel 1',
          children: [],
        },
      ],
      children: [],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: STEPS_CONTAINER,
      content: 'Steps',
      label: 'Steps Container',
      validation: {
        required: false,
      },
      styling: {
        type: 'default',
        size: 'default',
        direction: 'horizontal',
        labelPlacement: 'horizontal',
      },
      stepsProps: {
        current: 0,
        status: 'process',
        progressDot: false,
      },
      steps: [
        {
          id: shortid.generate(),
          key: 'step1',
          title: 'Step 1',
          description: 'First step',
          children: [],
        },
        {
          id: shortid.generate(),
          key: 'step2',
          title: 'Step 2',
          description: 'Second step',
          children: [],
        },
      ],
      children: [],
    },
  },

  // Additional Data Entry Components
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'autocomplete',
      content: 'AutoComplete',
      label: 'Auto Complete',
      placeholder: 'Type to search...',
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
        allowClear: true,
        backfill: false,
      },
      options: [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'cascader',
      content: 'Cascader',
      label: 'Cascader',
      placeholder: 'Please select...',
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
        allowClear: true,
        showSearch: true,
        multiple: false,
      },
      options: [
        {
          value: 'category1',
          label: 'Category 1',
          children: [
            { value: 'sub1', label: 'Sub Category 1' },
            { value: 'sub2', label: 'Sub Category 2' },
          ],
        },
        {
          value: 'category2',
          label: 'Category 2',
          children: [
            { value: 'sub3', label: 'Sub Category 3' },
            { value: 'sub4', label: 'Sub Category 4' },
          ],
        },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'colorpicker',
      content: 'Color Picker',
      label: 'Color Picker',
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
        showText: true,
        format: 'hex',
        allowClear: true,
      },
      defaultValue: '#1890ff',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'mentions',
      content: 'Mentions',
      label: 'Mentions',
      placeholder: 'Type @ to mention someone...',
      validation: {
        required: false,
      },
      styling: {
        rows: 3,
        allowClear: true,
        autoSize: true,
      },
      options: [
        { value: 'user1', label: 'User 1' },
        { value: 'user2', label: 'User 2' },
        { value: 'user3', label: 'User 3' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'timepicker',
      content: 'Time Picker',
      label: 'Time Picker',
      placeholder: 'Select time...',
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
        allowClear: true,
        format: 'HH:mm:ss',
        use12Hours: false,
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'transfer',
      content: 'Transfer',
      label: 'Transfer',
      validation: {
        required: false,
      },
      styling: {
        showSearch: true,
        showSelectAll: true,
        oneWay: false,
      },
      dataSource: [
        { key: '1', title: 'Item 1', description: 'Description 1' },
        { key: '2', title: 'Item 2', description: 'Description 2' },
        { key: '3', title: 'Item 3', description: 'Description 3' },
        { key: '4', title: 'Item 4', description: 'Description 4' },
      ],
      targetKeys: [],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'treeselect',
      content: 'Tree Select',
      label: 'Tree Select',
      placeholder: 'Please select...',
      validation: {
        required: false,
      },
      styling: {
        size: 'middle',
        allowClear: true,
        showSearch: true,
        multiple: false,
        treeCheckable: false,
      },
      treeData: [
        {
          title: 'Node 1',
          value: 'node1',
          children: [
            { title: 'Child 1', value: 'child1' },
            { title: 'Child 2', value: 'child2' },
          ],
        },
        {
          title: 'Node 2',
          value: 'node2',
          children: [
            { title: 'Child 3', value: 'child3' },
            { title: 'Child 4', value: 'child4' },
          ],
        },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'rate',
      content: 'Rate',
      label: 'Rating',
      validation: {
        required: false,
      },
      styling: {
        count: 5,
        allowHalf: true,
        allowClear: true,
        character: '★',
      },
      tooltips: ['Terrible', 'Bad', 'Normal', 'Good', 'Wonderful'],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'slider',
      content: 'Slider',
      label: 'Slider',
      validation: {
        required: false,
        min: 0,
        max: 100,
      },
      styling: {
        step: 1,
        marks: {
          0: '0',
          25: '25',
          50: '50',
          75: '75',
          100: '100',
        },
        included: true,
        range: false,
      },
      defaultValue: 30,
    },
  },
  // Data Display Components
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'avatar',
      content: 'Avatar',
      label: 'Avatar',
      validation: {
        required: false,
      },
      styling: {
        size: 'default',
        shape: 'circle',
        icon: 'UserOutlined',
      },
      src: '',
      alt: 'Avatar',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'badge',
      content: 'Badge',
      label: 'Badge',
      validation: {
        required: false,
      },
      styling: {
        count: 5,
        showZero: false,
        overflowCount: 99,
        dot: false,
        status: 'default',
      },
      text: 'Badge Text',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'calendar',
      content: 'Calendar',
      label: 'Calendar',
      validation: {
        required: false,
      },
      styling: {
        fullscreen: true,
        mode: 'month',
      },
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'carousel',
      content: 'Carousel',
      label: 'Carousel',
      validation: {
        required: false,
      },
      styling: {
        autoplay: false,
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
      },
      items: [
        { id: 1, content: 'Slide 1', background: '#364d79' },
        { id: 2, content: 'Slide 2', background: '#1890ff' },
        { id: 3, content: 'Slide 3', background: '#722ed1' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'descriptions',
      content: 'Descriptions',
      label: 'Descriptions',
      validation: {
        required: false,
      },
      styling: {
        bordered: false,
        column: 3,
        size: 'default',
        layout: 'horizontal',
      },
      title: 'User Info',
      items: [
        { key: 'name', label: 'Name', children: 'John Doe' },
        { key: 'email', label: 'Email', children: '<EMAIL>' },
        { key: 'phone', label: 'Phone', children: '****** 567 8900' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'empty',
      content: 'Empty',
      label: 'Empty State',
      validation: {
        required: false,
      },
      styling: {
        image: 'default',
        imageStyle: {},
      },
      description: 'No data available',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'image',
      content: 'Image',
      label: 'Image',
      validation: {
        required: false,
      },
      styling: {
        width: 200,
        height: 200,
        preview: true,
        fallback:
          'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN',
      },
      src: 'https://via.placeholder.com/200x200',
      alt: 'Sample Image',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'list',
      content: 'List',
      label: 'List',
      validation: {
        required: false,
      },
      styling: {
        bordered: false,
        split: true,
        size: 'default',
        itemLayout: 'horizontal',
      },
      header: 'List Header',
      footer: 'List Footer',
      dataSource: [
        { id: 1, title: 'Item 1', description: 'Description 1' },
        { id: 2, title: 'Item 2', description: 'Description 2' },
        { id: 3, title: 'Item 3', description: 'Description 3' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'tag',
      content: 'Tag',
      label: 'Tag',
      validation: {
        required: false,
      },
      styling: {
        color: 'default',
        closable: false,
        bordered: true,
      },
      text: 'Sample Tag',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'timeline',
      content: 'Timeline',
      label: 'Timeline',
      validation: {
        required: false,
      },
      styling: {
        mode: 'left',
        pending: false,
        reverse: false,
      },
      items: [
        { color: 'green', children: 'Create a services site 2015-09-01' },
        {
          color: 'green',
          children: 'Solve initial network problems 2015-09-01',
        },
        { color: 'red', children: 'Technical testing 2015-09-01' },
        { color: 'blue', children: 'Network problems being solved 2015-09-01' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'tree',
      content: 'Tree',
      label: 'Tree',
      validation: {
        required: false,
      },
      styling: {
        showLine: false,
        showIcon: false,
        checkable: false,
        selectable: true,
        multiple: false,
      },
      treeData: [
        {
          title: 'Parent 1',
          key: '0-0',
          children: [
            { title: 'Child 1', key: '0-0-0' },
            { title: 'Child 2', key: '0-0-1' },
          ],
        },
        {
          title: 'Parent 2',
          key: '0-1',
          children: [
            { title: 'Child 3', key: '0-1-0' },
            { title: 'Child 4', key: '0-1-1' },
          ],
        },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'table',
      content: 'Table',
      label: 'Table',
      validation: {
        required: false,
      },
      styling: {
        bordered: false,
        size: 'default',
        pagination: true,
        scroll: {},
      },
      columns: [
        { title: 'Name', dataIndex: 'name', key: 'name' },
        { title: 'Age', dataIndex: 'age', key: 'age' },
        { title: 'Address', dataIndex: 'address', key: 'address' },
      ],
      dataSource: [
        {
          key: '1',
          name: 'John Brown',
          age: 32,
          address: 'New York No. 1 Lake Park',
        },
        {
          key: '2',
          name: 'Jim Green',
          age: 42,
          address: 'London No. 1 Lake Park',
        },
        {
          key: '3',
          name: 'Joe Black',
          age: 32,
          address: 'Sidney No. 1 Lake Park',
        },
      ],
    },
  },
  // Feedback Components
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'alert',
      content: 'Alert',
      label: 'Alert',
      validation: {
        required: false,
      },
      styling: {
        type: 'info',
        showIcon: true,
        closable: false,
        banner: false,
      },
      message: 'Info Alert',
      description: 'This is an informational alert message.',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'progress',
      content: 'Progress',
      label: 'Progress',
      validation: {
        required: false,
      },
      styling: {
        type: 'line',
        status: 'active',
        showInfo: true,
        strokeColor: '#1890ff',
        strokeWidth: 6,
      },
      percent: 30,
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'skeleton',
      content: 'Skeleton',
      label: 'Skeleton',
      validation: {
        required: false,
      },
      styling: {
        active: true,
        avatar: true,
        paragraph: { rows: 4 },
        title: true,
      },
      loading: true,
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'spin',
      content: 'Spin',
      label: 'Loading Spinner',
      validation: {
        required: false,
      },
      styling: {
        size: 'default',
        spinning: true,
      },
      tip: 'Loading...',
    },
  },
  // Navigation Components
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'breadcrumb',
      content: 'Breadcrumb',
      label: 'Breadcrumb',
      validation: {
        required: false,
      },
      styling: {
        separator: '/',
      },
      items: [
        { title: 'Home', href: '/' },
        { title: 'Application Center', href: '/apps' },
        { title: 'Application List', href: '/apps/list' },
        { title: 'An Application' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'menu',
      content: 'Menu',
      label: 'Menu',
      validation: {
        required: false,
      },
      styling: {
        mode: 'vertical',
        theme: 'light',
        inlineCollapsed: false,
      },
      items: [
        { key: '1', label: 'Navigation One', icon: 'MailOutlined' },
        { key: '2', label: 'Navigation Two', icon: 'CalendarOutlined' },
        {
          key: 'sub1',
          label: 'Navigation Three - Submenu',
          icon: 'AppstoreOutlined',
          children: [
            { key: '3', label: 'Option 3' },
            { key: '4', label: 'Option 4' },
          ],
        },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'pagination',
      content: 'Pagination',
      label: 'Pagination',
      validation: {
        required: false,
      },
      styling: {
        size: 'default',
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: true,
      },
      current: 1,
      total: 500,
      pageSize: 10,
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'steps',
      content: 'Steps',
      label: 'Steps',
      validation: {
        required: false,
      },
      styling: {
        type: 'default',
        size: 'default',
        direction: 'horizontal',
        labelPlacement: 'horizontal',
      },
      current: 1,
      items: [
        { title: 'Finished', description: 'This is a description.' },
        { title: 'In Progress', description: 'This is a description.' },
        { title: 'Waiting', description: 'This is a description.' },
      ],
    },
  },
  // Layout Components
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'divider',
      content: 'Divider',
      label: 'Divider',
      validation: {
        required: false,
      },
      styling: {
        type: 'horizontal',
        orientation: 'center',
        orientationMargin: 0,
        dashed: false,
        plain: false,
      },
      children: 'Divider Text',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'space',
      content: 'Space',
      label: 'Space',
      validation: {
        required: false,
      },
      styling: {
        direction: 'horizontal',
        size: 'small',
        align: 'center',
        wrap: false,
      },
      children: [
        { type: 'button', content: 'Button 1' },
        { type: 'button', content: 'Button 2' },
        { type: 'button', content: 'Button 3' },
      ],
    },
  },
  // Display Components
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'alert',
      content: 'Alert',
      label: 'Alert',
      validation: {
        required: false,
      },
      styling: {
        type: 'info',
        showIcon: true,
        closable: false,
        banner: false,
      },
      message: 'Alert Message',
      description: 'This is a detailed description of the alert.',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'progress',
      content: 'Progress',
      label: 'Progress',
      validation: {
        required: false,
      },
      styling: {
        type: 'line',
        size: 'default',
        showInfo: true,
        status: 'normal',
        strokeColor: '#1890ff',
      },
      percent: 50,
      format: 'percent',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'statistic',
      content: 'Statistic',
      label: 'Statistic',
      validation: {
        required: false,
      },
      styling: {
        precision: 2,
        decimalSeparator: '.',
        groupSeparator: ',',
        loading: false,
      },
      title: 'Active Users',
      value: 112893,
      prefix: '',
      suffix: '',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'anchor',
      content: 'Anchor',
      label: 'Anchor Navigation',
      validation: {
        required: false,
      },
      styling: {
        direction: 'vertical',
        offsetTop: 0,
        bounds: 5,
        showInkInFixed: true,
        targetOffset: undefined,
      },
      items: [
        { key: 'part-1', href: '#part-1', title: 'Part 1' },
        { key: 'part-2', href: '#part-2', title: 'Part 2' },
        { key: 'part-3', href: '#part-3', title: 'Part 3' },
      ],
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'backtop',
      content: 'BackTop',
      label: 'Back to Top',
      validation: {
        required: false,
      },
      styling: {
        visibilityHeight: 400,
        duration: 450,
        target: undefined,
      },
      text: 'Back to Top',
    },
  },
  // General Components
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'button',
      content: 'Button',
      label: 'Button',
      validation: {
        required: false,
      },
      styling: {
        type: 'default',
        size: 'middle',
        shape: 'default',
        block: false,
        danger: false,
        ghost: false,
        loading: false,
        icon: '',
      },
      text: 'Click me',
      htmlType: 'button',
    },
  },
  {
    id: shortid.generate(),
    type: SIDEBAR_ITEM,
    component: {
      type: 'typography',
      content: 'Typography',
      label: 'Typography',
      validation: {
        required: false,
      },
      styling: {
        level: 1,
        type: 'secondary',
        disabled: false,
        mark: false,
        code: false,
        keyboard: false,
        underline: false,
        delete: false,
        strong: false,
        italic: false,
      },
      text: 'Sample Typography Text',
      component: 'title', // title, text, paragraph
    },
  },
];

const formConstants = {
  SIDEBAR_ITEM,
  ROW,
  COLUMN,
  COMPONENT,
  TAB_CONTAINER,
  CARD_CONTAINER,
  FORM_SECTION,
  ACCORDION_CONTAINER,
  COLLAPSE_CONTAINER,
  STEPS_CONTAINER,
  GRID_CONTAINER,
  FLEX_CONTAINER,
  MODAL_CONTAINER,
  DRAWER_CONTAINER,
  COMPONENT_CAPABILITIES,
  SIDEBAR_ITEMS,
};

export default formConstants;
