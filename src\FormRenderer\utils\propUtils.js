/**
 * Property Utilities
 * 
 * Utility functions for processing, merging, and manipulating component props
 * and configuration objects throughout the FormRenderer.
 */

/**
 * Merges component props with default values
 * 
 * @param {Object} defaultProps - Default props object
 * @param {Object} userProps - User-provided props object
 * @returns {Object} Merged props object
 */
export const mergeProps = (defaultProps = {}, userProps = {}) => {
  return {
    ...defaultProps,
    ...userProps,
    // Handle nested style objects
    style: {
      ...defaultProps.style,
      ...userProps.style,
    },
  };
};

/**
 * Extracts form item props from component configuration
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Form item props object
 */
export const extractFormItemProps = (component) => {
  if (!component) return {};

  return {
    label: component.label,
    name: component.name || component.id,
    tooltip: component.tooltip,
    extra: component.extra,
    hasFeedback: component.styling?.hasFeedback,
    validateStatus: component.styling?.validateStatus,
    help: component.styling?.help,
    ...component.formItemProps,
  };
};

/**
 * Extracts common component props from configuration
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Common component props object
 */
export const extractCommonProps = (component) => {
  if (!component) return {};

  return {
    placeholder: component.placeholder,
    disabled: component.styling?.disabled || false,
    size: component.styling?.size || 'middle',
    style: {
      width: '100%',
      ...component.styling?.style,
    },
    ...component.componentProps,
  };
};

/**
 * Processes button props with defaults
 * 
 * @param {Object} buttonConfig - Button configuration object
 * @param {Object} defaults - Default button props
 * @returns {Object} Processed button props
 */
export const processButtonProps = (buttonConfig = {}, defaults = {}) => {
  return {
    ...defaults,
    ...buttonConfig,
    style: {
      ...defaults.style,
      ...buttonConfig.style,
    },
  };
};

/**
 * Sanitizes props by removing undefined values
 * 
 * @param {Object} props - Props object to sanitize
 * @returns {Object} Sanitized props object
 */
export const sanitizeProps = (props) => {
  if (!props || typeof props !== 'object') return {};

  const sanitized = {};
  
  Object.keys(props).forEach(key => {
    const value = props[key];
    if (value !== undefined && value !== null) {
      sanitized[key] = value;
    }
  });

  return sanitized;
};

/**
 * Converts component styling configuration to CSS properties
 * 
 * @param {Object} styling - Styling configuration object
 * @returns {Object} CSS properties object
 */
export const convertStylingToCSS = (styling = {}) => {
  const cssProps = {};

  // Handle common styling properties
  if (styling.width) cssProps.width = styling.width;
  if (styling.height) cssProps.height = styling.height;
  if (styling.margin) cssProps.margin = styling.margin;
  if (styling.padding) cssProps.padding = styling.padding;
  if (styling.backgroundColor) cssProps.backgroundColor = styling.backgroundColor;
  if (styling.color) cssProps.color = styling.color;
  if (styling.fontSize) cssProps.fontSize = styling.fontSize;
  if (styling.fontWeight) cssProps.fontWeight = styling.fontWeight;
  if (styling.textAlign) cssProps.textAlign = styling.textAlign;
  if (styling.border) cssProps.border = styling.border;
  if (styling.borderRadius) cssProps.borderRadius = styling.borderRadius;

  // Merge with any direct style object
  if (styling.style && typeof styling.style === 'object') {
    Object.assign(cssProps, styling.style);
  }

  return cssProps;
};
