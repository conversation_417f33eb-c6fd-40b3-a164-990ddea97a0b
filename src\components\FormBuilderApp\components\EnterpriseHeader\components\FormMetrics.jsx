/**
 * @fileoverview Form Metrics component
 *
 * This component displays real-time form metrics including component counts,
 * complexity analysis, and performance indicators in the enterprise header.
 *
 * @module FormMetrics
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useCallback } from 'react';
import { Tooltip, Progress, Badge } from 'antd';
import {
  AppstoreOutlined,
  ContainerOutlined,
  DashboardOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useFormMetrics } from '../hooks/useFormMetrics';
import {
  MetricsContainer,
  MetricCard,
  MetricValue,
  MetricLabel,
} from '../styles/EnterpriseHeader.styles';
import { HEADER_ANIMATIONS } from '../constants/headerConstants';

/**
 * Form Metrics component
 *
 * @param {Object} props - Component props
 * @param {Array} props.layout - Current form layout
 * @param {Object} props.components - Component registry
 * @param {Function} props.onMetricClick - Metric click handler
 * @returns {React.ReactNode} Form metrics JSX
 */
const FormMetrics = ({
  layout = [],
  components = {},
  onMetricClick,
}) => {
  // Get form metrics
  const {
    totalComponents,
    containerComponents,
    complexity,
    performanceScore,
    getComplexityAnalysis,
  } = useFormMetrics({ layout, components });

  /**
   * Handle metric card click
   */
  const handleMetricClick = useCallback((metricType, data) => {
    if (onMetricClick) {
      onMetricClick(metricType, data);
    }
  }, [onMetricClick]);

  /**
   * Get performance color based on score
   */
  const getPerformanceColor = useCallback((score) => {
    if (score >= 80) return '#52c41a'; // Green
    if (score >= 60) return '#faad14'; // Yellow
    if (score >= 40) return '#ff7a45'; // Orange
    return '#f5222d'; // Red
  }, []);

  /**
   * Format complexity tooltip content
   */
  const getComplexityTooltip = useCallback(() => {
    const analysis = getComplexityAnalysis();
    return (
      <div>
        <div><strong>Complexity Analysis</strong></div>
        <div>Total Components: {analysis.totalComponents}</div>
        <div>Containers: {analysis.containerComponents}</div>
        <div>Data Entry: {analysis.dataEntryComponents}</div>
        <div>Layout: {analysis.layoutComponents}</div>
        <div>Display: {analysis.displayComponents}</div>
        <div>Score: {complexity.score}</div>
      </div>
    );
  }, [getComplexityAnalysis, complexity.score]);

  /**
   * Format performance tooltip content
   */
  const getPerformanceTooltip = useCallback(() => {
    return (
      <div>
        <div><strong>Performance Score</strong></div>
        <div>Based on form structure and complexity</div>
        <div>Score: {performanceScore}/100</div>
        <div>
          Status: {performanceScore >= 80 ? 'Excellent' : 
                   performanceScore >= 60 ? 'Good' : 
                   performanceScore >= 40 ? 'Fair' : 'Needs Optimization'}
        </div>
      </div>
    );
  }, [performanceScore]);

  return (
    <MetricsContainer>
      {/* Total Components Metric */}
      <Tooltip title="Total number of form components">
        <MetricCard
          variants={HEADER_ANIMATIONS.METRIC_UPDATE}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => handleMetricClick('components', { totalComponents })}
        >
          <Badge
            count={totalComponents}
            showZero
            style={{
              backgroundColor: '#1890ff',
              fontSize: '10px',
              minWidth: '16px',
              height: '16px',
              lineHeight: '16px',
            }}
          >
            <AppstoreOutlined
              style={{
                fontSize: '16px',
                color: '#1890ff',
                marginBottom: '2px',
              }}
            />
          </Badge>
          <MetricValue>{totalComponents}</MetricValue>
          <MetricLabel>Components</MetricLabel>
        </MetricCard>
      </Tooltip>

      {/* Container Components Metric */}
      <Tooltip title="Number of container components (tabs, cards, sections)">
        <MetricCard
          variants={HEADER_ANIMATIONS.METRIC_UPDATE}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => handleMetricClick('containers', { containerComponents })}
        >
          <Badge
            count={containerComponents}
            showZero
            style={{
              backgroundColor: '#722ed1',
              fontSize: '10px',
              minWidth: '16px',
              height: '16px',
              lineHeight: '16px',
            }}
          >
            <ContainerOutlined
              style={{
                fontSize: '16px',
                color: '#722ed1',
                marginBottom: '2px',
              }}
            />
          </Badge>
          <MetricValue>{containerComponents}</MetricValue>
          <MetricLabel>Containers</MetricLabel>
        </MetricCard>
      </Tooltip>

      {/* Complexity Metric */}
      <Tooltip title={getComplexityTooltip()}>
        <MetricCard
          variants={HEADER_ANIMATIONS.METRIC_UPDATE}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => handleMetricClick('complexity', complexity)}
        >
          <div style={{ position: 'relative', marginBottom: '2px' }}>
            <DashboardOutlined
              style={{
                fontSize: '16px',
                color: complexity.color,
              }}
            />
            <div
              style={{
                position: 'absolute',
                top: '-2px',
                right: '-8px',
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: complexity.color,
                border: '1px solid white',
              }}
            />
          </div>
          <MetricValue style={{ color: complexity.color }}>
            {complexity.level.charAt(0).toUpperCase()}
          </MetricValue>
          <MetricLabel>Complexity</MetricLabel>
        </MetricCard>
      </Tooltip>

      {/* Performance Score Metric */}
      <Tooltip title={getPerformanceTooltip()}>
        <MetricCard
          variants={HEADER_ANIMATIONS.METRIC_UPDATE}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => handleMetricClick('performance', { performanceScore })}
        >
          <div style={{ position: 'relative', marginBottom: '2px' }}>
            <ThunderboltOutlined
              style={{
                fontSize: '16px',
                color: getPerformanceColor(performanceScore),
              }}
            />
            <Progress
              type="circle"
              percent={performanceScore}
              size={20}
              strokeColor={getPerformanceColor(performanceScore)}
              strokeWidth={8}
              showInfo={false}
              style={{
                position: 'absolute',
                top: '-2px',
                right: '-12px',
              }}
            />
          </div>
          <MetricValue style={{ color: getPerformanceColor(performanceScore) }}>
            {performanceScore}
          </MetricValue>
          <MetricLabel>Performance</MetricLabel>
        </MetricCard>
      </Tooltip>
    </MetricsContainer>
  );
};

export default FormMetrics;
