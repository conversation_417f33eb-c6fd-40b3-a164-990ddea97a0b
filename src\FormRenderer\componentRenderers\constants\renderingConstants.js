/**
 * Rendering Constants
 * 
 * Constants related to component rendering, performance, and behavior.
 * These constants control how components are rendered and optimized.
 */

/**
 * Rendering performance constants
 */
export const RENDERING_PERFORMANCE = {
  // Maximum number of components to render before virtualization
  MAX_COMPONENTS_BEFORE_VIRTUALIZATION: 100,
  
  // Debounce delay for prop changes (ms)
  PROP_CHANGE_DEBOUNCE: 100,
  
  // Throttle delay for expensive operations (ms)
  EXPENSIVE_OPERATION_THROTTLE: 300,
  
  // Maximum render depth for nested components
  MAX_RENDER_DEPTH: 10,
  
  // Memoization comparison depth
  MEMO_COMPARISON_DEPTH: 3,
};

/**
 * Error handling constants
 */
export const ERROR_HANDLING = {
  // Maximum number of render retries
  MAX_RENDER_RETRIES: 3,
  
  // Retry delay (ms)
  RETRY_DELAY: 1000,
  
  // Error boundary fallback timeout (ms)
  ERROR_BOUNDARY_TIMEOUT: 5000,
  
  // Default error messages
  DEFAULT_ERROR_MESSAGES: {
    COMPONENT_NOT_FOUND: 'Component not found',
    INVALID_PROPS: 'Invalid component props',
    RENDER_ERROR: 'Failed to render component',
    VALIDATION_ERROR: 'Component validation failed',
    UNKNOWN_TYPE: 'Unknown component type',
  },
};

/**
 * Validation constants
 */
export const VALIDATION_CONSTANTS = {
  // Default validation trigger
  DEFAULT_TRIGGER: 'onChange',
  
  // Validation debounce delay (ms)
  VALIDATION_DEBOUNCE: 300,
  
  // Maximum validation message length
  MAX_MESSAGE_LENGTH: 200,
  
  // Default validation messages
  DEFAULT_MESSAGES: {
    REQUIRED: 'This field is required',
    EMAIL: 'Please enter a valid email address',
    URL: 'Please enter a valid URL',
    NUMBER: 'Please enter a valid number',
    MIN_LENGTH: 'Input is too short',
    MAX_LENGTH: 'Input is too long',
    PATTERN: 'Input format is invalid',
  },
};

/**
 * Styling constants
 */
export const STYLING_CONSTANTS = {
  // Default component spacing
  DEFAULT_SPACING: {
    SMALL: '8px',
    MEDIUM: '16px',
    LARGE: '24px',
    EXTRA_LARGE: '32px',
  },
  
  // Default border radius
  DEFAULT_BORDER_RADIUS: {
    SMALL: '4px',
    MEDIUM: '6px',
    LARGE: '8px',
  },
  
  // Default colors
  DEFAULT_COLORS: {
    PRIMARY: '#1890ff',
    SUCCESS: '#52c41a',
    WARNING: '#faad14',
    ERROR: '#ff4d4f',
    TEXT: '#262626',
    TEXT_SECONDARY: '#8c8c8c',
    BORDER: '#d9d9d9',
    BACKGROUND: '#ffffff',
  },
  
  // Default shadows
  DEFAULT_SHADOWS: {
    SMALL: '0 1px 2px rgba(0, 0, 0, 0.03)',
    MEDIUM: '0 1px 6px rgba(0, 0, 0, 0.1)',
    LARGE: '0 4px 12px rgba(0, 0, 0, 0.15)',
  },
  
  // Default transitions
  DEFAULT_TRANSITIONS: {
    FAST: '0.1s ease',
    NORMAL: '0.3s ease',
    SLOW: '0.5s ease',
  },
};

/**
 * Layout constants
 */
export const LAYOUT_CONSTANTS = {
  // Default form layout
  DEFAULT_FORM_LAYOUT: 'vertical',
  
  // Default form item spacing
  DEFAULT_FORM_ITEM_SPACING: '16px',
  
  // Default container padding
  DEFAULT_CONTAINER_PADDING: '16px',
  
  // Responsive breakpoints
  BREAKPOINTS: {
    XS: '480px',
    SM: '576px',
    MD: '768px',
    LG: '992px',
    XL: '1200px',
    XXL: '1600px',
  },
  
  // Grid system
  GRID_COLUMNS: 24,
  GRID_GUTTER: 16,
};

/**
 * Animation constants
 */
export const ANIMATION_CONSTANTS = {
  // Default animation duration
  DEFAULT_DURATION: 300,
  
  // Animation easing functions
  EASING: {
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
    LINEAR: 'linear',
  },
  
  // Common animation types
  TYPES: {
    FADE: 'fade',
    SLIDE: 'slide',
    SCALE: 'scale',
    BOUNCE: 'bounce',
  },
};

/**
 * Accessibility constants
 */
export const ACCESSIBILITY_CONSTANTS = {
  // ARIA roles
  ARIA_ROLES: {
    BUTTON: 'button',
    TEXTBOX: 'textbox',
    COMBOBOX: 'combobox',
    LISTBOX: 'listbox',
    OPTION: 'option',
    TAB: 'tab',
    TABPANEL: 'tabpanel',
    ALERT: 'alert',
    STATUS: 'status',
  },
  
  // Keyboard navigation
  KEYBOARD: {
    ENTER: 'Enter',
    SPACE: ' ',
    ESCAPE: 'Escape',
    ARROW_UP: 'ArrowUp',
    ARROW_DOWN: 'ArrowDown',
    ARROW_LEFT: 'ArrowLeft',
    ARROW_RIGHT: 'ArrowRight',
    TAB: 'Tab',
  },
  
  // Focus management
  FOCUS: {
    OUTLINE_COLOR: '#1890ff',
    OUTLINE_WIDTH: '2px',
    OUTLINE_STYLE: 'solid',
  },
};

/**
 * Data handling constants
 */
export const DATA_CONSTANTS = {
  // Maximum data size for components (bytes)
  MAX_DATA_SIZE: 1024 * 1024, // 1MB
  
  // Maximum number of options for select components
  MAX_OPTIONS_COUNT: 1000,
  
  // Maximum string length for text inputs
  MAX_STRING_LENGTH: 10000,
  
  // Default pagination size
  DEFAULT_PAGE_SIZE: 10,
  
  // Maximum page size
  MAX_PAGE_SIZE: 100,
};

/**
 * Development constants
 */
export const DEVELOPMENT_CONSTANTS = {
  // Debug mode flag
  DEBUG_MODE: process.env.NODE_ENV === 'development',
  
  // Performance monitoring
  ENABLE_PERFORMANCE_MONITORING: process.env.NODE_ENV === 'development',
  
  // Console logging levels
  LOG_LEVELS: {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
  },
  
  // Development warnings
  WARNINGS: {
    DEPRECATED_PROP: 'This prop is deprecated and will be removed in a future version',
    MISSING_KEY: 'Missing key prop for list item',
    INVALID_CHILDREN: 'Invalid children for this component type',
  },
};

/**
 * Component state constants
 */
export const COMPONENT_STATE = {
  // Loading states
  LOADING_STATES: {
    IDLE: 'idle',
    LOADING: 'loading',
    SUCCESS: 'success',
    ERROR: 'error',
  },
  
  // Interaction states
  INTERACTION_STATES: {
    NORMAL: 'normal',
    HOVER: 'hover',
    FOCUS: 'focus',
    ACTIVE: 'active',
    DISABLED: 'disabled',
  },
  
  // Validation states
  VALIDATION_STATES: {
    NONE: 'none',
    VALIDATING: 'validating',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error',
  },
};

/**
 * Event handling constants
 */
export const EVENT_CONSTANTS = {
  // Common event types
  TYPES: {
    CLICK: 'click',
    CHANGE: 'change',
    FOCUS: 'focus',
    BLUR: 'blur',
    KEYDOWN: 'keydown',
    KEYUP: 'keyup',
    SUBMIT: 'submit',
    RESET: 'reset',
  },
  
  // Event delegation
  DELEGATION: {
    CAPTURE: true,
    BUBBLE: false,
  },
  
  // Debounce delays for different events
  DEBOUNCE_DELAYS: {
    INPUT: 300,
    RESIZE: 100,
    SCROLL: 50,
    SEARCH: 500,
  },
};
