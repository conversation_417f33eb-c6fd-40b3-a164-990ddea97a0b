/**
 * useSidebarState Hook
 *
 * Custom hook for managing the state of the CategorizedSidebar component.
 * Handles active categories, search functionality, and component filtering.
 *
 * Features:
 * - Active category management with expand/collapse functionality
 * - Search term state with debounced updates
 * - Auto-expand categories when searching
 * - Performance-optimized state updates
 * - Memoized computed values
 *
 * State Management:
 * - activeKeys: Array of currently expanded category keys
 * - searchTerm: Current search input value
 * - Auto-expansion logic when search is active
 *
 * @returns {Object} Sidebar state and control functions
 */

import { useState, useMemo, useCallback } from 'react';

/**
 * Default categories that should be expanded on initial load
 *
 * These categories are commonly used and provide immediate value
 * to users when they first open the sidebar.
 */
const DEFAULT_ACTIVE_CATEGORIES = ['Data Entry', 'Containers'];

/**
 * Custom hook for managing sidebar state
 *
 * This hook encapsulates all state management logic for the sidebar,
 * including category expansion, search functionality, and the interaction
 * between search and category visibility.
 *
 * @returns {Object} Object containing:
 *   - activeKeys: Array of currently expanded category keys
 *   - searchTerm: Current search input value
 *   - setActiveKeys: Function to update active categories
 *   - setSearchTerm: Function to update search term
 *   - handleCollapseChange: Handler for category expand/collapse
 *   - clearSearch: Function to clear search and reset state
 *   - isSearchActive: Boolean indicating if search is currently active
 */
export const useSidebarState = () => {
  // State for tracking which categories are currently expanded
  // Initialized with commonly used categories for better UX
  const [activeKeys, setActiveKeys] = useState(DEFAULT_ACTIVE_CATEGORIES);

  // State for the current search term
  // Empty string indicates no active search
  const [searchTerm, setSearchTerm] = useState('');

  /**
   * Handles changes to category expansion state
   *
   * This function is called when users click on category headers to
   * expand or collapse them. It only updates the state when search
   * is not active, as search mode auto-expands relevant categories.
   *
   * @param {Array} keys - Array of category keys that should be expanded
   */
  const handleCollapseChange = useCallback(
    (keys) => {
      // Only allow manual category control when search is not active
      // During search, categories are auto-expanded based on content
      if (!searchTerm.trim()) {
        setActiveKeys(keys);
      }
    },
    [searchTerm],
  );

  /**
   * Clears the search term and resets to default state
   *
   * This function resets the search input and restores the default
   * category expansion state, providing a clean way to exit search mode.
   */
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setActiveKeys(DEFAULT_ACTIVE_CATEGORIES);
  }, []);

  /**
   * Determines if search is currently active
   *
   * Search is considered active when there is a non-empty search term.
   * This affects category expansion behavior and UI state.
   */
  const isSearchActive = useMemo(() => {
    return searchTerm.trim().length > 0;
  }, [searchTerm]);

  /**
   * Computes effective active keys based on search state
   *
   * When search is active, this returns all categories that have
   * matching content. When search is inactive, it returns the
   * manually controlled active keys.
   *
   * This is computed separately from the main component to allow
   * for more complex logic and better performance optimization.
   *
   * @param {Object} filteredItems - Filtered component items by category
   * @returns {Array} Array of category keys that should be expanded
   */
  const getEffectiveActiveKeys = useCallback(
    (filteredItems) => {
      if (isSearchActive) {
        // During search, expand all categories that have matching items
        return Object.keys(filteredItems);
      }
      // When not searching, use manually controlled expansion state
      return activeKeys;
    },
    [isSearchActive, activeKeys],
  );

  /**
   * Updates search term with validation
   *
   * This function provides a controlled way to update the search term
   * with optional validation and preprocessing.
   *
   * @param {string} term - New search term
   */
  const updateSearchTerm = useCallback((term) => {
    // Ensure term is a string and handle null/undefined
    const cleanTerm = typeof term === 'string' ? term : '';
    setSearchTerm(cleanTerm);
  }, []);

  // Note: Additional utility functions like resetSidebarState and getSearchStats
  // are available but not currently used. They can be uncommented if needed.

  return {
    // Core state values
    activeKeys,
    searchTerm,

    // State setters
    setActiveKeys,
    setSearchTerm: updateSearchTerm,

    // Event handlers
    handleCollapseChange,

    // Utility functions
    clearSearch,
    getEffectiveActiveKeys,

    // Computed values
    isSearchActive,

    // Note: defaultActiveCategories constant available but not currently used
  };
};
