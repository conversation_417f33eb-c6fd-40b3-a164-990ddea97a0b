/**
 * Advanced Data Entry Component Renderers
 * 
 * Renders advanced data entry components like AutoComplete, Cascader, etc.
 * These components provide more sophisticated input capabilities.
 */

import React from 'react';
import {
  AutoComplete,
  Cascader,
  ColorPicker,
  Mentions,
  TimePicker,
  Transfer,
  TreeSelect,
} from 'antd';

/**
 * Renders an AutoComplete component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered AutoComplete component
 */
export const renderAutoComplete = (component, commonProps) => {
  return (
    <AutoComplete
      {...commonProps}
      options={component.options || []}
      allowClear={component.styling?.allowClear}
      backfill={component.styling?.backfill}
      filterOption={component.styling?.filterOption}
    />
  );
};

/**
 * Renders a Cascader component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered Cascader component
 */
export const renderCascader = (component, commonProps) => {
  return (
    <Cascader
      {...commonProps}
      options={component.options || []}
      allowClear={component.styling?.allowClear}
      showSearch={component.styling?.showSearch}
      multiple={component.styling?.multiple}
      changeOnSelect={component.styling?.changeOnSelect}
      expandTrigger={component.styling?.expandTrigger}
    />
  );
};

/**
 * Renders a ColorPicker component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered ColorPicker component
 */
export const renderColorPicker = (component, commonProps) => {
  return (
    <ColorPicker
      {...commonProps}
      format={component.styling?.format}
      showText={component.styling?.showText}
      allowClear={component.styling?.allowClear}
      defaultValue={component.defaultValue}
    />
  );
};

/**
 * Renders a Mentions component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered Mentions component
 */
export const renderMentions = (component, commonProps) => {
  return (
    <Mentions
      {...commonProps}
      rows={component.styling?.rows}
      allowClear={component.styling?.allowClear}
      autoSize={component.styling?.autoSize}
      options={component.options || []}
    />
  );
};

/**
 * Renders a TimePicker component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered TimePicker component
 */
export const renderTimePicker = (component, commonProps) => {
  return (
    <TimePicker
      {...commonProps}
      format={component.styling?.format}
      use12Hours={component.styling?.use12Hours}
      allowClear={component.styling?.allowClear}
      hourStep={component.styling?.hourStep}
      minuteStep={component.styling?.minuteStep}
      secondStep={component.styling?.secondStep}
    />
  );
};

/**
 * Renders a Transfer component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered Transfer component
 */
export const renderTransfer = (component, commonProps) => {
  return (
    <Transfer
      {...commonProps}
      dataSource={component.dataSource || []}
      targetKeys={component.targetKeys || []}
      showSearch={component.styling?.showSearch}
      showSelectAll={component.styling?.showSelectAll}
      oneWay={component.styling?.oneWay}
      titles={component.styling?.titles}
      operations={component.styling?.operations}
      render={(item) => item.title}
    />
  );
};

/**
 * Renders a TreeSelect component
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @returns {JSX.Element} Rendered TreeSelect component
 */
export const renderTreeSelect = (component, commonProps) => {
  return (
    <TreeSelect
      {...commonProps}
      treeData={component.treeData || []}
      allowClear={component.styling?.allowClear}
      showSearch={component.styling?.showSearch}
      multiple={component.styling?.multiple}
      treeCheckable={component.styling?.treeCheckable}
      treeDefaultExpandAll={component.styling?.treeDefaultExpandAll}
    />
  );
};
