/**
 * @fileoverview Project state management hook
 *
 * This hook manages project metadata, save state, version control,
 * and project-level operations for the enterprise header.
 *
 * @module useProjectState
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { message } from 'antd';
import { DEFAULT_PROJECT, PROJECT_STATUS } from '../constants/headerConstants';

/**
 * Custom hook for project state management
 *
 * @param {Object} options - Configuration options
 * @param {Object} options.currentFormSchema - Current form schema
 * @param {Function} options.onSave - Save callback function
 * @param {Function} options.onPublish - Publish callback function
 * @param {Function} options.onExport - Export callback function
 * @returns {Object} Project state and actions
 *
 * @example
 * ```jsx
 * const {
 *   project,
 *   isModified,
 *   isSaving,
 *   lastSaved,
 *   saveProject,
 *   publishProject,
 *   updateProjectName,
 *   updateProjectDescription
 * } = useProjectState({
 *   currentFormSchema,
 *   onSave: handleSave,
 *   onPublish: handlePublish,
 *   onExport: handleExport
 * });
 * ```
 */
export const useProjectState = ({
  currentFormSchema,
  onSave,
  onPublish,
  onExport,
}) => {
  // Project metadata state
  const [project, setProject] = useState(() => ({
    ...DEFAULT_PROJECT,
    id: `form_${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }));

  // Save state management
  const [isModified, setIsModified] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);

  // Auto-save functionality
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const autoSaveTimeoutRef = useRef(null);
  const lastSchemaRef = useRef(null);

  /**
   * Update project name with validation
   *
   * @param {string} name - New project name
   */
  const updateProjectName = useCallback((name) => {
    if (!name || name.trim().length === 0) {
      message.error('Project name cannot be empty');
      return;
    }

    if (name.length > 100) {
      message.error('Project name must be less than 100 characters');
      return;
    }

    setProject((prev) => ({
      ...prev,
      name: name.trim(),
      updatedAt: new Date().toISOString(),
    }));
    setIsModified(true);
  }, []);

  /**
   * Update project description
   *
   * @param {string} description - New project description
   */
  const updateProjectDescription = useCallback((description) => {
    setProject((prev) => ({
      ...prev,
      description: description?.trim() || '',
      updatedAt: new Date().toISOString(),
    }));
    setIsModified(true);
  }, []);

  /**
   * Save project with loading state
   *
   * @returns {Promise<boolean>} Success status
   */
  const saveProject = useCallback(async () => {
    if (isSaving) return false;

    setIsSaving(true);

    try {
      // Prepare save data
      const saveData = {
        project,
        formSchema: currentFormSchema,
        timestamp: new Date().toISOString(),
      };

      // Call external save handler
      if (onSave) {
        await onSave(saveData);
      }

      // Update project state
      setProject((prev) => ({
        ...prev,
        updatedAt: new Date().toISOString(),
      }));

      setIsModified(false);
      setLastSaved(new Date());

      message.success('Project saved successfully');
      return true;
    } catch (error) {
      console.error('Save failed:', error);
      message.error('Failed to save project');
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [project, currentFormSchema, onSave, isSaving]);

  /**
   * Publish project with validation
   *
   * @returns {Promise<boolean>} Success status
   */
  const publishProject = useCallback(async () => {
    if (isPublishing) return false;

    // Validate form before publishing
    if (!currentFormSchema?.layout?.length) {
      message.error('Cannot publish empty form');
      return false;
    }

    setIsPublishing(true);

    try {
      // Save before publishing
      const saveSuccess = await saveProject();
      if (!saveSuccess) {
        throw new Error('Save failed before publish');
      }

      // Prepare publish data
      const publishData = {
        project: {
          ...project,
          status: PROJECT_STATUS.PUBLISHED,
          publishedAt: new Date().toISOString(),
        },
        formSchema: currentFormSchema,
      };

      // Call external publish handler
      if (onPublish) {
        await onPublish(publishData);
      }

      // Update project status
      setProject((prev) => ({
        ...prev,
        status: PROJECT_STATUS.PUBLISHED,
        publishedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));

      message.success('Project published successfully');
      return true;
    } catch (error) {
      console.error('Publish failed:', error);
      message.error('Failed to publish project');
      return false;
    } finally {
      setIsPublishing(false);
    }
  }, [project, currentFormSchema, onPublish, isPublishing, saveProject]);

  /**
   * Export project data
   *
   * @param {string} format - Export format ('json', 'pdf', 'html')
   * @returns {Promise<boolean>} Success status
   */
  const exportProject = useCallback(
    async (format = 'json') => {
      try {
        const exportData = {
          project,
          formSchema: currentFormSchema,
          format,
          exportedAt: new Date().toISOString(),
        };

        if (onExport) {
          await onExport(exportData);
        }

        message.success(`Project exported as ${format.toUpperCase()}`);
        return true;
      } catch (error) {
        console.error('Export failed:', error);
        message.error('Failed to export project');
        return false;
      }
    },
    [project, currentFormSchema, onExport],
  );

  /**
   * Auto-save functionality
   */
  useEffect(() => {
    if (!autoSaveEnabled || !isModified) return;

    // Clear existing timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // Set new auto-save timeout (5 seconds)
    autoSaveTimeoutRef.current = setTimeout(() => {
      saveProject();
    }, 5000);

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [isModified, autoSaveEnabled, saveProject]);

  /**
   * Track schema changes for modification detection
   */
  useEffect(() => {
    const currentSchemaString = JSON.stringify(currentFormSchema);

    if (
      lastSchemaRef.current &&
      lastSchemaRef.current !== currentSchemaString
    ) {
      setIsModified(true);
    }

    lastSchemaRef.current = currentSchemaString;
  }, [currentFormSchema]);

  /**
   * Toggle auto-save functionality
   */
  const toggleAutoSave = useCallback(() => {
    setAutoSaveEnabled((prev) => !prev);
    message.info(`Auto-save ${!autoSaveEnabled ? 'enabled' : 'disabled'}`);
  }, [autoSaveEnabled]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  return {
    // Project state
    project,
    isModified,
    isSaving,
    isPublishing,
    lastSaved,
    autoSaveEnabled,

    // Project actions
    updateProjectName,
    updateProjectDescription,
    saveProject,
    publishProject,
    exportProject,
    setAutoSaveEnabled,
    toggleAutoSave,
  };
};
