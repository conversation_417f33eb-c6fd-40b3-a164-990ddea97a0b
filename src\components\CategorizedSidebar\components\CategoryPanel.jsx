/**
 * CategoryPanel Component
 *
 * Individual category panel within the CategorizedSidebar that displays
 * a collapsible section containing components of a specific category.
 *
 * This component is a critical part of the drag-and-drop system, as it
 * contains the SideBarItem components that users drag onto the form canvas.
 *
 * Features:
 * - Collapsible panel with category header
 * - Category icon, name, and component count display
 * - Tooltip with category description for better UX
 * - Grid layout for component items with proper spacing
 * - Drag-and-drop integration with SideBarItem components
 * - Performance optimization with React.memo
 * - Accessibility-compliant design with ARIA labels
 *
 * Drag-and-Drop Integration:
 * - Each item is wrapped in a SideBarItem component that handles drag operations
 * - The grid layout ensures proper spacing for drag interactions
 * - Z-index management ensures drag operations work correctly
 * - Category context is passed to each item for analytics and tracking
 *
 * Performance Considerations:
 * - Component is memoized to prevent unnecessary re-renders
 * - Grid layout is optimized for smooth scrolling
 * - Conditional rendering prevents empty panels from being displayed
 *
 * @param {Object} props - Component props
 * @param {string} props.category - Name of the category (e.g., "Data Entry", "Containers")
 * @param {Array} props.items - Array of component items in this category
 * @param {Object} props.categoryConfig - Configuration object for category metadata
 * @param {boolean} props.isActive - Boolean indicating if this panel is expanded
 * @returns {JSX.Element} Rendered category panel component
 */

import React, { memo } from 'react';
import { Tooltip } from 'antd';
import { CategoryHeader } from '../styles/StyledComponents';
import SideBarItem from '../../DragAndDrop/SideBarItem';

/**
 * CategoryPanel component for displaying categorized components
 *
 * This component renders the content of a collapsible panel containing components
 * of a specific category. It returns the grid of draggable component items.
 *
 * Design Principles:
 * - Clear category identification with icons and descriptions
 * - Efficient grid layout for component display
 * - Consistent spacing and visual hierarchy
 * - Performance optimization through memoization
 *
 * @param {Object} props - Component props
 * @param {string} props.category - Name of the category
 * @param {Array} props.items - Array of component items in this category
 * @param {Object} props.categoryConfig - Category configuration with icons and descriptions
 * @returns {JSX.Element} Grid of draggable component items
 */
const CategoryPanel = memo(({ category, items = [] }) => {
  /**
   * Renders the grid of component items with drag-and-drop functionality
   *
   * Creates a grid layout containing all the draggable component
   * items for this category. Each item is wrapped in a SideBarItem
   * component that handles the complete drag-and-drop lifecycle.
   *
   * Drag-and-Drop Implementation Details:
   * - Each SideBarItem component implements HTML5 drag-and-drop API
   * - The 'data' prop contains the component definition and metadata
   * - The 'category' prop provides context for analytics and validation
   * - Grid layout ensures consistent spacing for drag interactions
   * - Single column layout optimizes for sidebar width constraints
   *
   * Performance Optimizations:
   * - Uses item.id as React key for efficient re-rendering
   * - Grid layout prevents layout thrashing during drag operations
   * - Minimal inline styles to reduce style recalculation
   *
   * Accessibility Considerations:
   * - Each SideBarItem includes proper ARIA labels for screen readers
   * - Keyboard navigation support through SideBarItem implementation
   * - Focus management during drag operations
   *
   * @returns {JSX.Element} Grid of draggable component items
   */
  const renderComponentGrid = () => (
    <div
      style={{
        display: 'grid',
        gap: '2px',
        // Single column layout optimized for sidebar width
        gridTemplateColumns: '1fr',
        width: '100%',
      }}
    >
      {items.map((item) => (
        <SideBarItem key={item.id} data={item} category={category} />
      ))}
    </div>
  );

  // Don't render panel if no items
  if (!items || items.length === 0) {
    return null;
  }

  // Return just the content - header will be handled by parent
  return renderComponentGrid();
});

// Set display name for debugging
CategoryPanel.displayName = 'CategoryPanel';

/**
 * CategoryHeader component for rendering category panel headers
 *
 * @param {Object} props - Component props
 * @param {string} props.category - Category name
 * @param {Object} props.categoryConfig - Category configuration
 * @param {number} props.itemCount - Number of items in category
 * @returns {JSX.Element} Category header element
 */
export const CategoryPanelHeader = memo(
  ({ category, categoryConfig = {}, itemCount = 0 }) => {
    const config = categoryConfig[category] || {};
    const metadata = {
      icon: config.icon || null,
      description: config.description || `${category} components`,
      color: config.color || '#666',
    };

    return (
      <CategoryHeader>
        {/* Category icon */}
        {metadata.icon && (
          <span style={{ color: metadata.color }}>{metadata.icon}</span>
        )}

        {/* Category name */}
        <span style={{ color: metadata.color }}>{category}</span>

        {/* Component count with tooltip */}
        <Tooltip
          title={metadata.description}
          placement='right'
          mouseEnterDelay={0.5}
        >
          <span className='category-count'>{itemCount}</span>
        </Tooltip>
      </CategoryHeader>
    );
  },
);

CategoryPanelHeader.displayName = 'CategoryPanelHeader';

export default CategoryPanel;
