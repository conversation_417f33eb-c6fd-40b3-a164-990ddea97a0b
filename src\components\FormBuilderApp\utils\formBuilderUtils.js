/**
 * @fileoverview Form Builder utility functions
 *
 * This module provides utility functions for form builder operations
 * including schema transformation, validation helpers, and common operations.
 *
 * @module formBuilderUtils
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

// Utility functions for form builder operations

/**
 * Create memoized form props to prevent unnecessary re-renders
 *
 * @returns {Object} Form properties object
 *
 * @example
 * ```jsx
 * const formProps = createMemoizedFormProps();
 * ```
 */
export const createMemoizedFormProps = () => {
  return {
    layout: 'vertical',
    size: 'large',
  };
};

/**
 * Create default form properties
 *
 * @returns {Object} Default form properties
 *
 * @example
 * ```jsx
 * const formProps = getDefaultFormProps();
 * ```
 */
export const getDefaultFormProps = () => {
  return {
    layout: 'vertical',
    size: 'large',
  };
};

/**
 * Generate unique component ID
 *
 * @returns {string} Unique component identifier
 *
 * @example
 * ```jsx
 * const componentId = generateComponentId();
 * ```
 */
export const generateComponentId = () => {
  return `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Validate component structure
 *
 * @param {Object} component - Component to validate
 * @returns {boolean} True if component is valid
 *
 * @example
 * ```jsx
 * const isValid = validateComponentStructure(component);
 * ```
 */
export const validateComponentStructure = (component) => {
  if (!component || typeof component !== 'object') {
    return false;
  }

  // Check required properties
  const requiredProps = ['id', 'type'];
  return requiredProps.every((prop) => component.hasOwnProperty(prop));
};

/**
 * Deep clone object safely
 *
 * @param {*} obj - Object to clone
 * @returns {*} Cloned object
 *
 * @example
 * ```jsx
 * const cloned = deepClone(originalObject);
 * ```
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item));
  }

  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }

  return obj;
};

/**
 * Check if layout is empty
 *
 * @param {Array} layout - Layout array to check
 * @returns {boolean} True if layout is empty
 *
 * @example
 * ```jsx
 * const isEmpty = isLayoutEmpty(layout);
 * ```
 */
export const isLayoutEmpty = (layout) => {
  return !layout || !Array.isArray(layout) || layout.length === 0;
};

/**
 * Count total components in layout
 *
 * @param {Array} layout - Layout array
 * @param {Object} components - Components registry
 * @returns {number} Total component count
 *
 * @example
 * ```jsx
 * const count = countTotalComponents(layout, components);
 * ```
 */
export const countTotalComponents = (layout, components) => {
  if (!layout || !Array.isArray(layout)) {
    return 0;
  }

  let count = 0;

  const countInItems = (items) => {
    items.forEach((item) => {
      if (item.type === 'component' && components[item.id]) {
        count++;
      }

      if (item.children && Array.isArray(item.children)) {
        countInItems(item.children);
      }

      if (item.tabs && Array.isArray(item.tabs)) {
        item.tabs.forEach((tab) => {
          if (tab.children && Array.isArray(tab.children)) {
            countInItems(tab.children);
          }
        });
      }
    });
  };

  countInItems(layout);
  return count;
};

/**
 * Get layout depth
 *
 * @param {Array} layout - Layout array
 * @returns {number} Maximum nesting depth
 *
 * @example
 * ```jsx
 * const depth = getLayoutDepth(layout);
 * ```
 */
export const getLayoutDepth = (layout) => {
  if (!layout || !Array.isArray(layout)) {
    return 0;
  }

  const getDepth = (items, currentDepth = 0) => {
    let maxDepth = currentDepth;

    items.forEach((item) => {
      let itemDepth = currentDepth + 1;

      if (item.children && Array.isArray(item.children)) {
        itemDepth = Math.max(
          itemDepth,
          getDepth(item.children, currentDepth + 1),
        );
      }

      if (item.tabs && Array.isArray(item.tabs)) {
        item.tabs.forEach((tab) => {
          if (tab.children && Array.isArray(tab.children)) {
            itemDepth = Math.max(
              itemDepth,
              getDepth(tab.children, currentDepth + 1),
            );
          }
        });
      }

      maxDepth = Math.max(maxDepth, itemDepth);
    });

    return maxDepth;
  };

  return getDepth(layout);
};

/**
 * Format timestamp for display
 *
 * @param {string|Date} timestamp - Timestamp to format
 * @returns {string} Formatted timestamp
 *
 * @example
 * ```jsx
 * const formatted = formatTimestamp(new Date());
 * ```
 */
export const formatTimestamp = (timestamp) => {
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

  if (isNaN(date.getTime())) {
    return 'Invalid Date';
  }

  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

/**
 * Debounce function for performance optimization
 *
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 *
 * @example
 * ```jsx
 * const debouncedSave = debounce(saveFunction, 300);
 * ```
 */
export const debounce = (func, wait) => {
  let timeout;

  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle function for performance optimization
 *
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 *
 * @example
 * ```jsx
 * const throttledUpdate = throttle(updateFunction, 100);
 * ```
 */
export const throttle = (func, limit) => {
  let inThrottle;

  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};
