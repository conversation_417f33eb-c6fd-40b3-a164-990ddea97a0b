import { useMemo } from 'react';

/**
 * Custom hook for validation rules generation
 * 
 * Generates comprehensive validation rules based on component configuration.
 * Supports various validation types including required, pattern, length, email, etc.
 * 
 * @param {Object} component - Component configuration object
 * @returns {Array} Array of Ant Design validation rules
 */
export const useValidationRules = (component) => {
  /**
   * Memoized validation rules generation
   * 
   * Creates validation rules based on component validation configuration.
   * Supports multiple validation types and custom validation functions.
   */
  const validationRules = useMemo(() => {
    if (!component || !component.validation) {
      return [];
    }

    const rules = [];
    const validation = component.validation;
    const fieldLabel = component.label || 'Field';

    // Required field validation
    if (validation.required) {
      rules.push({
        required: true,
        message: validation.message || `${fieldLabel} is required`,
      });
    }

    // Email validation
    if (validation.email || component.type === 'email') {
      rules.push({
        type: 'email',
        message: validation.emailMessage || 'Please enter a valid email address',
      });
    }

    // URL validation
    if (validation.url) {
      rules.push({
        type: 'url',
        message: validation.urlMessage || 'Please enter a valid URL',
      });
    }

    // Number validation
    if (validation.number || component.type === 'number') {
      rules.push({
        type: 'number',
        message: validation.numberMessage || 'Please enter a valid number',
      });
    }

    // Pattern validation (regex)
    if (validation.pattern) {
      rules.push({
        pattern: new RegExp(validation.pattern),
        message: validation.patternMessage || 'Invalid format',
      });
    }

    // Minimum length validation
    if (validation.min !== undefined) {
      rules.push({
        min: validation.min,
        message: validation.minMessage || `Minimum length is ${validation.min}`,
      });
    }

    // Maximum length validation
    if (validation.max !== undefined) {
      rules.push({
        max: validation.max,
        message: validation.maxMessage || `Maximum length is ${validation.max}`,
      });
    }

    // Minimum value validation (for numbers)
    if (validation.minValue !== undefined) {
      rules.push({
        type: 'number',
        min: validation.minValue,
        message: validation.minValueMessage || `Minimum value is ${validation.minValue}`,
      });
    }

    // Maximum value validation (for numbers)
    if (validation.maxValue !== undefined) {
      rules.push({
        type: 'number',
        max: validation.maxValue,
        message: validation.maxValueMessage || `Maximum value is ${validation.maxValue}`,
      });
    }

    // Custom validator function
    if (validation.validator && typeof validation.validator === 'function') {
      rules.push({
        validator: validation.validator,
      });
    }

    // Whitespace validation
    if (validation.whitespace) {
      rules.push({
        whitespace: true,
        message: validation.whitespaceMessage || 'Input cannot be only whitespace',
      });
    }

    // Transform function for value processing
    if (validation.transform && typeof validation.transform === 'function') {
      rules.push({
        transform: validation.transform,
      });
    }

    return rules;
  }, [
    component?.validation,
    component?.label,
    component?.type,
  ]);

  return validationRules;
};
