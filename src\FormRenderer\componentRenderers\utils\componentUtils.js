/**
 * Component Utility Functions
 * 
 * General utility functions for component processing, validation, and manipulation.
 * These functions provide common operations used throughout the ComponentRenderer.
 */

/**
 * Checks if a component is a container type
 * 
 * @param {Object} component - Component configuration object
 * @returns {boolean} True if component is a container type
 */
export const isContainerComponent = (component) => {
  if (!component || !component.type) return false;
  
  const containerTypes = [
    'TAB_CONTAINER',
    'CARD_CONTAINER', 
    'FORM_SECTION',
    'ACCORDION_CONTAINER',
    'STEPS_CONTAINER',
    'GRID_CONTAINER',
    'FLEX_CONTAINER'
  ];
  
  return containerTypes.includes(component.type);
};

/**
 * Checks if a component is a form input type
 * 
 * @param {Object} component - Component configuration object
 * @returns {boolean} True if component is a form input type
 */
export const isFormInputComponent = (component) => {
  if (!component || !component.type) return false;
  
  const inputTypes = [
    'input', 'email', 'textarea', 'select', 'radio', 'checkbox',
    'inputnumber', 'number', 'password', 'datepicker', 'date',
    'rangepicker', 'switch', 'rate', 'slider', 'upload',
    'autocomplete', 'cascader', 'colorpicker', 'mentions',
    'timepicker', 'transfer', 'treeselect'
  ];
  
  return inputTypes.includes(component.type);
};

/**
 * Checks if a component is a display type
 * 
 * @param {Object} component - Component configuration object
 * @returns {boolean} True if component is a display type
 */
export const isDisplayComponent = (component) => {
  if (!component || !component.type) return false;
  
  const displayTypes = [
    'avatar', 'badge', 'image', 'tag', 'button', 'typography',
    'statistic', 'table', 'list', 'calendar', 'carousel',
    'descriptions', 'empty', 'timeline', 'tree'
  ];
  
  return displayTypes.includes(component.type);
};

/**
 * Gets the component category based on its type
 * 
 * @param {Object} component - Component configuration object
 * @returns {string} Component category
 */
export const getComponentCategory = (component) => {
  if (!component || !component.type) return 'unknown';
  
  if (isFormInputComponent(component)) return 'input';
  if (isDisplayComponent(component)) return 'display';
  if (isContainerComponent(component)) return 'container';
  
  const feedbackTypes = ['alert', 'progress', 'skeleton', 'spin'];
  if (feedbackTypes.includes(component.type)) return 'feedback';
  
  const navigationTypes = ['breadcrumb', 'menu', 'pagination', 'steps'];
  if (navigationTypes.includes(component.type)) return 'navigation';
  
  const layoutTypes = ['divider', 'space'];
  if (layoutTypes.includes(component.type)) return 'layout';
  
  return 'unknown';
};

/**
 * Validates component configuration
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Validation result with isValid flag and errors array
 */
export const validateComponent = (component) => {
  const errors = [];
  
  if (!component) {
    errors.push('Component is required');
    return { isValid: false, errors };
  }
  
  if (!component.type) {
    errors.push('Component type is required');
  }
  
  if (!component.id && !component.name) {
    errors.push('Component must have either id or name');
  }
  
  // Validate form input components have proper field names
  if (isFormInputComponent(component) && !component.name && !component.id) {
    errors.push('Form input components must have a name or id for form binding');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Gets default props for a component type
 * 
 * @param {string} componentType - Type of component
 * @returns {Object} Default props object
 */
export const getDefaultPropsForType = (componentType) => {
  const defaults = {
    input: {
      placeholder: 'Enter text...',
      allowClear: true,
    },
    select: {
      placeholder: 'Please select...',
      allowClear: true,
      showSearch: true,
    },
    textarea: {
      placeholder: 'Enter text...',
      rows: 4,
      allowClear: true,
    },
    datepicker: {
      placeholder: 'Select date',
      allowClear: true,
    },
    upload: {
      listType: 'text',
      multiple: false,
    },
    button: {
      type: 'default',
      size: 'middle',
    },
  };
  
  return defaults[componentType] || {};
};

/**
 * Merges component configuration with defaults
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Component with merged defaults
 */
export const mergeComponentDefaults = (component) => {
  if (!component || !component.type) return component;
  
  const defaults = getDefaultPropsForType(component.type);
  
  return {
    ...component,
    componentProps: {
      ...defaults,
      ...component.componentProps,
    },
  };
};

/**
 * Extracts field name from component
 * 
 * @param {Object} component - Component configuration object
 * @returns {string} Field name for form binding
 */
export const getFieldName = (component) => {
  if (!component) return null;
  return component.name || component.id || null;
};

/**
 * Checks if component should be wrapped in Form.Item
 * 
 * @param {Object} component - Component configuration object
 * @returns {boolean} True if component should be wrapped in Form.Item
 */
export const shouldWrapInFormItem = (component) => {
  if (!component) return false;
  
  // Form input components should be wrapped
  if (isFormInputComponent(component)) return true;
  
  // Components with validation should be wrapped
  if (component.validation) return true;
  
  // Components with form item props should be wrapped
  if (component.formItemProps) return true;
  
  // Display and layout components typically don't need wrapping
  return false;
};
