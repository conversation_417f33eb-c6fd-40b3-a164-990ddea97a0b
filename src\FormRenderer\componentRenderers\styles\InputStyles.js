/**
 * Input Component Styling
 * 
 * Styled components specifically for input-type form components.
 * Provides consistent styling for all input variations.
 */

import styled from 'styled-components';

/**
 * Base input styling theme
 */
const inputTheme = {
  borderRadius: '6px',
  borderColor: '#d9d9d9',
  borderColorHover: '#40a9ff',
  borderColorFocus: '#1890ff',
  borderColorError: '#ff4d4f',
  boxShadowFocus: '0 0 0 2px rgba(24, 144, 255, 0.2)',
  boxShadowError: '0 0 0 2px rgba(255, 77, 79, 0.2)',
  padding: '8px 12px',
  fontSize: '14px',
  transition: 'all 0.3s ease',
};

/**
 * Text input styling
 * 
 * Provides styling for text input components.
 */
export const TextInputWrapper = styled.div`
  width: 100%;
  
  .ant-input {
    border-radius: ${inputTheme.borderRadius};
    border-color: ${inputTheme.borderColor};
    font-size: ${inputTheme.fontSize};
    transition: ${inputTheme.transition};
    
    &:hover {
      border-color: ${inputTheme.borderColorHover};
    }
    
    &:focus {
      border-color: ${inputTheme.borderColorFocus};
      box-shadow: ${inputTheme.boxShadowFocus};
    }
    
    &.ant-input-status-error {
      border-color: ${inputTheme.borderColorError};
      box-shadow: ${inputTheme.boxShadowError};
    }
  }
  
  /* Input with affix styling */
  .ant-input-affix-wrapper {
    border-radius: ${inputTheme.borderRadius};
    border-color: ${inputTheme.borderColor};
    transition: ${inputTheme.transition};
    
    &:hover {
      border-color: ${inputTheme.borderColorHover};
    }
    
    &:focus-within {
      border-color: ${inputTheme.borderColorFocus};
      box-shadow: ${inputTheme.boxShadowFocus};
    }
    
    &.ant-input-affix-wrapper-status-error {
      border-color: ${inputTheme.borderColorError};
      box-shadow: ${inputTheme.boxShadowError};
    }
    
    .ant-input-prefix,
    .ant-input-suffix {
      color: #8c8c8c;
    }
  }
`;

/**
 * Textarea styling
 * 
 * Provides styling for textarea components.
 */
export const TextareaWrapper = styled.div`
  width: 100%;
  
  .ant-input {
    border-radius: ${inputTheme.borderRadius};
    border-color: ${inputTheme.borderColor};
    font-size: ${inputTheme.fontSize};
    transition: ${inputTheme.transition};
    resize: vertical;
    min-height: 80px;
    
    &:hover {
      border-color: ${inputTheme.borderColorHover};
    }
    
    &:focus {
      border-color: ${inputTheme.borderColorFocus};
      box-shadow: ${inputTheme.boxShadowFocus};
    }
    
    &.ant-input-status-error {
      border-color: ${inputTheme.borderColorError};
      box-shadow: ${inputTheme.boxShadowError};
    }
  }
  
  /* Auto-size textarea */
  .ant-input-autosize {
    min-height: 80px;
    max-height: 300px;
  }
`;

/**
 * Password input styling
 * 
 * Provides styling for password input components.
 */
export const PasswordInputWrapper = styled.div`
  width: 100%;
  
  .ant-input-password {
    border-radius: ${inputTheme.borderRadius};
    border-color: ${inputTheme.borderColor};
    transition: ${inputTheme.transition};
    
    &:hover {
      border-color: ${inputTheme.borderColorHover};
    }
    
    &:focus-within {
      border-color: ${inputTheme.borderColorFocus};
      box-shadow: ${inputTheme.boxShadowFocus};
    }
    
    &.ant-input-password-status-error {
      border-color: ${inputTheme.borderColorError};
      box-shadow: ${inputTheme.boxShadowError};
    }
    
    .ant-input {
      border: none;
      box-shadow: none;
      
      &:focus {
        border: none;
        box-shadow: none;
      }
    }
    
    .ant-input-suffix {
      .ant-input-password-icon {
        color: #8c8c8c;
        transition: color 0.3s ease;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
`;

/**
 * Number input styling
 * 
 * Provides styling for number input components.
 */
export const NumberInputWrapper = styled.div`
  width: 100%;
  
  .ant-input-number {
    width: 100%;
    border-radius: ${inputTheme.borderRadius};
    border-color: ${inputTheme.borderColor};
    transition: ${inputTheme.transition};
    
    &:hover {
      border-color: ${inputTheme.borderColorHover};
    }
    
    &:focus-within {
      border-color: ${inputTheme.borderColorFocus};
      box-shadow: ${inputTheme.boxShadowFocus};
    }
    
    &.ant-input-number-status-error {
      border-color: ${inputTheme.borderColorError};
      box-shadow: ${inputTheme.boxShadowError};
    }
    
    .ant-input-number-input {
      font-size: ${inputTheme.fontSize};
    }
    
    .ant-input-number-handler-wrap {
      border-radius: 0 ${inputTheme.borderRadius} ${inputTheme.borderRadius} 0;
      
      .ant-input-number-handler {
        border-color: ${inputTheme.borderColor};
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
`;

/**
 * Search input styling
 * 
 * Provides styling for search input components.
 */
export const SearchInputWrapper = styled.div`
  width: 100%;
  
  .ant-input-search {
    .ant-input-group {
      .ant-input {
        border-radius: ${inputTheme.borderRadius} 0 0 ${inputTheme.borderRadius};
        border-color: ${inputTheme.borderColor};
        transition: ${inputTheme.transition};
        
        &:hover {
          border-color: ${inputTheme.borderColorHover};
        }
        
        &:focus {
          border-color: ${inputTheme.borderColorFocus};
          box-shadow: ${inputTheme.boxShadowFocus};
        }
      }
      
      .ant-input-group-addon {
        .ant-btn {
          border-radius: 0 ${inputTheme.borderRadius} ${inputTheme.borderRadius} 0;
          border-left: none;
          
          &:hover {
            border-color: ${inputTheme.borderColorHover};
          }
        }
      }
    }
  }
`;

/**
 * Input group styling
 * 
 * Provides styling for input group components.
 */
export const InputGroupWrapper = styled.div`
  width: 100%;
  
  .ant-input-group {
    .ant-input-group-addon {
      background-color: #fafafa;
      border-color: ${inputTheme.borderColor};
      color: #666;
      font-weight: 500;
      
      &:first-child {
        border-radius: ${inputTheme.borderRadius} 0 0 ${inputTheme.borderRadius};
      }
      
      &:last-child {
        border-radius: 0 ${inputTheme.borderRadius} ${inputTheme.borderRadius} 0;
      }
    }
    
    .ant-input {
      border-color: ${inputTheme.borderColor};
      transition: ${inputTheme.transition};
      
      &:hover {
        border-color: ${inputTheme.borderColorHover};
      }
      
      &:focus {
        border-color: ${inputTheme.borderColorFocus};
        box-shadow: ${inputTheme.boxShadowFocus};
      }
      
      &:not(:first-child):not(:last-child) {
        border-radius: 0;
      }
      
      &:first-child {
        border-radius: ${inputTheme.borderRadius} 0 0 ${inputTheme.borderRadius};
      }
      
      &:last-child {
        border-radius: 0 ${inputTheme.borderRadius} ${inputTheme.borderRadius} 0;
      }
    }
  }
  
  /* Compact input group */
  .ant-input-group-compact {
    .ant-input {
      border-right-width: 0;
      
      &:hover {
        border-right-width: 1px;
        z-index: 1;
      }
      
      &:focus {
        border-right-width: 1px;
        z-index: 1;
      }
      
      &:last-child {
        border-right-width: 1px;
      }
    }
  }
`;

/**
 * Input validation styling
 * 
 * Provides styling for input validation states.
 */
export const InputValidationWrapper = styled.div`
  width: 100%;
  
  /* Success state */
  &.validation-success {
    .ant-input,
    .ant-input-affix-wrapper,
    .ant-input-number,
    .ant-input-password {
      border-color: #52c41a;
      
      &:focus,
      &:focus-within {
        border-color: #52c41a;
        box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
      }
    }
  }
  
  /* Warning state */
  &.validation-warning {
    .ant-input,
    .ant-input-affix-wrapper,
    .ant-input-number,
    .ant-input-password {
      border-color: #faad14;
      
      &:focus,
      &:focus-within {
        border-color: #faad14;
        box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
      }
    }
  }
  
  /* Error state */
  &.validation-error {
    .ant-input,
    .ant-input-affix-wrapper,
    .ant-input-number,
    .ant-input-password {
      border-color: ${inputTheme.borderColorError};
      
      &:focus,
      &:focus-within {
        border-color: ${inputTheme.borderColorError};
        box-shadow: ${inputTheme.boxShadowError};
      }
    }
  }
  
  /* Validating state */
  &.validation-validating {
    .ant-input,
    .ant-input-affix-wrapper,
    .ant-input-number,
    .ant-input-password {
      border-color: #1890ff;
      
      &:focus,
      &:focus-within {
        border-color: #1890ff;
        box-shadow: ${inputTheme.boxShadowFocus};
      }
    }
  }
`;
