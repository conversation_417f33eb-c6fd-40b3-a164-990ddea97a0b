/**
 * AIChatSection Constants
 * 
 * Central location for all constants used in the AIChatSection component.
 * Includes default messages, configuration values, and other static data.
 * 
 * Features:
 * - Welcome message template
 * - Processing stage constants
 * - Configuration defaults
 * - UI text constants
 * - Error messages
 */

/**
 * Default welcome message for new chat sessions
 * Provides users with context and examples of AI capabilities
 */
export const WELCOME_MESSAGE = {
  id: 1,
  text: `👋 Welcome to AI-Powered Form Builder!

I'm your intelligent assistant, powered by Groq's Llama 3.3 70B. I can instantly generate complete, professional forms from simple descriptions.

✨ **What I can create:**
• Simple contact forms
• Complex multi-step wizards
• Enterprise onboarding flows
• Survey forms with advanced logic
• E-commerce checkout forms

🚀 **Just tell me what you need!** For example:
"Create a 5-step supplier registration form"
"Build a customer feedback survey"
"Make a job application form with file uploads"`,
  isUser: false,
  timestamp: new Date(),
  type: 'welcome',
};

/**
 * Processing stage constants for AI form generation
 * Used to show progress and current operation to users
 */
export const PROCESSING_STAGES = {
  ANALYZING: 'Analyzing your request...',
  GENERATING: 'Generating form schema...',
  VALIDATING: 'Validating schema...',
  APPLYING: 'Applying to builder...',
  COMPLETE: 'Form generation complete!',
};

/**
 * AI status constants
 * Represents different states of the AI processing system
 */
export const AI_STATUS = {
  READY: 'ready',
  PROCESSING: 'processing',
  SUCCESS: 'success',
  ERROR: 'error',
  CONNECTING: 'connecting',
  DISCONNECTED: 'disconnected',
};

/**
 * Message type constants
 * Categorizes different types of chat messages
 */
export const MESSAGE_TYPES = {
  WELCOME: 'welcome',
  USER: 'user',
  AI: 'ai',
  FORM_GENERATED: 'form-generated',
  ERROR: 'error',
  SYSTEM: 'system',
  LOADING: 'loading',
};

/**
 * Connection status constants
 * Represents AI service connection states
 */
export const CONNECTION_STATUS = {
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  ERROR: 'error',
  TESTING: 'testing',
};

/**
 * Default configuration values
 * Centralized configuration for the chat component
 */
export const DEFAULT_CONFIG = {
  // Connection settings
  CONNECTION_TIMEOUT: 10000, // 10 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second base delay
  
  // UI settings
  MAX_MESSAGE_LENGTH: 2000,
  TYPING_DELAY: 500, // Delay before showing typing indicator
  ANIMATION_DURATION: 300, // Default animation duration in ms
  
  // Processing settings
  PROCESSING_TIMEOUT: 60000, // 60 seconds
  PROGRESS_UPDATE_INTERVAL: 100, // Progress bar update interval
  
  // Message settings
  MAX_MESSAGES: 100, // Maximum messages to keep in memory
  AUTO_SCROLL_DELAY: 100, // Delay before auto-scrolling to new messages
};

/**
 * UI text constants
 * Centralized text content for consistent messaging
 */
export const UI_TEXT = {
  // Placeholders
  INPUT_PLACEHOLDER: '✨ Describe your form idea... (e.g., "Create a customer onboarding wizard with 4 steps")',
  
  // Button labels
  SEND_BUTTON: 'Send',
  COPY_BUTTON: 'Copy JSON',
  MODIFY_BUTTON: 'Modify',
  RETRY_BUTTON: 'Retry',
  
  // Status messages
  CONNECTION_SUCCESS: 'AI service connected successfully',
  CONNECTION_ERROR: 'AI service connection failed',
  GENERATION_SUCCESS: 'Form generated successfully',
  GENERATION_ERROR: 'Form generation failed',
  
  // Loading messages
  AI_THINKING: 'AI is thinking...',
  CONNECTING: 'Connecting to AI service...',
  PROCESSING: 'Processing your request...',
  
  // Success messages
  FORM_APPLIED: '🚀 Form automatically applied to builder and preview!',
  SCHEMA_COPIED: 'Schema copied to clipboard!',
  
  // Error messages
  CONNECTION_REQUIRED: 'AI service is not connected. Please check your configuration.',
  EMPTY_INPUT: 'Please enter a form description.',
  GENERATION_FAILED: 'Failed to generate form. Please try again.',
};

/**
 * Error message templates
 * Standardized error messages for different scenarios
 */
export const ERROR_MESSAGES = {
  CONNECTION_TIMEOUT: 'Connection timeout. Please check your network and try again.',
  INVALID_RESPONSE: 'Received invalid response from AI service.',
  RATE_LIMIT: 'Rate limit exceeded. Please wait a moment and try again.',
  SERVICE_UNAVAILABLE: 'AI service is temporarily unavailable. Please try again later.',
  INVALID_INPUT: 'Invalid input provided. Please check your request and try again.',
  SCHEMA_VALIDATION: 'Generated schema failed validation. Please try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
};

/**
 * Animation timing constants
 * Consistent timing values for animations throughout the component
 */
export const ANIMATION_TIMING = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 800,
  
  // Specific animations
  MESSAGE_ENTER: 300,
  MESSAGE_EXIT: 200,
  TYPING_DOT: 1400,
  STATUS_TRANSITION: 400,
  PROGRESS_UPDATE: 100,
};

/**
 * Color constants for consistent theming
 * Used when theme colors are not available
 */
export const FALLBACK_COLORS = {
  PRIMARY: '#3b82f6',
  SUCCESS: '#10b981',
  ERROR: '#ef4444',
  WARNING: '#f59e0b',
  INFO: '#3b82f6',
  GRAY: '#6b7280',
  LIGHT_GRAY: '#9ca3af',
  DARK_GRAY: '#374151',
};

/**
 * Keyboard shortcuts
 * Key combinations for various actions
 */
export const KEYBOARD_SHORTCUTS = {
  SEND_MESSAGE: 'Enter',
  NEW_LINE: 'Shift+Enter',
  CLEAR_INPUT: 'Escape',
  FOCUS_INPUT: 'Ctrl+/',
};

/**
 * Accessibility constants
 * ARIA labels and other accessibility-related constants
 */
export const ACCESSIBILITY = {
  LABELS: {
    CHAT_INPUT: 'Type your form description',
    SEND_BUTTON: 'Send message',
    AI_STATUS: 'AI connection status',
    MESSAGE_LIST: 'Chat messages',
    COPY_SCHEMA: 'Copy form schema to clipboard',
    MODIFY_FORM: 'Modify generated form',
  },
  
  ROLES: {
    CHAT_LOG: 'log',
    STATUS_BAR: 'status',
    INPUT_AREA: 'form',
  },
  
  LIVE_REGIONS: {
    STATUS_UPDATES: 'polite',
    ERROR_MESSAGES: 'assertive',
  },
};
