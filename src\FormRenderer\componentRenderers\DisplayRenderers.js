/**
 * Display Component Renderers
 * 
 * Renders display components like Avatar, Badge, Image, Tag, etc.
 * These components are primarily for displaying information rather than input.
 */

import React from 'react';
import {
  Avatar,
  Badge,
  Image,
  Tag,
  Button,
  Typography,
} from 'antd';
import { UserOutlined } from '@ant-design/icons';

/**
 * Renders an Avatar component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Avatar component
 */
export const renderAvatar = (component) => {
  return (
    <Avatar
      size={component.styling?.size}
      shape={component.styling?.shape}
      src={component.src}
      alt={component.alt}
      icon={component.styling?.icon ? <UserOutlined /> : undefined}
    />
  );
};

/**
 * Renders a Badge component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Badge component
 */
export const renderBadge = (component) => {
  return (
    <Badge
      count={component.styling?.count}
      showZero={component.styling?.showZero}
      overflowCount={component.styling?.overflowCount}
      dot={component.styling?.dot}
      status={component.styling?.status}
    >
      <div style={{ padding: '8px 16px', background: '#f0f0f0' }}>
        {component.text || 'Badge Content'}
      </div>
    </Badge>
  );
};

/**
 * Renders an Image component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Image component
 */
export const renderImage = (component) => {
  return (
    <Image
      width={component.styling?.width}
      height={component.styling?.height}
      src={component.src}
      alt={component.alt}
      preview={component.styling?.preview}
      fallback={component.styling?.fallback}
    />
  );
};

/**
 * Renders a Tag component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Tag component
 */
export const renderTag = (component) => {
  return (
    <Tag
      color={component.styling?.color}
      closable={component.styling?.closable}
      bordered={component.styling?.bordered}
    >
      {component.text || 'Tag Text'}
    </Tag>
  );
};

/**
 * Renders a Button component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Button component
 */
export const renderButton = (component) => {
  return (
    <Button
      type={component.styling?.type}
      size={component.styling?.size}
      shape={component.styling?.shape}
      block={component.styling?.block}
      danger={component.styling?.danger}
      ghost={component.styling?.ghost}
      loading={component.styling?.loading}
      htmlType={component.htmlType}
      icon={component.styling?.icon}
    >
      {component.text || 'Button Text'}
    </Button>
  );
};

/**
 * Renders a Typography component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Typography component
 */
export const renderTypography = (component) => {
  const { Title, Text, Paragraph } = Typography;
  const TypographyComponent =
    component.component === 'title'
      ? Title
      : component.component === 'paragraph'
      ? Paragraph
      : Text;

  return (
    <TypographyComponent
      level={component.styling?.level}
      type={component.styling?.type}
      disabled={component.styling?.disabled}
      mark={component.styling?.mark}
      code={component.styling?.code}
      keyboard={component.styling?.keyboard}
      underline={component.styling?.underline}
      delete={component.styling?.delete}
      strong={component.styling?.strong}
      italic={component.styling?.italic}
    >
      {component.text || 'Typography Text'}
    </TypographyComponent>
  );
};

/**
 * Renders a Statistic component (custom implementation)
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Statistic component
 */
export const renderStatistic = (component) => {
  return (
    <div style={{ textAlign: 'center' }}>
      <div
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1890ff',
          marginBottom: '8px',
        }}
      >
        {component.prefix}
        {component.value || '112,893'}
        {component.suffix}
      </div>
      <div style={{ fontSize: '14px', color: '#666' }}>
        {component.title || 'Statistic Title'}
      </div>
    </div>
  );
};
