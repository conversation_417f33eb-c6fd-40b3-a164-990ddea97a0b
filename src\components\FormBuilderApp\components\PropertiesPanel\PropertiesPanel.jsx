/**
 * @fileoverview Properties Panel component for form builder
 *
 * This component renders a responsive floating properties panel that appears
 * when users double-click on form builder canvas components. Features include
 * intelligent positioning, three-tab structure, and responsive design.
 *
 * @module PropertiesPanel
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useCallback, useEffect, useMemo } from 'react';
import { Spin } from 'antd';
import { CloseOutlined, SettingOutlined } from '@ant-design/icons';
import { AnimatePresence } from 'framer-motion';
import { usePropertiesPanel } from './hooks/usePropertiesPanel';
import { usePositioning } from './hooks/usePositioning';
import { useSwipeGesture } from './hooks/useSwipeGesture';
import { useEventHandling } from './hooks/useEventHandling';
import { useScrollManagement } from './hooks/useScrollManagement';
import EnhancedPropertiesTab from './components/EnhancedPropertiesTab';
import AdvancedSettingsTab from './components/AdvancedSettingsTab';
import RolesTab from './components/RolesTab';
import {
  PANEL_TABS,
  ANIMATION_VARIANTS,
  RESPONSIVE_BREAKPOINTS,
} from './constants/propertiesPanelConstants';
import * as S from './styles/PropertiesPanel.styles';

/**
 * Properties Panel component
 *
 * Renders a floating properties panel with intelligent positioning,
 * responsive design, and three-tab structure for component configuration.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the panel is visible
 * @param {Object} props.targetElement - DOM element that triggered the panel
 * @param {Object} props.componentData - Data of the component being edited
 * @param {string} props.componentId - ID of the component being edited
 * @param {Function} props.onClose - Callback when panel is closed
 * @param {Function} props.onUpdateComponent - Callback when component is updated
 * @param {Object} props.formSchema - Current form schema for context
 *
 * @returns {JSX.Element} Properties panel component
 */
const PropertiesPanel = ({
  visible,
  targetElement,
  componentData,
  componentId,
  onClose,
  onUpdateComponent,
  formSchema,
}) => {
  // Custom hooks for panel logic
  const {
    activeTab,
    setActiveTab,
    isLoading,
    isMobile,
    handleTabChange,
    handlePropertyUpdate,
    handleClose,
  } = usePropertiesPanel({
    componentData,
    componentId,
    onUpdateComponent,
    onClose,
  });

  const { position, panelStyle, caretPosition, caretOffset } = usePositioning({
    targetElement,
    visible,
    isMobile,
  });

  // Swipe gesture for mobile close
  const { swipeProps } = useSwipeGesture({
    onSwipeDown: handleClose,
    enabled: isMobile && visible,
    threshold: 60,
    velocityThreshold: 0.4,
  });

  // Enhanced event handling with cleanup
  const { handleBackdropClick, panelRef } = useEventHandling({
    visible,
    onClose: handleClose,
    isMobile,
    targetElement,
  });

  // Scroll management for content areas
  const { scrollRef, canScrollUp, canScrollDown, isScrollable, scrollProps } =
    useScrollManagement({
      enabled: visible,
      threshold: 10,
    });

  // Memoized tab content
  const tabContent = useMemo(() => {
    switch (activeTab) {
      case PANEL_TABS.PROPERTIES:
        return (
          <EnhancedPropertiesTab
            componentData={componentData}
            componentId={componentId}
            onPropertyUpdate={handlePropertyUpdate}
            formSchema={formSchema}
          />
        );
      case PANEL_TABS.ADVANCED:
        return (
          <AdvancedSettingsTab
            componentData={componentData}
            componentId={componentId}
            onPropertyUpdate={handlePropertyUpdate}
            formSchema={formSchema}
          />
        );
      case PANEL_TABS.ROLES:
        return (
          <RolesTab
            componentData={componentData}
            componentId={componentId}
            onPropertyUpdate={handlePropertyUpdate}
            formSchema={formSchema}
          />
        );
      default:
        return null;
    }
  }, [activeTab, componentData, componentId, handlePropertyUpdate, formSchema]);

  // Debug logging for caret rendering
  console.log('🎯 [PropertiesPanel] Render state:', {
    visible,
    isMobile,
    caretPosition,
    caretOffset,
    targetElement: targetElement?.tagName,
    shouldShowCaret: !isMobile && visible,
  });

  // Don't render if not visible
  if (!visible) {
    return null;
  }

  return (
    <AnimatePresence>
      {visible && (
        <>
          {/* Mobile backdrop */}
          {isMobile && (
            <S.PanelBackdrop
              variants={ANIMATION_VARIANTS.backdrop}
              initial='hidden'
              animate='visible'
              exit='exit'
              onClick={handleBackdropClick}
            />
          )}

          {/* Main panel */}
          <S.PanelContainer
            ref={panelRef}
            style={panelStyle}
            variants={
              isMobile
                ? ANIMATION_VARIANTS.mobileSheet
                : ANIMATION_VARIANTS.panel
            }
            initial='hidden'
            animate='visible'
            exit='exit'
            tabIndex={-1}
            {...(isMobile ? swipeProps : {})}
          >
            {/* Visual caret for desktop */}
            {!isMobile && (
              <S.PanelCaret
                position={caretPosition}
                offset={caretOffset}
                style={
                  {
                    // Debug styling - remove in production
                    // border: '2px solid red',
                    // backgroundColor: 'yellow',
                  }
                }
                data-caret-position={caretPosition}
                data-caret-offset={caretOffset}
                data-testid='properties-panel-caret'
              />
            )}

            {/* Enhanced panel header */}
            <S.PanelHeader>
              <S.PanelTitle>
                <div className='title-main'>
                  <SettingOutlined />
                  Component Properties
                </div>
                <div className='title-subtitle'>
                  {componentData?.type
                    ? `${
                        componentData.type.charAt(0).toUpperCase() +
                        componentData.type.slice(1)
                      } Component`
                    : 'Configure component settings'}
                </div>
              </S.PanelTitle>
              <S.CloseButton
                onClick={handleClose}
                aria-label='Close properties panel'
              >
                <CloseOutlined />
              </S.CloseButton>
            </S.PanelHeader>

            {/* Panel content */}
            <S.PanelContent>
              {/* Tab navigation */}
              <S.TabNavigation>
                <S.TabButton
                  active={activeTab === PANEL_TABS.PROPERTIES}
                  onClick={() => handleTabChange(PANEL_TABS.PROPERTIES)}
                >
                  Properties
                </S.TabButton>
                <S.TabButton
                  active={activeTab === PANEL_TABS.ADVANCED}
                  onClick={() => handleTabChange(PANEL_TABS.ADVANCED)}
                >
                  Advanced
                </S.TabButton>
                <S.TabButton
                  active={activeTab === PANEL_TABS.ROLES}
                  onClick={() => handleTabChange(PANEL_TABS.ROLES)}
                >
                  Roles
                </S.TabButton>
              </S.TabNavigation>

              {/* Tab content */}
              <S.TabContent
                key={activeTab}
                variants={ANIMATION_VARIANTS.tabContent}
                initial='hidden'
                animate='visible'
                exit='exit'
              >
                {tabContent}
              </S.TabContent>

              {/* Loading overlay */}
              <AnimatePresence>
                {isLoading && (
                  <S.LoadingOverlay
                    variants={ANIMATION_VARIANTS.loading}
                    initial='hidden'
                    animate='visible'
                    exit='exit'
                  >
                    <Spin size='large' tip='Updating component...' />
                  </S.LoadingOverlay>
                )}
              </AnimatePresence>
            </S.PanelContent>
          </S.PanelContainer>
        </>
      )}
    </AnimatePresence>
  );
};

export default PropertiesPanel;
