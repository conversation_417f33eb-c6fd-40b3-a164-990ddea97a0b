/**
 * InlineEditableText Component
 * 
 * Reusable component for inline text editing functionality.
 * Provides click-to-edit behavior with keyboard shortcuts and validation.
 * 
 * Features:
 * - Click-to-edit functionality
 * - Keyboard shortcuts (Enter to save, Escape to cancel)
 * - Auto-save on blur
 * - Placeholder text support
 * - Visual feedback for editable state
 * - Accessibility compliance
 * 
 * @module InlineEditableText
 */

import React, { memo } from 'react';

// Import styled components
import { InlineEditableText as StyledInlineEditableText } from '../styles/StyledComponents';

/**
 * InlineEditableText Component
 * 
 * Renders text that can be edited inline with click-to-edit functionality.
 * Integrates with the useInlineEditing hook for state management.
 * 
 * @param {Object} props - Component props
 * @param {string} props.text - Text to display/edit
 * @param {string} props.propertyName - Property path for editing (supports dot notation)
 * @param {string} props.placeholder - Placeholder text when empty
 * @param {boolean} props.isEditing - Whether currently in editing mode
 * @param {string} props.editingValue - Current editing value
 * @param {Function} props.onLabelClick - Handler for initiating edit
 * @param {Function} props.onEditingChange - Handler for editing value changes
 * @param {Function} props.onEditingKeyDown - Handler for keyboard events
 * @param {Function} props.onEditingBlur - Handler for blur events
 * @param {Object} props.style - Additional styles
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element} Inline editable text component
 */
const InlineEditableText = memo(({
  text,
  propertyName = 'label',
  placeholder = 'Click to edit',
  isEditing = false,
  editingValue = '',
  onLabelClick,
  onEditingChange,
  onEditingKeyDown,
  onEditingBlur,
  style = {},
  className = '',
}) => {
  // Determine display text and placeholder state
  const displayText = text || placeholder;
  const isPlaceholder = !text;

  /**
   * Handles click to start editing
   */
  const handleClick = (e) => {
    if (onLabelClick) {
      onLabelClick(e, text, propertyName);
    }
  };

  /**
   * Handles input value changes
   */
  const handleInputChange = (e) => {
    if (onEditingChange) {
      onEditingChange(e.target.value);
    }
  };

  /**
   * Handles keyboard events
   */
  const handleKeyDown = (e) => {
    if (onEditingKeyDown) {
      onEditingKeyDown(e, propertyName);
    }
  };

  /**
   * Handles blur events
   */
  const handleBlur = () => {
    if (onEditingBlur) {
      onEditingBlur(propertyName);
    }
  };

  /**
   * Gets accessibility attributes
   */
  const getAccessibilityProps = () => {
    if (isEditing) {
      return {
        'aria-label': `Editing ${propertyName}`,
        'aria-describedby': `${propertyName}-help`,
      };
    }
    
    return {
      'aria-label': `Click to edit ${propertyName}`,
      'aria-describedby': `${propertyName}-help`,
      role: 'button',
      tabIndex: 0,
    };
  };

  return (
    <StyledInlineEditableText className={className} style={style}>
      {isEditing ? (
        <input
          className='editing-input'
          value={editingValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          autoFocus
          style={{
            fontSize: 'inherit',
            fontFamily: 'inherit',
            fontWeight: 'inherit',
            color: 'inherit',
          }}
          {...getAccessibilityProps()}
        />
      ) : (
        <span
          className={`editable-text ${isPlaceholder ? 'placeholder-text' : ''}`}
          onClick={handleClick}
          title='Click to edit'
          {...getAccessibilityProps()}
        >
          {displayText}
        </span>
      )}
      
      {/* Hidden help text for screen readers */}
      <span
        id={`${propertyName}-help`}
        style={{
          position: 'absolute',
          left: '-10000px',
          width: '1px',
          height: '1px',
          overflow: 'hidden',
        }}
      >
        {isEditing
          ? 'Press Enter to save, Escape to cancel'
          : 'Click to edit this text'
        }
      </span>
    </StyledInlineEditableText>
  );
});

// Set display name for debugging
InlineEditableText.displayName = 'InlineEditableText';

export default InlineEditableText;
