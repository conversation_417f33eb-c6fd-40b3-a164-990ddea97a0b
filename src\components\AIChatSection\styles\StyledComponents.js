/**
 * AIChatSection Styled Components
 *
 * This module contains all styled-components used in the AIChatSection.
 * Extracted from the main component for better maintainability and reusability.
 *
 * Features:
 * - Enterprise-grade AI Chat styling with 2024+ design standards
 * - Modern glass morphism effects and subtle depth
 * - Responsive design with smooth animations
 * - Accessibility-compliant color contrasts
 * - Microsoft Fluent Design inspired elevation system
 */

import styled from 'styled-components';
import { motion } from 'framer-motion';
import { colors } from '../../../styles/theme';

/**
 * Main container for the AI Chat interface
 * Features modern glass morphism with layered background effects
 */
export const AIChatContainer = styled(motion.div)`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(180deg, #fafbfc 0%, #ffffff 100%);
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;

  /* Modern glass morphism effect */
  backdrop-filter: blur(20px);

  /* Subtle depth without heavy shadows */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(255, 255, 255, 0.4) 100%
    );
    pointer-events: none;
    z-index: 0;
  }

  /* All children above the background */
  > * {
    position: relative;
    z-index: 1;
  }
`;

/**
 * Header section with AI status and branding
 * Includes connection status indicator and enterprise styling
 */
export const ChatHeader = styled(motion.div)`
  padding: 24px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;

  /* Enterprise-grade header styling with connection status indicator */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: ${(props) =>
      props.connected
        ? 'linear-gradient(90deg, #10b981 0%, #059669 100%)'
        : 'linear-gradient(90deg, #f59e0b 0%, #d97706 100%)'};
    opacity: ${(props) => (props.connected ? 1 : 0.8)};
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Subtle bottom separator */
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.06) 50%,
      transparent 100%
    );
  }
`;

/**
 * Scrollable messages container with modern scrollbar styling
 * Features fade effects at top and bottom for better UX
 */
export const ChatMessages = styled(motion.div)`
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  scroll-behavior: smooth;

  /* Modern minimal scrollbar */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.2);
    }
  }

  /* Fade effect at top and bottom for better visual hierarchy */
  &::before,
  &::after {
    content: '';
    position: sticky;
    display: block;
    height: 8px;
    margin: 0 -20px;
    pointer-events: none;
    z-index: 2;
  }

  &::before {
    top: 0;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  &::after {
    bottom: 0;
    background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 0) 100%
    );
  }
`;

/**
 * Individual message bubble container
 * Handles layout for both user and AI messages with proper alignment
 */
export const MessageBubble = styled(motion.div)`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;
  max-width: 100%;

  /* Reverse layout for user messages (right-aligned) */
  ${(props) =>
    props.isUser &&
    `
    flex-direction: row-reverse;
    justify-content: flex-start;
  `}
`;

/**
 * Message content bubble with dynamic styling based on sender
 * Features modern chat bubble design with tail indicators
 */
export const MessageContent = styled(motion.div)`
  max-width: 320px;
  padding: 16px 20px;
  border-radius: 20px;
  font-size: 14px;
  line-height: 1.6;
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
    sans-serif;
  font-weight: 400;

  /* User message styling (blue gradient) */
  ${(props) =>
    props.isUser
      ? `
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: #ffffff;
    border-bottom-right-radius: 6px;
    box-shadow: 0 2px 12px rgba(59, 130, 246, 0.15);
    border: none;

    /* Chat bubble tail for user messages */
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      right: -8px;
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-left-color: #2563eb;
      border-bottom: 0;
      border-right: 0;
      margin-bottom: -8px;
    }
  `
      : `
    /* AI message styling (light background with subtle border) */
    background: rgba(248, 250, 252, 0.8);
    color: #1f2937;
    border-bottom-left-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(10px);

    /* Chat bubble tail for AI messages */
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: -8px;
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-right-color: rgba(248, 250, 252, 0.8);
      border-bottom: 0;
      border-left: 0;
      margin-bottom: -8px;
    }
  `}

  /* Hover effect for enhanced interactivity */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${(props) =>
      props.isUser
        ? '0 4px 20px rgba(59, 130, 246, 0.25)'
        : '0 2px 8px rgba(0, 0, 0, 0.08)'};
  }
`;

/**
 * Chat input area with modern styling and enhanced UX
 * Features gradient background and custom input styling
 */
export const ChatInput = styled(motion.div)`
  padding: 24px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.04);
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  position: relative;

  /* Subtle top separator */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.06) 50%,
      transparent 100%
    );
  }

  /* Enhanced input styling */
  .ant-input {
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    padding: 14px 18px;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
      sans-serif;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      background: #ffffff;
    }

    &::placeholder {
      color: #9ca3af;
      font-weight: 400;
    }
  }

  /* Enhanced primary button styling */
  .ant-btn-primary {
    border-radius: 16px;
    height: auto;
    padding: 14px 20px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
    }

    &:disabled {
      background: #e5e7eb;
      box-shadow: none;
      transform: none;
    }
  }
`;
