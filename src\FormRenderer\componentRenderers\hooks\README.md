# ComponentRenderer Hooks

This directory contains custom React hooks for the Component<PERSON>enderer module.

## Structure

- **useComponentProps.js** - Manages component prop generation and memoization
- **useValidationRules.js** - Handles validation rule generation for components
- **useComponentState.js** - Manages component-specific state and lifecycle
- **useFormItemProps.js** - Generates form item props for Ant Design components

## Usage

These hooks encapsulate complex component logic and provide reusable functionality across different component renderers.
