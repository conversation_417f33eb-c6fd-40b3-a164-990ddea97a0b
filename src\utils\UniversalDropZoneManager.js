/**
 * Universal Drop Zone Manager
 * Enterprise-grade drop zone management system for unlimited nesting support
 * 
 * Features:
 * - Dynamic path generation for any nesting level
 * - Container-specific drop zone behavior
 * - Capability-based validation
 * - Performance optimizations with intelligent memoization
 * - Comprehensive debug logging and error handling
 * 
 * <AUTHOR> Builder Team
 * @version 2.0.0
 */

import { COMPONENT_CAPABILITIES } from '../constants';

/**
 * Universal Drop Zone Manager Class
 * Handles all drop zone operations with enterprise-grade performance
 */
export class UniversalDropZoneManager {
  constructor() {
    this.cache = new Map();
    this.pathCache = new Map();
    this.validationCache = new Map();
    this.debugMode = process.env.NODE_ENV === 'development';
    
    // Performance metrics
    this.metrics = {
      cacheHits: 0,
      cacheMisses: 0,
      pathGenerations: 0,
      validations: 0,
    };
  }

  /**
   * Generates hierarchical path for any component location
   * @param {Object} context - Path generation context
   * @returns {string} Hierarchical path
   */
  generatePath(context) {
    const {
      parentPath = 'root',
      containerType,
      containerIndex,
      stepId,
      tabId,
      rowIndex,
      columnIndex,
      componentIndex,
    } = context;

    this.metrics.pathGenerations++;

    // Build path segments
    const segments = [parentPath];

    // Add container-specific segments
    if (containerType) {
      if (containerIndex !== undefined) {
        segments.push(`${containerType}${containerIndex}`);
      }
      
      // Add container-specific identifiers
      if (stepId !== undefined) segments.push(`step${stepId}`);
      if (tabId !== undefined) segments.push(`tab${tabId}`);
    }

    // Add layout segments
    if (rowIndex !== undefined) segments.push(`row${rowIndex}`);
    if (columnIndex !== undefined) segments.push(`col${columnIndex}`);
    if (componentIndex !== undefined) segments.push(`comp${componentIndex}`);

    const path = segments.join('.');
    
    if (this.debugMode) {
      console.log('🔄 [DropZoneManager] Generated path:', {
        context,
        path,
        segments,
      });
    }

    return path;
  }

  /**
   * Creates universal drop zone data with caching
   * @param {Object} config - Drop zone configuration
   * @returns {Object} Universal drop zone data
   */
  createDropZone(config) {
    const {
      path,
      containerId,
      containerType,
      containerContext = {},
      index = 0,
      acceptedTypes = [],
      capabilities = {},
    } = config;

    // Generate cache key
    const cacheKey = this.generateCacheKey(config);
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      this.metrics.cacheHits++;
      return this.cache.get(cacheKey);
    }

    this.metrics.cacheMisses++;

    // Calculate depth from path
    const depth = path.split('.').length - 1;

    // Create drop zone data
    const dropZone = {
      id: `dropzone_${cacheKey}`,
      path,
      containerId,
      containerType,
      containerContext,
      index,
      depth,
      acceptedTypes: this.resolveAcceptedTypes(containerType, acceptedTypes),
      capabilities: this.resolveCapabilities(containerType, capabilities),
      memoKey: cacheKey,
      lastUpdate: Date.now(),
    };

    // Cache the result
    this.cache.set(cacheKey, dropZone);

    if (this.debugMode) {
      console.log('🎯 [DropZoneManager] Created drop zone:', {
        config,
        dropZone,
        cacheKey,
      });
    }

    return dropZone;
  }

  /**
   * Validates if a drop operation is allowed
   * @param {Object} draggedItem - Item being dragged
   * @param {Object} dropZone - Target drop zone
   * @returns {Object} Validation result
   */
  validateDrop(draggedItem, dropZone) {
    const validationKey = `${draggedItem.type}_${dropZone.memoKey}`;
    
    // Check validation cache
    if (this.validationCache.has(validationKey)) {
      return this.validationCache.get(validationKey);
    }

    this.metrics.validations++;

    const result = this.performValidation(draggedItem, dropZone);
    
    // Cache validation result
    this.validationCache.set(validationKey, result);

    return result;
  }

  /**
   * Performs actual drop validation logic
   * @private
   */
  performValidation(draggedItem, dropZone) {
    const { type: draggedType } = draggedItem;
    const { acceptedTypes, capabilities, depth, containerType } = dropZone;

    // Check if type is accepted
    if (acceptedTypes.length > 0 && !acceptedTypes.includes(draggedType)) {
      return {
        isValid: false,
        reason: `${draggedType} not accepted in ${containerType || 'container'}`,
        code: 'TYPE_NOT_ACCEPTED',
      };
    }

    // Check nesting depth limits
    if (capabilities.maxDepth && depth > capabilities.maxDepth) {
      return {
        isValid: false,
        reason: `Maximum nesting depth (${capabilities.maxDepth}) exceeded`,
        code: 'MAX_DEPTH_EXCEEDED',
      };
    }

    // Check container-specific rules
    const containerValidation = this.validateContainerRules(
      draggedItem,
      dropZone
    );
    if (!containerValidation.isValid) {
      return containerValidation;
    }

    return {
      isValid: true,
      reason: 'Drop operation is valid',
      code: 'VALID',
    };
  }

  /**
   * Validates container-specific drop rules
   * @private
   */
  validateContainerRules(draggedItem, dropZone) {
    const { containerType, capabilities } = dropZone;
    const { type: draggedType } = draggedItem;

    // Get container capabilities
    const containerCaps = COMPONENT_CAPABILITIES[containerType];
    if (!containerCaps) {
      return { isValid: true, reason: 'No specific rules', code: 'NO_RULES' };
    }

    // Check if container can contain this type
    if (containerCaps.canContain && !containerCaps.canContain.includes(draggedType)) {
      return {
        isValid: false,
        reason: `${containerType} cannot contain ${draggedType}`,
        code: 'CONTAINER_RESTRICTION',
      };
    }

    // Check maximum children limit
    if (capabilities.childrenCount >= containerCaps.maxChildren) {
      return {
        isValid: false,
        reason: `Maximum children limit (${containerCaps.maxChildren}) reached`,
        code: 'MAX_CHILDREN_EXCEEDED',
      };
    }

    return { isValid: true, reason: 'Container rules satisfied', code: 'VALID' };
  }

  /**
   * Resolves accepted types for a container
   * @private
   */
  resolveAcceptedTypes(containerType, customTypes = []) {
    if (customTypes.length > 0) return customTypes;

    const capabilities = COMPONENT_CAPABILITIES[containerType];
    return capabilities?.canContain || [];
  }

  /**
   * Resolves capabilities for a container
   * @private
   */
  resolveCapabilities(containerType, customCapabilities = {}) {
    const defaultCapabilities = COMPONENT_CAPABILITIES[containerType] || {};
    return { ...defaultCapabilities, ...customCapabilities };
  }

  /**
   * Generates cache key for drop zone configuration
   * @private
   */
  generateCacheKey(config) {
    const keyParts = [
      config.path,
      config.containerId,
      config.containerType,
      config.index,
      JSON.stringify(config.containerContext),
    ];
    
    return keyParts.filter(Boolean).join('|');
  }

  /**
   * Clears all caches - useful for development and testing
   */
  clearCache() {
    this.cache.clear();
    this.pathCache.clear();
    this.validationCache.clear();
    
    if (this.debugMode) {
      console.log('🧹 [DropZoneManager] Cache cleared');
    }
  }

  /**
   * Gets performance metrics
   */
  getMetrics() {
    const cacheHitRate = this.metrics.cacheHits / 
      (this.metrics.cacheHits + this.metrics.cacheMisses) * 100;

    return {
      ...this.metrics,
      cacheHitRate: cacheHitRate.toFixed(2) + '%',
      cacheSize: this.cache.size,
    };
  }

  /**
   * Parses hierarchical path into components
   * @param {string} path - Hierarchical path
   * @returns {Object} Parsed path components
   */
  parsePath(path) {
    const segments = path.split('.');
    const parsed = {
      root: segments[0],
      containers: [],
      layout: {},
    };

    for (let i = 1; i < segments.length; i++) {
      const segment = segments[i];
      
      // Parse container segments
      if (segment.includes('Container') || segment.includes('step') || segment.includes('tab')) {
        parsed.containers.push(segment);
      }
      
      // Parse layout segments
      if (segment.startsWith('row')) {
        parsed.layout.rowIndex = parseInt(segment.replace('row', ''), 10);
      } else if (segment.startsWith('col')) {
        parsed.layout.columnIndex = parseInt(segment.replace('col', ''), 10);
      } else if (segment.startsWith('comp')) {
        parsed.layout.componentIndex = parseInt(segment.replace('comp', ''), 10);
      }
    }

    return parsed;
  }
}

// Export singleton instance
export const dropZoneManager = new UniversalDropZoneManager();

// Export for testing
export default UniversalDropZoneManager;
