import React, { useRef, memo, useState, useCallback, useMemo } from 'react';
import { useDrag } from 'react-dnd';
import { Tabs, Button, Modal, Form, Input, Space, Tooltip } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DragOutlined,
} from '@ant-design/icons';
import { TAB_CONTAINER } from '../../../constants';
import DropZone from '../DropZone';
import Component from '../Component';

/**
 * Enhanced TabContainer component with full drag-and-drop functionality
 * Supports multiple tabs with individual drop zones for each tab content area
 * Follows the same pattern as StepsContainer for consistency
 */
const TabContainer = memo(
  ({ data, components, handleDrop, path, onUpdateComponent }) => {
    const ref = useRef(null);
    const [form] = Form.useForm();

    // Local state for tab management
    const [currentTab, setCurrentTab] = useState(0);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [editingTab, setEditingTab] = useState(null);

    // Get tabs from data with fallback to empty array
    const tabs = data?.tabs || [];

    // Memoized drag item
    const dragItem = useMemo(
      () => ({
        id: data?.id,
        type: TAB_CONTAINER,
        tabs: tabs,
        path: path || `tabs-${data?.id}`,
      }),
      [data?.id, tabs, path],
    );

    // Memoized component renderer
    const renderComponent = useCallback(
      (component, currentPath) => {
        return (
          <Component
            key={component.id}
            data={component}
            components={components}
            path={currentPath}
            handleDrop={handleDrop}
            onUpdateComponent={onUpdateComponent}
          />
        );
      },
      [components, handleDrop, onUpdateComponent],
    );

    const [{ isDragging }, drag] = useDrag({
      type: TAB_CONTAINER,
      item: dragItem,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    drag(ref);

    // Tab management functions
    const handleAddTab = useCallback(() => {
      const currentTabs = tabs || [];
      const tabNumber = currentTabs.length + 1;
      const timestamp = Date.now();

      const newTab = {
        id: `tab_${timestamp}`,
        key: `tab_${timestamp}`,
        label: `Tab ${tabNumber}`,
        children: [],
      };

      const updatedTabs = [...currentTabs, newTab];

      // Update both the layout and component state
      if (onUpdateComponent && data.id) {
        onUpdateComponent(data.id, { tabs: updatedTabs });
      }
    }, [data.id, tabs, onUpdateComponent]);

    const handleEditTab = useCallback(
      (tab) => {
        setEditingTab(tab);
        form.setFieldsValue({
          label: tab.label,
        });
        setIsEditModalVisible(true);
      },
      [form],
    );

    const handleDeleteTab = useCallback(
      (tabToDelete) => {
        const updatedTabs = tabs.filter((tab) => tab.id !== tabToDelete.id);

        // Adjust current tab if necessary
        if (currentTab >= updatedTabs.length && updatedTabs.length > 0) {
          setCurrentTab(updatedTabs.length - 1);
        } else if (updatedTabs.length === 0) {
          setCurrentTab(0);
        }

        if (onUpdateComponent && data.id) {
          onUpdateComponent(data.id, { tabs: updatedTabs });
        }
      },
      [tabs, currentTab, data.id, onUpdateComponent],
    );

    const handleSaveTab = useCallback(() => {
      form
        .validateFields()
        .then((values) => {
          const updatedTabs = tabs.map((tab) =>
            tab.id === editingTab.id
              ? {
                  ...tab,
                  label: values.label,
                }
              : tab,
          );

          if (onUpdateComponent && data.id) {
            onUpdateComponent(data.id, { tabs: updatedTabs });
          }

          setIsEditModalVisible(false);
          setEditingTab(null);
          form.resetFields();
        })
        .catch((info) => {
          console.log('Validate Failed:', info);
        });
    }, [form, tabs, editingTab, data.id, onUpdateComponent]);

    // Render tab content with drop zones
    const renderTabContent = useCallback(
      (tab, tabIndex) => {
        const children = tab.children || [];

        console.log('🎯 [TabContainer] Rendering tab content:', {
          tabId: tab.id,
          tabIndex,
          childrenCount: children.length,
          containerId: data.id,
        });

        return (
          <div
            key={`tab-content-${tab.id}`}
            style={{
              minHeight: '200px',
              padding: '16px',
              border: '1px dashed #d9d9d9',
              borderRadius: '4px',
              background: '#fafafa',
            }}
          >
            {children.length === 0 ? (
              // Empty state drop zone
              <DropZone
                data={{
                  path: `${path}-tab-${tabIndex}-0`,
                  childrenCount: 0,
                  containerId: data.id,
                  tabId: tab.id,
                  containerType: 'tab-content',
                }}
                onDrop={handleDrop}
                className='tab-empty-drop-zone'
              />
            ) : (
              // Render existing components with drop zones between them
              <>
                {children.map((child, index) => {
                  const currentPath = `${path}-tab-${tabIndex}-${index}`;
                  return (
                    <React.Fragment key={`tab-child-${child.id}`}>
                      <DropZone
                        data={{
                          path: currentPath,
                          childrenCount: children.length,
                          containerId: data.id,
                          tabId: tab.id,
                          containerType: 'tab-content',
                          index: index,
                        }}
                        onDrop={handleDrop}
                        className='tab-drop-zone'
                      />
                      {renderComponent(child, currentPath)}
                    </React.Fragment>
                  );
                })}
                {/* Final drop zone after all components */}
                <DropZone
                  data={{
                    path: `${path}-tab-${tabIndex}-${children.length}`,
                    childrenCount: children.length,
                    containerId: data.id,
                    tabId: tab.id,
                    containerType: 'tab-content',
                    index: children.length,
                  }}
                  onDrop={handleDrop}
                  isLast={true}
                  className='tab-drop-zone-last'
                />
              </>
            )}
          </div>
        );
      },
      [data.id, path, handleDrop, renderComponent],
    );

    // Create tab items for Ant Design Tabs component
    const tabItems = useMemo(() => {
      return tabs.map((tab, index) => ({
        key: tab.key,
        label: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <span>{tab.label}</span>
            <Space size={2}>
              <Tooltip title='Edit Tab'>
                <Button
                  type='text'
                  size='small'
                  icon={<EditOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditTab(tab);
                  }}
                  style={{ padding: '0 4px' }}
                />
              </Tooltip>
              {tabs.length > 1 && (
                <Tooltip title='Delete Tab'>
                  <Button
                    type='text'
                    size='small'
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteTab(tab);
                    }}
                    style={{ padding: '0 4px', color: '#ff4d4f' }}
                  />
                </Tooltip>
              )}
            </Space>
          </div>
        ),
        children: renderTabContent(tab, index),
      }));
    }, [tabs, handleEditTab, handleDeleteTab, renderTabContent]);

    return (
      <>
        <div
          ref={ref}
          style={{
            opacity: isDragging ? 0.5 : 1,
            border: '2px solid #d9d9d9',
            borderRadius: '6px',
            margin: '8px 0',
            minHeight: '300px',
            background: '#fff',
            position: 'relative',
          }}
        >
          {/* Drag handle */}
          <div
            style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              cursor: 'move',
              color: '#8c8c8c',
              zIndex: 10,
            }}
          >
            <DragOutlined />
          </div>

          {/* Header with Add Tab button */}
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '8px 16px',
              background: '#fafafa',
              borderBottom: '1px solid #d9d9d9',
              borderRadius: '6px 6px 0 0',
            }}
          >
            <span style={{ fontWeight: '500', color: '#666' }}>
              Tab Container ({tabs.length} tabs)
            </span>
            <Button
              type='dashed'
              size='small'
              icon={<PlusOutlined />}
              onClick={handleAddTab}
            >
              Add Tab
            </Button>
          </div>

          {/* Tab content */}
          <div style={{ padding: '16px' }}>
            {tabs.length > 0 ? (
              <Tabs
                activeKey={tabs[currentTab]?.key}
                onChange={(key) => {
                  const tabIndex = tabs.findIndex((tab) => tab.key === key);
                  if (tabIndex !== -1) {
                    setCurrentTab(tabIndex);
                  }
                }}
                items={tabItems}
                type='line'
                size='default'
                tabPosition='top'
              />
            ) : (
              // Empty state when no tabs
              <div
                style={{
                  textAlign: 'center',
                  padding: '40px',
                  color: '#8c8c8c',
                  background: '#fafafa',
                  border: '1px dashed #d9d9d9',
                  borderRadius: '4px',
                }}
              >
                <p style={{ margin: '0 0 16px 0', fontSize: '16px' }}>
                  No tabs created yet
                </p>
                <Button
                  type='primary'
                  icon={<PlusOutlined />}
                  onClick={handleAddTab}
                >
                  Create First Tab
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Edit Tab Modal */}
        <Modal
          title='Edit Tab'
          open={isEditModalVisible}
          onOk={handleSaveTab}
          onCancel={() => {
            setIsEditModalVisible(false);
            setEditingTab(null);
            form.resetFields();
          }}
          okText='Save'
          cancelText='Cancel'
        >
          <Form form={form} layout='vertical'>
            <Form.Item
              name='label'
              label='Tab Label'
              rules={[
                { required: true, message: 'Please enter tab label' },
                { min: 1, max: 50, message: 'Label must be 1-50 characters' },
              ]}
            >
              <Input placeholder='Enter tab label' />
            </Form.Item>
          </Form>
        </Modal>
      </>
    );
  },
  (prevProps, nextProps) => {
    // Memoization comparison for performance
    return (
      prevProps.data?.id === nextProps.data?.id &&
      prevProps.data?.tabs?.length === nextProps.data?.tabs?.length &&
      JSON.stringify(prevProps.data?.tabs) ===
        JSON.stringify(nextProps.data?.tabs)
    );
  },
);

TabContainer.displayName = 'TabContainer';

export default TabContainer;
