import React, { useRef, memo } from 'react';
import { useDrag } from 'react-dnd';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { TAB_CONTAINER } from '../../../constants';

// EMERGENCY SIMPLE TEST VERSION
const TabContainer = memo(
  ({ data, components, handleDrop, path, onUpdateComponent }) => {
    const ref = useRef(null);

    const [{ isDragging }, drag] = useDrag({
      type: TAB_CONTAINER,
      item: { id: data?.id, type: TAB_CONTAINER },
      collect: (monitor) => ({ isDragging: monitor.isDragging() }),
    });

    drag(ref);

    return (
      <div
        ref={ref}
        style={{
          opacity: isDragging ? 0.5 : 1,
          border: '2px dashed #d9d9d9',
          borderRadius: '6px',
          margin: '8px 0',
          minHeight: '300px',
          background: '#fafafa',
        }}
      >
        {/* EMERGENCY DEBUG */}
        <div
          style={{
            background: 'blue',
            color: 'white',
            padding: '10px',
            fontSize: '16px',
            fontWeight: 'bold',
          }}
        >
          🚨 TAB CONTAINER IS RENDERING! 🚨
        </div>

        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '8px 16px',
            background: '#fff',
            borderBottom: '1px solid #d9d9d9',
          }}
        >
          <span style={{ fontWeight: '500', color: '#666' }}>
            Tab Container
          </span>
          <Button type='dashed' size='small' icon={<PlusOutlined />}>
            Add Tab
          </Button>
        </div>

        <div
          style={{
            background: 'lightblue',
            padding: '40px',
            fontSize: '16px',
            textAlign: 'center',
            minHeight: '200px',
            border: '2px dashed blue',
          }}
        >
          🎯 TAB CONTENT AREA - DRAG COMPONENTS HERE 🎯
          <br />
          <br />
          This is where tab components will be dropped
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.data?.id !== nextProps.data?.id;
  },
);

TabContainer.displayName = 'TabContainer';

export default TabContainer;
