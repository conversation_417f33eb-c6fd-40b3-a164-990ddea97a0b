/**
 * @fileoverview Advanced Component Management System barrel exports
 *
 * This module provides comprehensive component management features including
 * visual hierarchy tree, usage analytics, advanced search, and component templates
 * for enterprise form builder usage.
 *
 * @module ComponentManagement
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

// Main management components
export { default as ComponentHierarchyTree } from './components/ComponentHierarchyTree';
export { default as ComponentAnalytics } from './components/ComponentAnalytics';
export { default as ComponentSearch } from './components/ComponentSearch';
export { default as ComponentTemplates } from './components/ComponentTemplates';
export { default as ComponentInspector } from './components/ComponentInspector';

// Management panels
export { default as ComponentManagerPanel } from './components/ComponentManagerPanel';
export { default as HierarchyPanel } from './components/HierarchyPanel';
export { default as AnalyticsPanel } from './components/AnalyticsPanel';
export { default as TemplatesPanel } from './components/TemplatesPanel';

// Search and filter components
export { default as AdvancedSearch } from './components/AdvancedSearch';
export { default as ComponentFilter } from './components/ComponentFilter';
export { default as SearchResults } from './components/SearchResults';

// Template management
export { default as TemplateCreator } from './components/TemplateCreator';
export { default as TemplateLibrary } from './components/TemplateLibrary';
export { default as TemplatePreview } from './components/TemplatePreview';

// Analytics components
export { default as UsageMetrics } from './components/UsageMetrics';
export { default as ComponentStats } from './components/ComponentStats';
export { default as PerformanceAnalysis } from './components/PerformanceAnalysis';

// Hooks
export { useComponentHierarchy } from './hooks/useComponentHierarchy';
export { useComponentAnalytics } from './hooks/useComponentAnalytics';
export { useComponentSearch } from './hooks/useComponentSearch';
export { useComponentTemplates } from './hooks/useComponentTemplates';
export { useComponentSelection } from './hooks/useComponentSelection';

// Constants
export * from './constants/managementConstants';

// Utilities
export * from './utils/hierarchyUtils';
export * from './utils/analyticsUtils';
export * from './utils/searchUtils';
export * from './utils/templateUtils';
