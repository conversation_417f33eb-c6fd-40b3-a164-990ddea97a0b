import { useCallback } from 'react';
import { message } from 'antd';

/**
 * Custom hook for managing form state and lifecycle
 * 
 * Provides utilities for form reset, state management, and lifecycle events.
 * Centralizes form state operations for better maintainability.
 * 
 * @param {Object} form - Ant Design form instance
 * @returns {Object} Form state management utilities
 * @returns {Function} returns.handleReset - Function to reset form fields
 */
export const useFormState = (form) => {
  /**
   * Handles form reset with user feedback
   * 
   * Resets all form fields to their initial values and provides
   * user feedback through a message notification.
   */
  const handleReset = useCallback(() => {
    if (!form) {
      console.warn('Form instance not available for reset');
      return;
    }

    form.resetFields();
    message.info('Form has been reset');
  }, [form]);

  return {
    handleReset,
  };
};
