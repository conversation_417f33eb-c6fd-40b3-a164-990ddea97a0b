{"name": "react-dnd-form-builder-optimized", "version": "1.0.0", "description": "Enterprise-scale React drag-and-drop form builder with comprehensive performance optimizations", "private": true, "dependencies": {"antd": "^5.26.0", "classnames": "2.5.1", "framer-motion": "^11.18.2", "groq-sdk": "^0.25.0", "immutability-helper": "^3.1.1", "lodash": "4.17.21", "react": "19.1.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "19.1.0", "react-frame-component": "^5.2.7", "react-scripts": "5.0.1", "react-use": "^17.6.0", "shortid": "2.2.17", "styled-components": "^6.1.19"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js", "build:profile": "npm run build -- --profile", "build:analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js --mode server", "serve": "npx serve -s build", "performance:test": "node -e \"console.log('Run performance tests in browser console: window.__PERFORMANCE_BENCHMARK__()')\"", "performance:monitor": "node -e \"console.log('Performance monitoring active in development mode')\"", "size:check": "npm run build && du -sh build/static/js/*.js", "optimize": "npm run build:analyze && npm run size:check"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}