/**
 * <PERSON>ton Styling Components and Utilities
 * 
 * Styled components and utilities specifically for form buttons.
 * Provides consistent button styling across the FormRenderer.
 */

import styled from 'styled-components';
import { formTheme } from './FormStyles';

/**
 * Primary form button (typically submit button)
 * 
 * Styled component for primary action buttons with consistent theming.
 */
export const PrimaryButton = styled.button`
  background-color: ${formTheme.colors.primary};
  border: 1px solid ${formTheme.colors.primary};
  color: white;
  padding: 8px 16px;
  border-radius: ${formTheme.borderRadius.md};
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    box-shadow: ${formTheme.shadows.sm};
  }
  
  &:active {
    background-color: #096dd9;
    border-color: #096dd9;
  }
  
  &:disabled {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }
  
  /* Loading state */
  &.loading {
    position: relative;
    pointer-events: none;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 14px;
      height: 14px;
      margin: -7px 0 0 -7px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

/**
 * Secondary form button (typically reset/cancel button)
 * 
 * Styled component for secondary action buttons.
 */
export const SecondaryButton = styled.button`
  background-color: white;
  border: 1px solid ${formTheme.colors.border};
  color: ${formTheme.colors.text};
  padding: 8px 16px;
  border-radius: ${formTheme.borderRadius.md};
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: ${formTheme.colors.primary};
    color: ${formTheme.colors.primary};
  }
  
  &:active {
    border-color: #096dd9;
    color: #096dd9;
  }
  
  &:disabled {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }
`;

/**
 * Button group container for form actions
 * 
 * Provides consistent spacing and alignment for button groups.
 */
export const ButtonGroup = styled.div`
  display: flex;
  gap: ${formTheme.spacing.sm};
  align-items: center;
  justify-content: ${props => props.align || 'flex-start'};
  
  /* Responsive behavior */
  @media (max-width: 576px) {
    flex-direction: column;
    gap: ${formTheme.spacing.md};
    
    button {
      width: 100%;
    }
  }
`;

/**
 * Button size variants
 */
export const buttonSizeStyles = {
  small: {
    padding: '4px 8px',
    fontSize: '12px',
    borderRadius: formTheme.borderRadius.sm,
  },
  middle: {
    padding: '8px 16px',
    fontSize: '14px',
    borderRadius: formTheme.borderRadius.md,
  },
  large: {
    padding: '12px 24px',
    fontSize: '16px',
    borderRadius: formTheme.borderRadius.lg,
  },
};

/**
 * Button type variants
 */
export const buttonTypeStyles = {
  primary: {
    backgroundColor: formTheme.colors.primary,
    borderColor: formTheme.colors.primary,
    color: 'white',
  },
  default: {
    backgroundColor: 'white',
    borderColor: formTheme.colors.border,
    color: formTheme.colors.text,
  },
  dashed: {
    backgroundColor: 'white',
    borderColor: formTheme.colors.border,
    borderStyle: 'dashed',
    color: formTheme.colors.text,
  },
  text: {
    backgroundColor: 'transparent',
    border: 'none',
    color: formTheme.colors.text,
  },
  link: {
    backgroundColor: 'transparent',
    border: 'none',
    color: formTheme.colors.primary,
    textDecoration: 'none',
  },
};
