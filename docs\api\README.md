# 📋 API Reference

## Core Components API

### FormBuilderApp

Main application component that provides the complete form builder interface.

```typescript
interface FormBuilderAppProps {
  /** Initial form schema to load */
  initialSchema?: FormSchema;
  /** Callback when form schema changes */
  onSchemaChange?: (schema: FormSchema) => void;
  /** Application configuration */
  config?: FormBuilderConfig;
  /** Custom component registry */
  customComponents?: ComponentRegistry;
  /** Theme configuration */
  theme?: ThemeConfig;
}
```

**Usage:**
```jsx
<FormBuilderApp
  initialSchema={mySchema}
  onSchemaChange={handleSchemaChange}
  config={{
    enableAI: true,
    enableAnalytics: true,
    autoSave: true
  }}
/>
```

### EnterpriseHeader

Advanced header component with project management capabilities.

```typescript
interface EnterpriseHeaderProps {
  /** Current project information */
  project?: ProjectInfo;
  /** Form metrics data */
  metrics?: FormMetrics;
  /** Save callback */
  onSave?: () => Promise<void>;
  /** Export callback */
  onExport?: (format: ExportFormat) => void;
  /** Import callback */
  onImport?: (file: File) => void;
  /** Auto-save enabled */
  autoSave?: boolean;
}
```

### PropertiesPanel

Enhanced properties panel with visual editors.

```typescript
interface PropertiesPanelProps {
  /** Currently selected component */
  selectedComponent?: ComponentData;
  /** Component update callback */
  onComponentUpdate?: (id: string, updates: Partial<ComponentData>) => void;
  /** Panel visibility */
  visible?: boolean;
  /** Panel position */
  position?: PanelPosition;
  /** Panel configuration */
  config?: PropertiesPanelConfig;
}
```

### ComponentHierarchyTree

Visual component hierarchy with management capabilities.

```typescript
interface ComponentHierarchyTreeProps {
  /** Array of form components */
  components: ComponentData[];
  /** Selected component ID */
  selectedComponentId?: string;
  /** Component selection callback */
  onComponentSelect?: (id: string) => void;
  /** Component update callback */
  onComponentUpdate?: (id: string, updates: Partial<ComponentData>) => void;
  /** Component deletion callback */
  onComponentDelete?: (id: string) => void;
  /** Show search functionality */
  showSearch?: boolean;
}
```

## Custom Hooks API

### useFormBuilder

Core form builder state management hook.

```typescript
interface UseFormBuilderReturn {
  /** Current form schema */
  schema: FormSchema;
  /** Form layout data */
  layout: ComponentData[];
  /** Update entire schema */
  updateSchema: (schema: FormSchema) => void;
  /** Add new component */
  addComponent: (component: ComponentData, path?: string) => void;
  /** Remove component */
  removeComponent: (id: string) => void;
  /** Update component */
  updateComponent: (id: string, updates: Partial<ComponentData>) => void;
  /** Undo last action */
  undo: () => void;
  /** Redo last action */
  redo: () => void;
  /** Check if can undo */
  canUndo: boolean;
  /** Check if can redo */
  canRedo: boolean;
}
```

### usePropertiesPanel

Properties panel state and interaction management.

```typescript
interface UsePropertiesPanelReturn {
  /** Panel visibility state */
  isVisible: boolean;
  /** Panel position */
  position: PanelPosition;
  /** Currently selected component */
  selectedComponent: ComponentData | null;
  /** Show panel */
  showPanel: (componentId: string, position?: PanelPosition) => void;
  /** Hide panel */
  hidePanel: () => void;
  /** Update panel position */
  updatePosition: (position: PanelPosition) => void;
  /** Update component property */
  updateProperty: (property: string, value: any) => void;
}
```

### useComponentAnalytics

Component usage analytics and metrics.

```typescript
interface UseComponentAnalyticsReturn {
  /** Analytics data */
  analytics: AnalyticsData;
  /** Loading state */
  isLoading: boolean;
  /** Refresh analytics */
  refresh: () => void;
  /** Get component usage */
  getComponentUsage: (componentId: string) => UsageMetrics;
  /** Get popular components */
  getPopularComponents: (limit?: number) => ComponentData[];
  /** Get performance metrics */
  getPerformanceMetrics: () => PerformanceMetrics;
}
```

### useDevicePreview

Device preview and responsive testing.

```typescript
interface UseDevicePreviewReturn {
  /** Selected device */
  selectedDevice: string;
  /** Current zoom level */
  zoom: number;
  /** Device orientation */
  orientation: 'portrait' | 'landscape';
  /** Set selected device */
  setSelectedDevice: (device: string) => void;
  /** Set zoom level */
  setZoom: (zoom: number) => void;
  /** Toggle orientation */
  toggleOrientation: () => void;
  /** Device configuration */
  deviceConfig: DeviceConfig;
}
```

## Type Definitions

### Core Types

```typescript
interface FormSchema {
  id: string;
  title: string;
  description?: string;
  version: string;
  components: ComponentData[];
  settings: FormSettings;
  metadata: FormMetadata;
}

interface ComponentData {
  id: string;
  type: string;
  label: string;
  properties: Record<string, any>;
  style: ComponentStyle;
  validation: ValidationRules;
  children?: ComponentData[];
  metadata: ComponentMetadata;
}

interface ComponentStyle {
  width?: string | number;
  height?: string | number;
  margin?: string;
  padding?: string;
  backgroundColor?: string;
  color?: string;
  fontSize?: string;
  fontWeight?: string | number;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  borderRadius?: string;
  border?: string;
  boxShadow?: string;
}

interface ValidationRules {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
  custom?: (value: any) => boolean | string;
}
```

### Configuration Types

```typescript
interface FormBuilderConfig {
  /** Enable AI form generation */
  enableAI?: boolean;
  /** Enable analytics tracking */
  enableAnalytics?: boolean;
  /** Auto-save interval in seconds */
  autoSave?: boolean | number;
  /** Maximum components per form */
  maxComponents?: number;
  /** Custom component registry */
  customComponents?: ComponentRegistry;
  /** API endpoints */
  api?: {
    save?: string;
    load?: string;
    export?: string;
    ai?: string;
  };
}

interface PropertiesPanelConfig {
  /** Default panel width */
  width?: number;
  /** Enable floating mode */
  floating?: boolean;
  /** Collision detection */
  collisionDetection?: boolean;
  /** Animation settings */
  animations?: boolean;
  /** Tab configuration */
  tabs?: PanelTab[];
}

interface DeviceConfig {
  key: string;
  name: string;
  width: number;
  height: number;
  scale: number;
  userAgent: string;
  category: 'mobile' | 'tablet' | 'desktop';
}
```

### Event Types

```typescript
interface ComponentUpdateEvent {
  componentId: string;
  property: string;
  oldValue: any;
  newValue: any;
  timestamp: number;
}

interface FormSaveEvent {
  schema: FormSchema;
  timestamp: number;
  success: boolean;
  error?: string;
}

interface ComponentDragEvent {
  componentType: string;
  sourcePosition: Position;
  targetPosition: Position;
  dropZone: string;
}
```

## Utility Functions

### Schema Utilities

```typescript
/** Validate form schema */
function validateSchema(schema: FormSchema): ValidationResult;

/** Convert schema to different format */
function convertSchema(schema: FormSchema, format: 'json' | 'yaml' | 'xml'): string;

/** Merge schemas */
function mergeSchemas(base: FormSchema, override: Partial<FormSchema>): FormSchema;

/** Extract component by ID */
function findComponent(schema: FormSchema, componentId: string): ComponentData | null;

/** Get component path */
function getComponentPath(schema: FormSchema, componentId: string): string[];
```

### Component Utilities

```typescript
/** Generate unique component ID */
function generateComponentId(type: string): string;

/** Clone component with new IDs */
function cloneComponent(component: ComponentData): ComponentData;

/** Validate component data */
function validateComponent(component: ComponentData): ValidationResult;

/** Get component dependencies */
function getComponentDependencies(component: ComponentData): string[];
```

### Performance Utilities

```typescript
/** Measure component render time */
function measureRenderTime(componentId: string): number;

/** Get memory usage */
function getMemoryUsage(): MemoryInfo;

/** Optimize schema for performance */
function optimizeSchema(schema: FormSchema): FormSchema;

/** Calculate complexity score */
function calculateComplexity(schema: FormSchema): number;
```

## Error Handling

### Error Types

```typescript
class FormBuilderError extends Error {
  code: string;
  details?: any;
  constructor(message: string, code: string, details?: any);
}

class ValidationError extends FormBuilderError {
  field: string;
  value: any;
}

class ComponentError extends FormBuilderError {
  componentId: string;
  componentType: string;
}
```

### Error Codes

- `SCHEMA_INVALID` - Invalid form schema
- `COMPONENT_NOT_FOUND` - Component not found
- `VALIDATION_FAILED` - Validation failed
- `SAVE_FAILED` - Save operation failed
- `LOAD_FAILED` - Load operation failed
- `EXPORT_FAILED` - Export operation failed
- `AI_REQUEST_FAILED` - AI request failed

## Events

### Event System

```typescript
interface EventEmitter {
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
  emit(event: string, data?: any): void;
}

// Available events
const EVENTS = {
  SCHEMA_CHANGED: 'schema:changed',
  COMPONENT_ADDED: 'component:added',
  COMPONENT_UPDATED: 'component:updated',
  COMPONENT_REMOVED: 'component:removed',
  COMPONENT_SELECTED: 'component:selected',
  FORM_SAVED: 'form:saved',
  FORM_LOADED: 'form:loaded',
  VALIDATION_FAILED: 'validation:failed',
  AI_RESPONSE: 'ai:response'
};
```

---

**Note**: This API is designed to be stable and backward-compatible. Breaking changes will be clearly documented and versioned.
