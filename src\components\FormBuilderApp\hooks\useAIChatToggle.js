/**
 * @fileoverview AI Chat Toggle Hook
 *
 * Custom hook for managing AI chat panel visibility state with localStorage persistence.
 * Provides centralized state management for the AI chat toggle functionality across
 * the form builder application.
 *
 * @module useAIChatToggle
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';

// Constants
const STORAGE_KEY = 'formBuilder_aiChatEnabled';
const DEFAULT_AI_CHAT_ENABLED = true;

/**
 * Custom hook for managing AI chat panel visibility
 *
 * Provides state management for AI chat panel visibility with localStorage persistence,
 * smooth animations, and proper state synchronization across components.
 *
 * Features:
 * - localStorage persistence across browser sessions
 * - Default enabled state for new users
 * - Smooth state transitions with callbacks
 * - Error handling for localStorage operations
 * - Debug logging for development
 *
 * @returns {Object} AI chat toggle state and handlers
 * @returns {boolean} returns.isAIChatEnabled - Current AI chat visibility state
 * @returns {Function} returns.toggleAIChat - Function to toggle AI chat visibility
 * @returns {Function} returns.setAIChatEnabled - Function to directly set AI chat state
 * @returns {boolean} returns.isTransitioning - Whether a transition is in progress
 *
 * @example
 * ```jsx
 * const {
 *   isAIChatEnabled,
 *   toggleAIChat,
 *   setAIChatEnabled,
 *   isTransitioning
 * } = useAIChatToggle();
 *
 * // Toggle AI chat visibility
 * const handleToggle = () => {
 *   toggleAIChat();
 * };
 *
 * // Directly set state
 * const enableAIChat = () => {
 *   setAIChatEnabled(true);
 * };
 * ```
 */
export const useAIChatToggle = () => {
  // State for AI chat visibility
  const [isAIChatEnabled, setIsAIChatEnabledState] = useState(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored !== null) {
        const parsed = JSON.parse(stored);
        console.log('🔍 [useAIChatToggle] Loaded from localStorage:', parsed);
        return parsed;
      }
      console.log('🔍 [useAIChatToggle] Using default state:', DEFAULT_AI_CHAT_ENABLED);
      return DEFAULT_AI_CHAT_ENABLED;
    } catch (error) {
      console.warn('🚨 [useAIChatToggle] Failed to load from localStorage:', error);
      return DEFAULT_AI_CHAT_ENABLED;
    }
  });

  // State for transition management
  const [isTransitioning, setIsTransitioning] = useState(false);

  /**
   * Persist state to localStorage
   */
  const persistState = useCallback((newState) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
      console.log('✅ [useAIChatToggle] Persisted to localStorage:', newState);
    } catch (error) {
      console.error('🚨 [useAIChatToggle] Failed to persist to localStorage:', error);
      message.warning('Failed to save AI chat preference');
    }
  }, []);

  /**
   * Set AI chat enabled state with persistence
   */
  const setAIChatEnabled = useCallback((enabled) => {
    console.log('🔄 [useAIChatToggle] Setting AI chat enabled:', enabled);
    
    setIsTransitioning(true);
    setIsAIChatEnabledState(enabled);
    persistState(enabled);
    
    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 300); // Match Framer Motion animation duration
    
    // Show user feedback
    message.info(`AI Chat ${enabled ? 'enabled' : 'disabled'}`);
  }, [persistState]);

  /**
   * Toggle AI chat visibility
   */
  const toggleAIChat = useCallback(() => {
    const newState = !isAIChatEnabled;
    console.log('🔄 [useAIChatToggle] Toggling AI chat:', isAIChatEnabled, '->', newState);
    setAIChatEnabled(newState);
  }, [isAIChatEnabled, setAIChatEnabled]);

  /**
   * Initialize state on mount and handle storage events
   */
  useEffect(() => {
    // Handle storage events from other tabs/windows
    const handleStorageChange = (event) => {
      if (event.key === STORAGE_KEY && event.newValue !== null) {
        try {
          const newState = JSON.parse(event.newValue);
          console.log('🔄 [useAIChatToggle] Storage event detected:', newState);
          setIsAIChatEnabledState(newState);
        } catch (error) {
          console.warn('🚨 [useAIChatToggle] Failed to parse storage event:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  /**
   * Debug logging for state changes
   */
  useEffect(() => {
    console.log('🔍 [useAIChatToggle] State changed:', {
      isAIChatEnabled,
      isTransitioning,
    });
  }, [isAIChatEnabled, isTransitioning]);

  return {
    isAIChatEnabled,
    toggleAIChat,
    setAIChatEnabled,
    isTransitioning,
  };
};

/**
 * Utility function to get current AI chat state from localStorage
 * Useful for components that need to check state without subscribing to changes
 *
 * @returns {boolean} Current AI chat enabled state
 */
export const getAIChatEnabledState = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored !== null) {
      return JSON.parse(stored);
    }
    return DEFAULT_AI_CHAT_ENABLED;
  } catch (error) {
    console.warn('🚨 [getAIChatEnabledState] Failed to read from localStorage:', error);
    return DEFAULT_AI_CHAT_ENABLED;
  }
};

/**
 * Utility function to directly set AI chat state in localStorage
 * Useful for external components that need to update state
 *
 * @param {boolean} enabled - Whether AI chat should be enabled
 */
export const setAIChatEnabledState = (enabled) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(enabled));
    console.log('✅ [setAIChatEnabledState] Updated localStorage:', enabled);
  } catch (error) {
    console.error('🚨 [setAIChatEnabledState] Failed to update localStorage:', error);
  }
};
