/**
 * Component - Refactored for Better Maintainability
 *
 * This is the main draggable component for the form builder.
 * It has been refactored into smaller, focused modules while preserving
 * all existing functionality and behavior.
 *
 * Features:
 * - Drag-and-drop functionality with visual feedback
 * - Inline text editing for component properties
 * - Container component support (tabs, cards, forms, etc.)
 * - Professional animations and interactions
 * - Accessibility compliance and keyboard support
 * - Performance optimizations with memoization
 *
 * Architecture:
 * - Modular component structure with separated concerns
 * - Custom hooks for state management and side effects
 * - Styled components for consistent theming
 * - Utility functions for common operations
 * - Comprehensive error handling and validation
 */

import React, { memo, useMemo } from 'react';
import {
  TAB_CONTAINER,
  CARD_CONTAINER,
  FORM_SECTION,
  ACCORDION_CONTAINER,
  STEPS_CONTAINER,
  GRID_CONTAINER,
  FLEX_CONTAINER,
} from '../../constants';
import {
  TabContainer,
  CardContainer,
  FormSection,
  AccordionContainer,
  StepsContainer,
} from './ContainerComponents';

// Import refactored modules
import ComponentWrapper from './Component/components/ComponentWrapper';
import ComponentContent from './Component/components/ComponentContent';

// Import custom hooks
import { useDragAndDrop } from './Component/hooks/useDragAndDrop';
import { useInlineEditing } from './Component/hooks/useInlineEditing';
import { useComponentActions } from './Component/hooks/useComponentActions';

// Import utilities
import {
  isContainerComponent,
  getComponentInfo,
  validateComponentData,
} from './Component/utils/componentUtils';

/**
 * Enhanced Component wrapper with advanced interactions
 *
 * Main component that integrates all the refactored modules to provide
 * a complete draggable form component with all original functionality.
 */
const Component = memo(
  ({ data, components, path, handleDrop, onUpdateComponent }) => {
    // Memoized component lookup - must be called before any early returns
    const component = useMemo(
      () => components?.[data?.id],
      [components, data?.id],
    );

    // Get component information
    const componentInfo = useMemo(
      () => getComponentInfo(component),
      [component],
    );

    // Check if this is a container component
    const isContainer = useMemo(() => {
      const result = isContainerComponent(data?.type);
      console.log(
        'Component.jsx - data.type:',
        data?.type,
        'isContainer:',
        result,
      );
      return result;
    }, [data?.type]);

    // Initialize custom hooks - must be called before any early returns
    const { dragRef, isDragging } = useDragAndDrop({
      data,
      path,
    });

    const { renderEditableText } = useInlineEditing({
      component,
      componentId: data?.id,
      onUpdateComponent,
    });

    const {
      isSelected,
      isHovered,
      handleEdit,
      handleDelete,
      handleCopy,
      handleSelect,
      handleMouseEnter,
      handleMouseLeave,
    } = useComponentActions({
      componentId: data?.id,
      componentData: component,
    });

    // Validate component data after hooks are initialized
    const validation = validateComponentData(data);
    if (!validation.isValid) {
      console.error('Component received invalid data:', validation.errors);
      return null;
    }

    // Handle container component updates
    const handleComponentUpdate = (componentId, updates) => {
      if (onUpdateComponent) {
        onUpdateComponent(componentId, updates);
      }
    };

    // Safety check for component lookup
    if (!component) {
      console.error(
        `Component with id "${data.id}" not found in components object. Available components:`,
        Object.keys(components),
      );
      return (
        <div
          style={{
            color: '#ef4444',
            fontSize: '12px',
            textAlign: 'center',
            padding: '16px',
          }}
        >
          ⚠️ Component not found: {data.id}
        </div>
      );
    }

    // Render container components
    if (isContainer) {
      console.log('Component.jsx - Rendering container component:', data.type);
      const containerProps = {
        data,
        components,
        handleDrop,
        path,
        onUpdateComponent: handleComponentUpdate,
      };

      switch (data.type) {
        case TAB_CONTAINER:
          return <TabContainer {...containerProps} />;
        case CARD_CONTAINER:
          return <CardContainer {...containerProps} />;
        case FORM_SECTION:
          return <FormSection {...containerProps} />;
        case ACCORDION_CONTAINER:
          return <AccordionContainer {...containerProps} />;
        case STEPS_CONTAINER:
          console.log(
            'Component.jsx - Rendering StepsContainer with props:',
            containerProps,
          );
          return <StepsContainer {...containerProps} />;
        case GRID_CONTAINER:
          return (
            <div
              style={{
                border: '1px dashed #d9d9d9',
                padding: '16px',
                borderRadius: '4px',
                background: '#fafafa',
              }}
            >
              Grid Container (Drop components here)
            </div>
          );
        case FLEX_CONTAINER:
          return (
            <div
              style={{
                border: '1px dashed #d9d9d9',
                padding: '16px',
                borderRadius: '4px',
                background: '#fafafa',
                display: 'flex',
                gap: '8px',
              }}
            >
              Flex Container (Drop components here)
            </div>
          );
        default:
          return null;
      }
    }

    // Render regular components using the new modular structure
    return (
      <ComponentWrapper
        componentInfo={componentInfo}
        isDragging={isDragging}
        isSelected={isSelected}
        isHovered={isHovered}
        isContainerComponent={isContainer}
        componentId={data?.id}
        onSelect={handleSelect}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onCopy={handleCopy}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        dragRef={dragRef}
        componentData={component}
        components={components}
      >
        <ComponentContent
          component={component}
          renderEditableText={renderEditableText}
        />
      </ComponentWrapper>
    );
  },
);

// Set display name for debugging
Component.displayName = 'Component';

export default Component;
