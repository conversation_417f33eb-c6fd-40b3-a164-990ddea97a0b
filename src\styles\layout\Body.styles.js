import styled from 'styled-components';
import { colors, elevation } from '../theme';

// Enhanced responsive layout container with modern design
export const Body = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(
    135deg,
    ${colors.backgroundTertiary} 0%,
    ${colors.background} 100%
  );
  font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto',
    sans-serif;
  overflow: hidden;
  position: relative;

  /* Modern backdrop effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 20% 80%,
        ${colors.primary}08 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        ${colors.aiPrimary}06 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: 0;
  }

  .ant-tabs {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
    position: relative;
    z-index: 1;
    border-radius: 0;
    box-shadow: ${elevation.depth1};
  }

  .ant-tabs-content-holder {
    flex: 1;
    overflow: hidden;
    background: transparent;
    height: calc(100vh - 56px);
    position: relative;
  }

  .ant-tabs-tabpane {
    height: 100%;
    overflow: hidden;
    position: relative;
  }

  .ant-tabs-tab {
    font-weight: 500;
    color: ${colors.textSecondary};
    font-size: 14px;
    padding: 16px 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px 12px 0 0;
    margin-right: 4px;
    position: relative;
    background: transparent;
    border: none;

    &:hover {
      color: ${colors.textPrimary};
      background: rgba(255, 255, 255, 0.8);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.ant-tabs-tab-active {
      color: ${colors.primary};
      background: ${colors.background};
      font-weight: 600;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);

      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(
          90deg,
          ${colors.primary} 0%,
          ${colors.aiPrimary} 100%
        );
        border-radius: 2px 2px 0 0;
      }
    }
  }

  .ant-tabs-nav {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    margin: 0;
    padding: 0 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
    z-index: 2;
  }

  .ant-tabs-ink-bar {
    display: none; /* Using custom indicator instead */
  }

  /* Responsive design for mobile devices */
  @media (max-width: 768px) {
    .ant-tabs-tab {
      padding: 12px 16px;
      font-size: 13px;
      margin-right: 2px;
    }

    .ant-tabs-nav {
      padding: 0 16px;
    }

    .ant-tabs-content-holder {
      height: calc(100vh - 48px);
    }
  }

  @media (max-width: 480px) {
    .ant-tabs-tab {
      padding: 10px 12px;
      font-size: 12px;

      span {
        display: flex;
        align-items: center;
        gap: 4px;

        .anticon {
          font-size: 14px;
        }
      }
    }

    .ant-tabs-nav {
      padding: 0 12px;
    }
  }
`;
