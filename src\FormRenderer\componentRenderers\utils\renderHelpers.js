/**
 * Render Helper Utilities
 * 
 * Helper functions for component rendering, error handling, and render optimization.
 * These functions provide common rendering patterns and error boundaries.
 */

import React from 'react';

/**
 * Creates a safe renderer that handles errors gracefully
 * 
 * @param {Function} renderFunction - The actual render function
 * @param {string} componentType - Type of component being rendered
 * @returns {Function} Safe render function with error handling
 */
export const createSafeRenderer = (renderFunction, componentType) => {
  return (component, ...args) => {
    try {
      if (!component) {
        return renderErrorComponent(`Component configuration missing for ${componentType}`);
      }
      
      return renderFunction(component, ...args);
    } catch (error) {
      console.error(`Error rendering ${componentType}:`, error);
      return renderErrorComponent(`Failed to render ${componentType}: ${error.message}`);
    }
  };
};

/**
 * Renders an error component for failed renders
 * 
 * @param {string} errorMessage - Error message to display
 * @returns {JSX.Element} Error component
 */
export const renderErrorComponent = (errorMessage) => {
  return (
    <div
      style={{
        padding: '8px 12px',
        color: '#ff4d4f',
        backgroundColor: '#fff2f0',
        border: '1px solid #ffccc7',
        borderRadius: '4px',
        fontSize: '12px',
      }}
    >
      <strong>Render Error:</strong> {errorMessage}
    </div>
  );
};

/**
 * Renders a placeholder component for unknown types
 * 
 * @param {string} componentType - Unknown component type
 * @returns {JSX.Element} Placeholder component
 */
export const renderPlaceholderComponent = (componentType) => {
  return (
    <div
      style={{
        padding: '16px',
        color: '#666',
        backgroundColor: '#fafafa',
        border: '1px dashed #d9d9d9',
        borderRadius: '4px',
        textAlign: 'center',
        fontSize: '14px',
      }}
    >
      <div style={{ marginBottom: '8px' }}>
        <strong>Unknown Component Type</strong>
      </div>
      <div style={{ fontSize: '12px', color: '#999' }}>
        Type: {componentType || 'undefined'}
      </div>
    </div>
  );
};

/**
 * Wraps a component with conditional Form.Item wrapper
 * 
 * @param {JSX.Element} component - Component to wrap
 * @param {Object} formItemProps - Form.Item props
 * @param {boolean} shouldWrap - Whether to wrap with Form.Item
 * @returns {JSX.Element} Wrapped or unwrapped component
 */
export const conditionalFormItemWrapper = (component, formItemProps, shouldWrap) => {
  if (!shouldWrap) {
    return component;
  }
  
  const { Form } = require('antd');
  return <Form.Item {...formItemProps}>{component}</Form.Item>;
};

/**
 * Creates a memoized renderer for performance optimization
 * 
 * @param {Function} renderFunction - Render function to memoize
 * @param {Array} dependencies - Dependencies for memoization
 * @returns {Function} Memoized render function
 */
export const createMemoizedRenderer = (renderFunction, dependencies = []) => {
  const { useMemo } = require('react');
  
  return (component, ...args) => {
    return useMemo(
      () => renderFunction(component, ...args),
      [component, ...args, ...dependencies]
    );
  };
};

/**
 * Applies conditional styling based on component state
 * 
 * @param {Object} baseStyle - Base style object
 * @param {Object} component - Component configuration
 * @param {Object} state - Component state
 * @returns {Object} Combined style object
 */
export const applyConditionalStyling = (baseStyle = {}, component = {}, state = {}) => {
  const style = { ...baseStyle };
  
  // Apply disabled styling
  if (component.styling?.disabled || state.disabled) {
    style.opacity = 0.6;
    style.cursor = 'not-allowed';
  }
  
  // Apply loading styling
  if (component.styling?.loading || state.loading) {
    style.position = 'relative';
  }
  
  // Apply error styling
  if (state.error || component.styling?.validateStatus === 'error') {
    style.borderColor = '#ff4d4f';
  }
  
  // Apply focus styling
  if (state.focused) {
    style.borderColor = '#1890ff';
    style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.2)';
  }
  
  // Merge with component-specific styles
  if (component.styling?.style) {
    Object.assign(style, component.styling.style);
  }
  
  return style;
};

/**
 * Validates props before rendering
 * 
 * @param {Object} props - Props to validate
 * @param {Array} requiredProps - Array of required prop names
 * @param {string} componentType - Type of component for error messages
 * @returns {Object} Validation result
 */
export const validateRenderProps = (props, requiredProps = [], componentType = 'Component') => {
  const errors = [];
  
  requiredProps.forEach(propName => {
    if (props[propName] === undefined || props[propName] === null) {
      errors.push(`Required prop '${propName}' is missing for ${componentType}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Creates a render context with common utilities
 * 
 * @param {Object} component - Component configuration
 * @param {Object} commonProps - Common props for all components
 * @param {Object} formItemProps - Form item props
 * @returns {Object} Render context object
 */
export const createRenderContext = (component, commonProps, formItemProps) => {
  return {
    component,
    commonProps,
    formItemProps,
    
    // Helper methods
    hasValidation: () => Boolean(component.validation),
    hasEvents: () => Boolean(component.events),
    hasCustomProps: () => Boolean(component.componentProps),
    
    // Style helpers
    getStyle: (additionalStyle = {}) => ({
      ...commonProps.style,
      ...additionalStyle,
    }),
    
    // Prop helpers
    mergeProps: (additionalProps = {}) => ({
      ...commonProps,
      ...additionalProps,
    }),
  };
};

/**
 * Handles async rendering for components that need to load data
 * 
 * @param {Function} asyncRenderFunction - Async render function
 * @param {Object} fallbackComponent - Component to show while loading
 * @returns {Function} Async renderer
 */
export const createAsyncRenderer = (asyncRenderFunction, fallbackComponent = null) => {
  const { useState, useEffect } = require('react');
  
  return (component, ...args) => {
    const [renderedComponent, setRenderedComponent] = useState(fallbackComponent);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    
    useEffect(() => {
      const renderAsync = async () => {
        try {
          setLoading(true);
          setError(null);
          const result = await asyncRenderFunction(component, ...args);
          setRenderedComponent(result);
        } catch (err) {
          setError(err);
          setRenderedComponent(renderErrorComponent(err.message));
        } finally {
          setLoading(false);
        }
      };
      
      renderAsync();
    }, [component, ...args]);
    
    if (loading && fallbackComponent) {
      return fallbackComponent;
    }
    
    return renderedComponent;
  };
};

/**
 * Creates a debug wrapper for development
 * 
 * @param {Function} renderFunction - Render function to debug
 * @param {string} componentType - Type of component
 * @returns {Function} Debug-wrapped render function
 */
export const createDebugRenderer = (renderFunction, componentType) => {
  return (component, ...args) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`Rendering ${componentType}`);
      console.log('Component config:', component);
      console.log('Render args:', args);
      
      const startTime = performance.now();
      const result = renderFunction(component, ...args);
      const endTime = performance.now();
      
      console.log(`Render time: ${endTime - startTime}ms`);
      console.groupEnd();
      
      return result;
    }
    
    return renderFunction(component, ...args);
  };
};
