/**
 * @fileoverview Quick Actions component
 *
 * This component provides quick access to common form builder actions
 * like preview, test, validate, and analytics.
 *
 * @module QuickActions
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useCallback } from 'react';
import { But<PERSON>, Tooltip, Space, Dropdown } from 'antd';
import {
  EyeOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  MobileOutlined,
  DesktopOutlined,
  TabletOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { ActionGroup } from '../styles/EnterpriseHeader.styles';

/**
 * Quick Actions component
 *
 * @param {Object} props - Component props
 * @param {string} props.activeTab - Currently active tab
 * @param {Function} props.onPreview - Preview action handler
 * @param {Function} props.onTest - Test action handler
 * @param {Function} props.onValidate - Validate action handler
 * @param {Function} props.onAnalytics - Analytics action handler
 * @param {Function} props.onDevicePreview - Device preview handler
 * @returns {React.ReactNode} Quick actions JSX
 */
const QuickActions = ({
  activeTab,
  onPreview,
  onTest,
  onValidate,
  onAnalytics,
  onDevicePreview,
}) => {
  /**
   * Handle preview action
   */
  const handlePreview = useCallback(() => {
    if (onPreview) {
      onPreview();
    }
  }, [onPreview]);

  /**
   * Handle test action
   */
  const handleTest = useCallback(() => {
    if (onTest) {
      onTest();
    }
  }, [onTest]);

  /**
   * Handle validate action
   */
  const handleValidate = useCallback(() => {
    if (onValidate) {
      onValidate();
    }
  }, [onValidate]);

  /**
   * Handle analytics action
   */
  const handleAnalytics = useCallback(() => {
    if (onAnalytics) {
      onAnalytics();
    }
  }, [onAnalytics]);

  /**
   * Device preview dropdown menu
   */
  const devicePreviewMenu = {
    items: [
      {
        key: 'desktop',
        label: 'Desktop Preview',
        icon: <DesktopOutlined />,
        onClick: () => onDevicePreview?.('desktop'),
      },
      {
        key: 'tablet',
        label: 'Tablet Preview',
        icon: <TabletOutlined />,
        onClick: () => onDevicePreview?.('tablet'),
      },
      {
        key: 'mobile',
        label: 'Mobile Preview',
        icon: <MobileOutlined />,
        onClick: () => onDevicePreview?.('mobile'),
      },
    ],
  };

  /**
   * Settings dropdown menu
   */
  const settingsMenu = {
    items: [
      {
        key: 'preferences',
        label: 'Preferences',
        onClick: () => console.log('Open preferences'),
      },
      {
        key: 'shortcuts',
        label: 'Keyboard Shortcuts',
        onClick: () => console.log('Show shortcuts'),
      },
      {
        type: 'divider',
      },
      {
        key: 'help',
        label: 'Help & Documentation',
        onClick: () => console.log('Open help'),
      },
    ],
  };

  return (
    <ActionGroup>
      <Space size="small">
        {/* Preview Action */}
        {activeTab === 'builder' && (
          <Tooltip title="Preview form in new tab">
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                type="default"
                size="small"
                icon={<EyeOutlined />}
                onClick={handlePreview}
              >
                Preview
              </Button>
            </motion.div>
          </Tooltip>
        )}

        {/* Test Action */}
        <Tooltip title="Test form functionality">
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              type="default"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={handleTest}
            >
              Test
            </Button>
          </motion.div>
        </Tooltip>

        {/* Validate Action */}
        <Tooltip title="Validate form structure and rules">
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              type="default"
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={handleValidate}
            >
              Validate
            </Button>
          </motion.div>
        </Tooltip>

        {/* Device Preview Dropdown */}
        <Dropdown menu={devicePreviewMenu} trigger={['click']}>
          <Tooltip title="Preview on different devices">
            <Button
              type="default"
              size="small"
              icon={<MobileOutlined />}
            />
          </Tooltip>
        </Dropdown>

        {/* Analytics Action */}
        <Tooltip title="View form analytics and insights">
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              type="default"
              size="small"
              icon={<BarChartOutlined />}
              onClick={handleAnalytics}
            >
              Analytics
            </Button>
          </motion.div>
        </Tooltip>

        {/* Settings Dropdown */}
        <Dropdown menu={settingsMenu} trigger={['click']}>
          <Tooltip title="Settings and preferences">
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
            />
          </Tooltip>
        </Dropdown>
      </Space>
    </ActionGroup>
  );
};

export default QuickActions;
