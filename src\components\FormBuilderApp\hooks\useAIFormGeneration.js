/**
 * @fileoverview Custom hook for AI form generation functionality
 *
 * This hook encapsulates all AI-related form generation logic, including
 * schema transformation, validation, enhancement, and state updates.
 *
 * @module useAIFormGeneration
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useCallback, startTransition } from 'react';
import { message } from 'antd';
import {
  validateAIGeneratedSchema,
  transformAISchema,
  enhanceAISchema,
} from '../../../utils/aiSchemaValidator';
import { animationTiming } from '../constants/animations';

/**
 * Custom hook for AI form generation functionality
 *
 * Provides comprehensive AI form generation capabilities including schema
 * transformation, validation, enhancement, and seamless integration with
 * the form builder state management system.
 *
 * @param {Object} params - Hook parameters
 * @param {Function} params.setLayout - Layout state setter function
 * @param {Function} params.setComponents - Components state setter function
 * @param {Function} params.setForceRenderKey - Force render key setter function
 * @param {Function} params.setActiveTab - Active tab setter function
 * @param {Object} params.aiGenerationAttemptRef - Ref for tracking AI attempts
 * @param {Array} params.layout - Current layout state (for logging)
 * @param {Object} params.components - Current components state (for logging)
 *
 * @returns {Object} AI form generation handlers
 * @returns {Function} returns.handleAIFormGenerated - Main AI form generation handler
 * @returns {Function} returns.handleSchemaUpdate - Schema update handler
 *
 * @example
 * ```jsx
 * const { handleAIFormGenerated, handleSchemaUpdate } = useAIFormGeneration({
 *   setLayout,
 *   setComponents,
 *   setForceRenderKey,
 *   setActiveTab,
 *   aiGenerationAttemptRef,
 *   layout,
 *   components
 * });
 *
 * // Handle AI-generated form
 * handleAIFormGenerated(generatedSchema);
 *
 * // Handle schema updates
 * handleSchemaUpdate(updatedSchema);
 * ```
 */
export const useAIFormGeneration = ({
  setLayout,
  setComponents,
  setForceRenderKey,
  setActiveTab,
  aiGenerationAttemptRef,
  layout,
  components,
}) => {
  /**
   * Handle AI-generated form integration
   *
   * Processes AI-generated schemas through transformation, validation,
   * enhancement, and state integration pipeline.
   *
   * @param {Object} generatedSchema - Raw AI-generated schema
   */
  const handleAIFormGenerated = useCallback(
    (generatedSchema) => {
      aiGenerationAttemptRef.current += 1;
      const attemptId = aiGenerationAttemptRef.current;
      console.log(
        `🔍 [handleAIFormGenerated] Attempt #${attemptId} - Received schema:`,
        generatedSchema,
      );

      try {
        // Transform the schema for compatibility
        console.log('🔍 [handleAIFormGenerated] Transforming schema...');
        const transformedSchema = transformAISchema(generatedSchema);
        console.log(
          '🔍 [handleAIFormGenerated] Transformed schema:',
          transformedSchema,
        );

        // Enhanced validation for AI-generated schemas
        console.log('🔍 [handleAIFormGenerated] Validating schema...');
        const aiValidation = validateAIGeneratedSchema(transformedSchema);
        console.log(
          '🔍 [handleAIFormGenerated] Validation result:',
          aiValidation,
        );

        // Handle validation failures
        if (!aiValidation.isValid) {
          message.error({
            content: (
              <div>
                <strong>❌ AI Schema Validation Failed:</strong>
                <ul style={{ marginTop: '8px', marginBottom: 0 }}>
                  {aiValidation.errors.slice(0, 3).map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
                {aiValidation.errors.length > 3 && (
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '4px',
                    }}
                  >
                    ...and {aiValidation.errors.length - 3} more issues
                  </div>
                )}
              </div>
            ),
            duration: 10,
          });
          return;
        }

        // Show warnings but proceed
        if (aiValidation.warnings.length > 0) {
          message.warning({
            content: (
              <div>
                <strong>⚠️ AI Schema Warnings:</strong>
                <ul style={{ marginTop: '8px', marginBottom: 0 }}>
                  {aiValidation.warnings.slice(0, 2).map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
                {aiValidation.warnings.length > 2 && (
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '4px',
                    }}
                  >
                    ...and {aiValidation.warnings.length - 2} more warnings
                  </div>
                )}
              </div>
            ),
            duration: 6,
          });
        }

        // Enhance the schema with missing properties
        console.log('🔍 [handleAIFormGenerated] Enhancing schema...');
        const enhancedSchema = enhanceAISchema(transformedSchema);
        console.log(
          '🔍 [handleAIFormGenerated] Enhanced schema:',
          enhancedSchema,
        );

        // Apply the enhanced schema to the form builder
        if (enhancedSchema.layout && enhancedSchema.components) {
          console.log('🔍 [handleAIFormGenerated] Applying to form builder...');
          console.log(
            '🔍 [handleAIFormGenerated] Current Layout Before Update:',
            layout,
          );
          console.log(
            '🔍 [handleAIFormGenerated] Current Components Before Update:',
            components,
          );
          console.log(
            '🔍 [handleAIFormGenerated] New Layout to Apply:',
            enhancedSchema.layout,
          );
          console.log(
            '🔍 [handleAIFormGenerated] New Components to Apply:',
            enhancedSchema.components,
          );

          // Enhanced state update with React 18 batching consideration
          console.log(
            `🔍 [handleAIFormGenerated] Attempt #${attemptId} - About to update state...`,
          );

          // Use startTransition for better performance and ensure updates are batched properly
          startTransition(() => {
            // Force a complete state reset to ensure re-rendering
            setLayout([]);
            setComponents({});

            // Use Framer Motion timing for smooth state transitions
            setTimeout(() => {
              console.log(
                `🔍 [handleAIFormGenerated] Attempt #${attemptId} - Setting new layout and components...`,
              );
              setLayout([...enhancedSchema.layout]);
              setComponents({ ...enhancedSchema.components });

              console.log(
                `🔍 [handleAIFormGenerated] Attempt #${attemptId} - State update completed`,
              );

              // Force re-render and tab switch to builder with Framer Motion timing
              setTimeout(() => {
                setForceRenderKey((prev) => prev + 1);
                setActiveTab('builder');
                console.log(
                  `🔍 [handleAIFormGenerated] Attempt #${attemptId} - Switched to builder tab and forced re-render`,
                );
              }, animationTiming.stateUpdate * 1000); // Convert to milliseconds
            }, animationTiming.stateReset * 1000); // Convert to milliseconds
          });

          // Log final structure for debugging
          console.log('🔍 [handleAIFormGenerated] Final layout structure:');
          enhancedSchema.layout.forEach((row, rowIndex) => {
            console.log(`  Row ${rowIndex}:`, row);
            if (row.children) {
              row.children.forEach((col, colIndex) => {
                console.log(`    Column ${colIndex}:`, col);
                if (col.children) {
                  console.log(`      Components:`, col.children);
                }
              });
            }
          });

          console.log(
            '✅ [handleAIFormGenerated] State update calls completed!',
          );

          // Show success message
          message.success({
            content: (
              <div>
                <strong>✅ AI-generated form applied successfully!</strong>
                <br />
                <small>
                  Quality Score: {aiValidation.score}/100 | Components:{' '}
                  {aiValidation.metadata?.componentCount || 0} | Depth:{' '}
                  {aiValidation.metadata?.layoutDepth || 0}
                </small>
              </div>
            ),
            duration: 5,
          });
        } else {
          console.error(
            '❌ [handleAIFormGenerated] Enhanced schema is missing required layout or components',
          );
          console.error(
            '❌ [handleAIFormGenerated] Enhanced schema structure:',
            {
              hasLayout: !!enhancedSchema.layout,
              hasComponents: !!enhancedSchema.components,
              layoutType: typeof enhancedSchema.layout,
              componentsType: typeof enhancedSchema.components,
              layoutLength: enhancedSchema.layout
                ? enhancedSchema.layout.length
                : 'N/A',
              componentsKeys: enhancedSchema.components
                ? Object.keys(enhancedSchema.components).length
                : 'N/A',
            },
          );
          message.error(
            'Enhanced schema is missing required layout or components',
          );
        }
      } catch (error) {
        console.error(
          '❌ [handleAIFormGenerated] Error applying AI-generated form:',
          error,
        );
        message.error('Failed to apply AI-generated form');
      }
    },
    [
      setLayout,
      setComponents,
      setForceRenderKey,
      setActiveTab,
      aiGenerationAttemptRef,
      layout,
      components,
    ],
  );

  /**
   * Handle schema updates from AI chat
   *
   * Processes direct schema updates from the AI chat interface.
   *
   * @param {Object} updatedSchema - Updated schema object
   */
  const handleSchemaUpdate = useCallback(
    (updatedSchema) => {
      try {
        if (updatedSchema.layout) {
          setLayout(updatedSchema.layout);
        }
        if (updatedSchema.components) {
          setComponents(updatedSchema.components);
        }
        message.success('Schema updated successfully!');
      } catch (error) {
        console.error('Error updating schema:', error);
        message.error('Failed to update schema');
      }
    },
    [setLayout, setComponents],
  );

  return {
    handleAIFormGenerated,
    handleSchemaUpdate,
  };
};
