/**
 * AIChatSection Module Index
 * 
 * Barrel export file for the AIChatSection module.
 * Provides clean imports for all components, hooks, and utilities.
 * 
 * Usage:
 * import AIChatSection, { useAIConnection, ChatMessage } from './AIChatSection';
 * 
 * Architecture Overview:
 * - Main component: AIChatSection (default export)
 * - Custom hooks: useAIConnection, useMessageHandling
 * - UI components: ChatHeader, ChatMessage, LoadingMessage, StatusBar
 * - Styled components: All styling components
 * - Utilities: Message utilities and helper functions
 * - Constants: Animation variants, UI text, configuration
 */

// Main component (default export)
export { default } from '../AIChatSection';

// Custom hooks
export { useAIConnection } from './hooks/useAIConnection';
export { useMessageHandling } from './hooks/useMessageHandling';

// UI Components
export { default as ChatHeader } from './components/ChatHeader';
export { default as ChatMessage } from './components/ChatMessage';
export { default as LoadingMessage } from './components/LoadingMessage';
export { default as StatusBar } from './components/StatusBar';

// Styled Components
export {
  AIChatContainer,
  ChatMessages,
  ChatInput,
  MessageBubble,
  MessageContent,
} from './styles/StyledComponents';

export {
  AIStatusBar,
  ConnectionIndicator,
  ProcessingIndicator,
  TypingIndicator,
  ActionButtons,
  ComplexityTag,
} from './styles/StatusComponents';

// Animation constants
export {
  messageVariants,
  containerVariants,
  headerVariants,
  processingVariants,
  loadingMessageVariants,
  messageContentVariants,
  easingCurves,
  animationDurations,
  springConfigs,
  injectGlobalAnimations,
} from './constants/animations';

// Utility functions
export {
  generateMessageId,
  createMessage,
  createUserMessage,
  createAIMessage,
  createFormGeneratedMessage,
  createErrorMessage,
  isValidMessage,
  filterMessagesByType,
  getLastMessageByType,
  sortMessagesByTimestamp,
  formatMessageTimestamp,
  truncateMessageText,
  extractMentions,
  countWords,
  estimateReadingTime,
} from './utils/messageUtils';

// Constants
export {
  WELCOME_MESSAGE,
  PROCESSING_STAGES,
  AI_STATUS,
  MESSAGE_TYPES,
  CONNECTION_STATUS,
  DEFAULT_CONFIG,
  UI_TEXT,
  ERROR_MESSAGES,
  ANIMATION_TIMING,
  FALLBACK_COLORS,
  KEYBOARD_SHORTCUTS,
  ACCESSIBILITY,
} from './constants';

/**
 * Module Information
 * 
 * This module represents a complete refactoring of the original 1077-line
 * AIChatSection.jsx component into a maintainable, modular architecture.
 * 
 * Benefits of this refactoring:
 * - Improved maintainability through separation of concerns
 * - Better testability with isolated components and hooks
 * - Enhanced reusability of individual components
 * - Cleaner code organization and documentation
 * - Easier debugging and development experience
 * - Consistent styling and animation patterns
 * - Type safety and better IDE support
 * 
 * File Structure:
 * ├── components/          # Reusable UI components
 * ├── hooks/              # Custom React hooks
 * ├── styles/             # Styled components
 * ├── utils/              # Utility functions
 * ├── constants/          # Configuration and constants
 * └── index.js           # Barrel exports (this file)
 */
