/**
 * @fileoverview Custom hook for tab management and UI state
 * 
 * This hook encapsulates all tab-related state management including
 * active tab tracking and modal visibility states.
 * 
 * @module useTabManagement
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useState } from 'react';

/**
 * Custom hook for tab management and UI state
 * 
 * Provides centralized management for tab navigation and modal states
 * in the form builder application.
 * 
 * @returns {Object} Tab management state and handlers
 * @returns {string} returns.activeTab - Currently active tab key
 * @returns {Function} returns.setActiveTab - Active tab setter
 * @returns {boolean} returns.jsonModalVisible - JSON modal visibility state
 * @returns {Function} returns.setJsonModalVisible - JSON modal visibility setter
 * @returns {boolean} returns.importModalVisible - Import modal visibility state
 * @returns {Function} returns.setImportModalVisible - Import modal visibility setter
 * @returns {string} returns.jsonInput - JSON input field value
 * @returns {Function} returns.setJsonInput - JSON input setter
 * 
 * @example
 * ```jsx
 * const {
 *   activeTab,
 *   setActiveTab,
 *   jsonModalVisible,
 *   setJsonModalVisible,
 *   importModalVisible,
 *   setImportModalVisible,
 *   jsonInput,
 *   setJsonInput
 * } = useTabManagement();
 * 
 * // Switch to preview tab
 * setActiveTab('preview');
 * 
 * // Show JSON modal
 * setJsonModalVisible(true);
 * ```
 */
export const useTabManagement = () => {
  // Tab navigation state
  const [activeTab, setActiveTab] = useState('builder');
  
  // Modal visibility states
  const [jsonModalVisible, setJsonModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  
  // JSON input state
  const [jsonInput, setJsonInput] = useState('');

  return {
    // Tab management
    activeTab,
    setActiveTab,
    
    // Modal states
    jsonModalVisible,
    setJsonModalVisible,
    importModalVisible,
    setImportModalVisible,
    
    // Input states
    jsonInput,
    setJsonInput,
  };
};
