/**
 * @fileoverview Error Boundary component for form rendering
 *
 * This component provides error boundary functionality specifically
 * designed for form rendering errors with user-friendly fallbacks.
 *
 * @module ErrorBoundary
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React from 'react';
import { Button } from 'antd';
import { motion } from 'framer-motion';
import { errorVariants } from '../constants/animations';

/**
 * Error Boundary component for form rendering
 *
 * Catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI instead of the
 * component tree that crashed.
 *
 * @class ErrorBoundary
 * @extends {React.Component}
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to wrap
 * @param {React.ReactNode} [props.fallback] - Custom fallback UI
 *
 * @example
 * ```jsx
 * <ErrorBoundary fallback={<CustomErrorUI />}>
 *   <FormRenderer />
 * </ErrorBoundary>
 * ```
 */
class ErrorBoundary extends React.Component {
  /**
   * Constructor for ErrorBoundary
   *
   * @param {Object} props - Component props
   */
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  /**
   * Static method to update state when an error occurs
   *
   * @param {Error} error - The error that was thrown
   * @returns {Object} New state object
   */
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  /**
   * Lifecycle method called when an error occurs
   *
   * Logs error details to console for debugging purposes.
   *
   * @param {Error} error - The error that was thrown
   * @param {Object} errorInfo - Information about the error
   */
  componentDidCatch(error, errorInfo) {
    console.error('FormRenderer Error:', error, errorInfo);
  }

  /**
   * Render method for ErrorBoundary
   *
   * @returns {React.ReactNode} Either the fallback UI or children
   */
  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <motion.div
            variants={errorVariants}
            initial='hidden'
            animate='visible'
            style={{
              padding: '60px 40px',
              textAlign: 'center',
              background: '#fafafa',
              borderRadius: '12px',
              border: '2px dashed #e0e0e0',
              margin: '20px',
            }}
          >
            <div
              style={{
                fontSize: '48px',
                marginBottom: '16px',
                color: '#ef4444',
              }}
            >
              ⚠️
            </div>
            <h3
              style={{
                color: '#212121',
                fontSize: '18px',
                fontWeight: 600,
                marginBottom: '12px',
              }}
            >
              Form Rendering Error
            </h3>
            <p
              style={{
                color: '#616161',
                fontSize: '14px',
                lineHeight: '1.5',
                marginBottom: '24px',
                maxWidth: '400px',
                margin: '0 auto 24px',
              }}
            >
              There was an issue rendering the form. This might be due to an
              invalid schema or missing components. Please check the console for
              details.
            </p>
            <Button
              type='primary'
              onClick={() => window.location.reload()}
              style={{
                borderRadius: '8px',
                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                border: 'none',
                boxShadow: '0 2px 8px rgba(239, 68, 68, 0.3)',
                fontWeight: 500,
              }}
            >
              🔄 Reload Page
            </Button>
          </motion.div>
        )
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
