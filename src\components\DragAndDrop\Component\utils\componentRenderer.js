/**
 * Component Renderer Utilities
 *
 * Comprehensive component rendering system for the form builder.
 * Handles rendering of all Ant Design components with proper styling,
 * inline editing, and drag preview functionality.
 *
 * Features:
 * - 50+ Ant Design component types
 * - Inline editing support for all text properties
 * - Consistent styling and sizing
 * - Category-based organization (Data Entry, Display, Feedback, etc.)
 * - Form.Item wrapper integration for form components
 * - Responsive design and accessibility compliance
 *
 * @module ComponentRenderer
 */

import React from 'react';
import {
  Input,
  Select,
  Radio,
  Checkbox,
  DatePicker,
  InputNumber,
  Switch,
  Rate,
  Slider,
  Upload,
  Button,
  AutoComplete,
  Cascader,
  ColorPicker,
  Mentions,
  TimePicker,
  TreeSelect,
  Avatar,
  Badge,
  Tag,
  Alert,
  Progress,
  Skeleton,
  Spin,
  Breadcrumb,
  Menu,
  Pagination,
  Steps,
  Divider,
  Space,
  // Statistic, // Available but not currently used
  // Form, // Available but not currently used
} from 'antd';
import { UploadOutlined, UserOutlined } from '@ant-design/icons';

const { TextArea } = Input;

/**
 * Common props for all components in drag preview mode
 *
 * Provides consistent styling and behavior for components
 * when displayed in the form builder canvas.
 */
const getCommonProps = () => ({
  disabled: true, // Disabled for drag preview
  size: 'small',
  style: { width: '100%' },
});

/**
 * Data Entry Component Renderers
 *
 * Components primarily used for user input and data collection.
 * These components typically need Form.Item wrappers for proper labeling.
 */

/**
 * Renders basic input components (text, email, password)
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered input component
 */
export const renderInputComponents = (component, renderEditableText) => {
  const commonProps = getCommonProps();

  switch (component.type) {
    case 'input':
      return (
        <Input
          {...commonProps}
          placeholder={component.placeholder || component.label}
        />
      );

    case 'email':
      return (
        <Input
          {...commonProps}
          type='email'
          placeholder={component.placeholder || component.label}
        />
      );

    case 'password':
      return (
        <Input.Password
          {...commonProps}
          placeholder={component.placeholder || component.label}
        />
      );

    case 'textarea':
      return (
        <TextArea
          {...commonProps}
          rows={2}
          placeholder={component.placeholder || component.label}
        />
      );

    default:
      return null;
  }
};

/**
 * Renders selection components (select, radio, checkbox)
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered selection component
 */
export const renderSelectionComponents = (component, renderEditableText) => {
  const commonProps = getCommonProps();

  switch (component.type) {
    case 'select':
      return (
        <Select
          {...commonProps}
          placeholder={component.placeholder || component.label}
        >
          {component.options?.map((option, index) => (
            <Select.Option key={option.value} value={option.value}>
              {renderEditableText
                ? renderEditableText(
                    option.label,
                    `options.${index}.label`,
                    `Option ${index + 1}`,
                  )
                : option.label || `Option ${index + 1}`}
            </Select.Option>
          ))}
        </Select>
      );

    case 'radio':
      return (
        <Radio.Group {...commonProps}>
          {component.options?.map((option, index) => (
            <Radio key={option.value} value={option.value}>
              {renderEditableText
                ? renderEditableText(
                    option.label,
                    `options.${index}.label`,
                    `Option ${index + 1}`,
                  )
                : option.label || `Option ${index + 1}`}
            </Radio>
          ))}
        </Radio.Group>
      );

    case 'checkbox':
      return (
        <Checkbox {...commonProps}>
          {renderEditableText
            ? renderEditableText(component.label, 'label', 'Checkbox Label')
            : component.label || 'Checkbox Label'}
        </Checkbox>
      );

    default:
      return null;
  }
};

/**
 * Renders numeric and date input components
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered numeric/date component
 */
export const renderNumericDateComponents = (component, renderEditableText) => {
  const commonProps = getCommonProps();

  switch (component.type) {
    case 'inputnumber':
    case 'number':
      return (
        <InputNumber
          {...commonProps}
          placeholder={component.placeholder || component.label}
        />
      );

    case 'datepicker':
    case 'date':
      return (
        <DatePicker
          {...commonProps}
          placeholder={component.placeholder || component.label}
        />
      );

    case 'rangepicker':
      return <DatePicker.RangePicker {...commonProps} />;

    case 'timepicker':
      return (
        <TimePicker
          {...commonProps}
          placeholder={component.placeholder || component.label}
        />
      );

    default:
      return null;
  }
};

/**
 * Renders interactive components (switch, rate, slider)
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered interactive component
 */
export const renderInteractiveComponents = (component, renderEditableText) => {
  const commonProps = getCommonProps();

  switch (component.type) {
    case 'switch':
      return <Switch {...commonProps} size='small' />;

    case 'rate':
      return <Rate {...commonProps} count={5} allowHalf disabled />;

    case 'slider':
      return <Slider {...commonProps} defaultValue={30} />;

    default:
      return null;
  }
};

/**
 * Renders upload and advanced input components
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered upload/advanced component
 */
export const renderUploadAdvancedComponents = (
  component,
  renderEditableText,
) => {
  const commonProps = getCommonProps();

  switch (component.type) {
    case 'upload':
      return (
        <Upload {...commonProps} disabled>
          <Button size='small' icon={<UploadOutlined />}>
            Upload
          </Button>
        </Upload>
      );

    case 'autocomplete':
      return (
        <AutoComplete
          {...commonProps}
          options={component.options || []}
          placeholder={component.placeholder || component.label}
        />
      );

    case 'cascader':
      return (
        <Cascader
          {...commonProps}
          options={component.options || []}
          placeholder={component.placeholder || component.label}
        />
      );

    case 'colorpicker':
      return <ColorPicker {...commonProps} size='small' />;

    case 'mentions':
      return (
        <Mentions
          {...commonProps}
          rows={2}
          placeholder={component.placeholder || component.label}
        />
      );

    case 'treeselect':
      return (
        <TreeSelect
          {...commonProps}
          treeData={component.treeData || []}
          placeholder={component.placeholder || component.label}
        />
      );

    case 'transfer':
      return (
        <div
          style={{
            ...commonProps.style,
            height: '100px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
          }}
        >
          Transfer Component
        </div>
      );

    default:
      return null;
  }
};

/**
 * Data Display Component Renderers
 *
 * Components primarily used for displaying information and data.
 * These components typically don't need Form.Item wrappers.
 */

/**
 * Renders avatar and badge components
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered avatar/badge component
 */
export const renderAvatarBadgeComponents = (component, renderEditableText) => {
  switch (component.type) {
    case 'avatar':
      return (
        <div
          style={{
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <Avatar size='small' icon={<UserOutlined />} />
          <span style={{ fontSize: '12px' }}>Avatar</span>
        </div>
      );

    case 'badge':
      return (
        <div
          style={{
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <Badge count={component.count || 5} size='small'>
            <div
              style={{
                width: '30px',
                height: '20px',
                background: '#f0f0f0',
                borderRadius: '4px',
              }}
            />
          </Badge>
          <span style={{ fontSize: '12px' }}>
            {renderEditableText
              ? renderEditableText(component.text, 'text', 'Badge Text')
              : component.text || 'Badge Text'}
          </span>
        </div>
      );

    case 'tag':
      return (
        <div
          style={{
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <Tag size='small' color='blue'>
            {renderEditableText
              ? renderEditableText(component.text, 'text', 'Tag Text')
              : component.text || 'Tag Text'}
          </Tag>
          <span style={{ fontSize: '12px' }}>Tag</span>
        </div>
      );

    default:
      return null;
  }
};

/**
 * Feedback Component Renderers
 *
 * Components used for user feedback, notifications, and status display.
 */

/**
 * Renders feedback components (alert, progress, etc.)
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered feedback component
 */
export const renderFeedbackComponents = (component, renderEditableText) => {
  switch (component.type) {
    case 'alert':
      return (
        <div style={{ padding: '4px' }}>
          <Alert
            message={
              renderEditableText
                ? renderEditableText(
                    component.message,
                    'message',
                    'Alert Message',
                  )
                : component.message || 'Alert Message'
            }
            type={component.styling?.type || 'info'}
            size='small'
            showIcon={component.styling?.showIcon !== false}
            description={
              renderEditableText
                ? renderEditableText(
                    component.description,
                    'description',
                    'Alert Description',
                  )
                : component.description || 'Alert Description'
            }
          />
        </div>
      );

    case 'progress':
      return (
        <div style={{ padding: '8px' }}>
          <Progress
            percent={component.percent || 30}
            size='small'
            type={component.styling?.type || 'line'}
            status={component.styling?.status || 'normal'}
            showInfo={component.styling?.showInfo !== false}
          />
          <div
            style={{ fontSize: '12px', marginTop: '4px', textAlign: 'center' }}
          >
            Progress
          </div>
        </div>
      );

    case 'statistic':
      return (
        <div style={{ padding: '8px', textAlign: 'center' }}>
          <div
            style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}
          >
            {renderEditableText
              ? renderEditableText(component.value, 'value', '112,893')
              : component.value || '112,893'}
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
            {renderEditableText
              ? renderEditableText(component.title, 'title', 'Statistic Title')
              : component.title || 'Statistic Title'}
          </div>
        </div>
      );

    case 'skeleton':
      return (
        <div style={{ padding: '8px' }}>
          <Skeleton active paragraph={{ rows: 1 }} title={false} />
        </div>
      );

    case 'spin':
      return (
        <div style={{ padding: '8px', textAlign: 'center' }}>
          <Spin size='small' />
          <div style={{ fontSize: '12px', marginTop: '8px' }}>Loading...</div>
        </div>
      );

    default:
      return null;
  }
};

/**
 * Navigation Component Renderers
 *
 * Components used for navigation and user guidance.
 */

/**
 * Renders navigation components (breadcrumb, menu, pagination, steps)
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered navigation component
 */
export const renderNavigationComponents = (component, renderEditableText) => {
  switch (component.type) {
    case 'breadcrumb':
      return (
        <div style={{ padding: '8px' }}>
          <Breadcrumb items={[{ title: 'Home' }, { title: 'Page' }]} />
        </div>
      );

    case 'menu':
      return (
        <div style={{ padding: '4px' }}>
          <Menu
            mode='horizontal'
            items={[{ key: '1', label: 'Menu Item' }]}
            style={{ border: 'none', fontSize: '12px' }}
          />
        </div>
      );

    case 'pagination':
      return (
        <div style={{ padding: '8px', textAlign: 'center' }}>
          <Pagination simple current={1} total={50} size='small' />
        </div>
      );

    case 'steps':
      return (
        <div style={{ padding: '8px' }}>
          <Steps
            size='small'
            current={1}
            items={[{ title: 'Step 1' }, { title: 'Step 2' }]}
          />
        </div>
      );

    default:
      return null;
  }
};

/**
 * Layout and General Component Renderers
 *
 * Components used for layout and general purpose elements.
 */

/**
 * Renders layout components (divider, space)
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered layout component
 */
export const renderLayoutComponents = (component, renderEditableText) => {
  switch (component.type) {
    case 'divider':
      return (
        <div style={{ padding: '8px' }}>
          <Divider style={{ margin: '8px 0' }}>
            {component.children || 'Divider'}
          </Divider>
        </div>
      );

    case 'space':
      return (
        <div style={{ padding: '8px' }}>
          <Space size='small'>
            <Button size='small'>Button 1</Button>
            <Button size='small'>Button 2</Button>
          </Space>
        </div>
      );

    default:
      return null;
  }
};

/**
 * Renders general components (button, typography)
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered general component
 */
export const renderGeneralComponents = (component, renderEditableText) => {
  switch (component.type) {
    case 'button':
      return (
        <div style={{ padding: '8px' }}>
          <Button size='small' type='primary'>
            {renderEditableText
              ? renderEditableText(component.text, 'text', 'Button Text')
              : component.text || 'Button Text'}
          </Button>
        </div>
      );

    case 'typography':
      return (
        <div style={{ padding: '8px' }}>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {renderEditableText
              ? renderEditableText(component.text, 'text', 'Typography Text')
              : component.text || 'Typography Text'}
          </div>
        </div>
      );

    default:
      return null;
  }
};

/**
 * Main Component Renderer
 *
 * Central function that routes component rendering to appropriate category renderers.
 * Handles all component types with fallback for unknown components.
 *
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered component
 */
export const renderFormComponent = (component, renderEditableText) => {
  if (!component) {
    return (
      <div
        style={{
          padding: '8px',
          color: '#999',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        No component data
      </div>
    );
  }

  // Try each category renderer
  const renderers = [
    renderInputComponents,
    renderSelectionComponents,
    renderNumericDateComponents,
    renderInteractiveComponents,
    renderUploadAdvancedComponents,
    renderAvatarBadgeComponents,
    renderFeedbackComponents,
    renderNavigationComponents,
    renderLayoutComponents,
    renderGeneralComponents,
  ];

  for (const renderer of renderers) {
    const result = renderer(component, renderEditableText);
    if (result) {
      return result;
    }
  }

  // Fallback for unknown component types
  return (
    <div
      style={{
        padding: '8px',
        color: '#666',
        fontSize: '12px',
        textAlign: 'center',
      }}
    >
      {component.label ||
        component.content ||
        component.type ||
        'Unknown Component'}
    </div>
  );
};
