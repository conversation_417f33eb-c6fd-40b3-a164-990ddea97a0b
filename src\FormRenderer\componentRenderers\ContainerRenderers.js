/**
 * Container Component Renderers
 *
 * Renders container components like Tab, Card, Form Section, Accordion, Steps containers.
 * These components can contain other components and provide complex layout structures.
 */

import React, { useState } from 'react';
import { Tabs, Card, Collapse, Steps } from 'antd';

// Interactive Steps Container component for standalone rendering
const InteractiveStepsComponent = ({ component }) => {
  const steps = component.steps || [];
  const stepsProps = component.stepsProps || {};

  // Use React state for interactive step navigation
  const [currentStep, setCurrentStep] = useState(stepsProps.current || 0);

  const stepItems = steps.map((step) => ({
    key: step.key,
    title: step.title,
    description: step.description,
  }));

  return (
    <div>
      <Steps
        current={currentStep}
        onChange={(step) => {
          console.log('ContainerRenderer: Step navigation clicked:', step);
          setCurrentStep(step);
        }}
        items={stepItems}
        direction={stepsProps.direction || 'horizontal'}
        size={stepsProps.size || 'default'}
        type='navigation'
        style={{
          marginBottom: '24px',
          cursor: 'pointer',
        }}
      />
      <div
        style={{
          padding: '16px',
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          background: '#fafafa',
          minHeight: '200px',
        }}
      >
        {steps[currentStep] ? (
          <div
            style={{
              color: '#333',
              textAlign: 'center',
              fontSize: '14px',
            }}
          >
            <div style={{ marginBottom: '8px' }}>📋</div>
            <div style={{ fontWeight: '500', marginBottom: '4px' }}>
              {steps[currentStep].title}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {steps[currentStep].description}
            </div>
            <div style={{ marginTop: '16px', color: '#999', fontSize: '12px' }}>
              Step content components would be rendered here
            </div>
          </div>
        ) : (
          <div
            style={{
              color: '#999',
              fontStyle: 'italic',
              textAlign: 'center',
              fontSize: '14px',
            }}
          >
            <div style={{ marginBottom: '8px' }}>📋</div>
            <div>No step content available</div>
          </div>
        )}
      </div>

      {/* Step navigation controls */}
      <div
        style={{
          marginTop: '16px',
          display: 'flex',
          justifyContent: 'space-between',
          borderTop: '1px solid #f0f0f0',
          paddingTop: '16px',
        }}
      >
        <button
          disabled={currentStep === 0}
          onClick={() => setCurrentStep(currentStep - 1)}
          style={{
            padding: '8px 16px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            background: currentStep === 0 ? '#f5f5f5' : '#fff',
            color: currentStep === 0 ? '#999' : '#333',
            cursor: currentStep === 0 ? 'not-allowed' : 'pointer',
          }}
        >
          Previous
        </button>
        <button
          disabled={currentStep === steps.length - 1}
          onClick={() => setCurrentStep(currentStep + 1)}
          style={{
            padding: '8px 16px',
            border: '1px solid #1890ff',
            borderRadius: '4px',
            background:
              currentStep === steps.length - 1 ? '#f5f5f5' : '#1890ff',
            color: currentStep === steps.length - 1 ? '#999' : '#fff',
            cursor:
              currentStep === steps.length - 1 ? 'not-allowed' : 'pointer',
          }}
        >
          Next
        </button>
      </div>
    </div>
  );
};

// Add display name for React DevTools
InteractiveStepsComponent.displayName = 'InteractiveStepsComponent';
// Note: Constants are available but not currently used in this file
// They may be used by the component registry system
// import {
//   TAB_CONTAINER,
//   CARD_CONTAINER,
//   FORM_SECTION,
//   ACCORDION_CONTAINER,
//   STEPS_CONTAINER,
//   GRID_CONTAINER,
//   FLEX_CONTAINER,
// } from '../../constants';

/**
 * Renders a Tab Container component
 *
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Tab Container component
 */
export const renderTabContainer = (component) => {
  const tabs = component.tabs || [];
  const tabItems = tabs.map((tab) => ({
    key: tab.key,
    label: tab.label,
    children: (
      <div style={{ padding: '16px' }}>
        <div style={{ color: '#666', fontStyle: 'italic' }}>
          Tab content would be rendered here in the actual form
        </div>
      </div>
    ),
  }));

  return (
    <Tabs
      items={tabItems}
      type={component.styling?.type || 'line'}
      size={component.styling?.size || 'default'}
      tabPosition={component.styling?.tabPosition || 'top'}
    />
  );
};

/**
 * Renders a Card Container component
 *
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Card Container component
 */
export const renderCardContainer = (component) => {
  const cardProps = component.cardProps || {};
  const cardStyling = component.styling || {};

  return (
    <Card
      title={cardProps.title}
      extra={cardProps.extra}
      hoverable={cardStyling.hoverable === true}
      size={cardStyling.size || 'default'}
      style={{
        border: cardStyling.bordered !== false ? undefined : 'none',
      }}
    >
      <div
        style={{
          color: '#666',
          fontStyle: 'italic',
          padding: '20px',
          textAlign: 'center',
        }}
      >
        Card content would be rendered here in the actual form
      </div>
    </Card>
  );
};

/**
 * Renders a Form Section component
 *
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Form Section component
 */
export const renderFormSection = (component) => {
  const sectionProps = component.sectionProps || {};
  const sectionStyling = component.styling || {};

  const sectionContent = (
    <div style={{ padding: '16px' }}>
      {sectionProps.description && (
        <div style={{ marginBottom: '16px', color: '#666' }}>
          {sectionProps.description}
        </div>
      )}
      <div
        style={{
          color: '#666',
          fontStyle: 'italic',
          textAlign: 'center',
        }}
      >
        Section content would be rendered here in the actual form
      </div>
    </div>
  );

  if (sectionStyling.collapsible) {
    return (
      <Collapse
        defaultActiveKey={sectionStyling.defaultCollapsed ? [] : ['1']}
        items={[
          {
            key: '1',
            label: sectionProps.title || 'Section',
            children: sectionContent,
          },
        ]}
      />
    );
  }

  return (
    <div
      style={{
        border:
          sectionStyling.bordered !== false ? '1px solid #d9d9d9' : 'none',
        borderRadius: '6px',
      }}
    >
      {sectionProps.title && (
        <div
          style={{
            padding: '12px 16px',
            borderBottom: '1px solid #d9d9d9',
            fontWeight: '500',
            background: '#fafafa',
          }}
        >
          {sectionProps.title}
        </div>
      )}
      {sectionContent}
    </div>
  );
};

/**
 * Renders an Accordion Container component
 *
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Accordion Container component
 */
export const renderAccordionContainer = (component) => {
  const accordionPanels = component.panels || [];
  const accordionProps = component.accordionProps || {};

  const accordionItems = accordionPanels.map((panel) => ({
    key: panel.key,
    label: panel.header,
    children: (
      <div style={{ padding: '16px' }}>
        <div style={{ color: '#666', fontStyle: 'italic' }}>
          Accordion panel content would be rendered here in the actual form
        </div>
      </div>
    ),
  }));

  return (
    <Collapse
      items={accordionItems}
      defaultActiveKey={accordionProps.defaultActiveKey}
      ghost={accordionProps.ghost}
      bordered={accordionProps.bordered !== false}
    />
  );
};

/**
 * Renders a Steps Container component with interactive navigation
 *
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Steps Container component
 */
export const renderStepsContainer = (component) => {
  return <InteractiveStepsComponent component={component} />;
};

/**
 * Renders a Grid Container component
 *
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Grid Container component
 */
export const renderGridContainer = (component) => {
  return (
    <div
      style={{
        padding: '16px',
        border: '1px dashed #d9d9d9',
        borderRadius: '4px',
        background: '#fafafa',
      }}
    >
      <div
        style={{
          color: '#666',
          fontStyle: 'italic',
          textAlign: 'center',
        }}
      >
        Grid container content would be rendered here in the actual form
      </div>
    </div>
  );
};

/**
 * Renders a Flex Container component
 *
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Flex Container component
 */
export const renderFlexContainer = (component) => {
  return (
    <div
      style={{
        padding: '16px',
        border: '1px dashed #d9d9d9',
        borderRadius: '4px',
        background: '#fafafa',
      }}
    >
      <div
        style={{
          color: '#666',
          fontStyle: 'italic',
          textAlign: 'center',
        }}
      >
        Flex container content would be rendered here in the actual form
      </div>
    </div>
  );
};
