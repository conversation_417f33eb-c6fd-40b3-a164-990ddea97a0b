import React, { memo, useCallback, useMemo, useState } from 'react';
import { Row, Col, Tabs, Card, Collapse, Steps } from 'antd';
import ComponentRenderer from './ComponentRenderer';
import {
  TAB_CONTAINER,
  CARD_CONTAINER,
  FORM_SECTION,
  ACCORDION_CONTAINER,
  STEPS_CONTAINER,
  GRID_CONTAINER,
  FLEX_CONTAINER,
} from '../constants';

// Interactive Steps Container component for preview mode
const InteractiveStepsContainer = memo(
  ({ containerData, components, renderContainerChildren }) => {
    // Use React state for interactive step navigation in preview
    const [currentStep, setCurrentStep] = useState(0);

    const component = components[containerData.id];

    // ALWAYS prioritize component registry for updated data (inline edits)
    const steps = component?.steps || containerData.steps || [];
    const stepsProps = component?.stepsProps || containerData.stepsProps || {};

    console.log('🔄 [InteractiveStepsContainer] Data sync check:', {
      containerId: containerData.id,
      componentSteps: component?.steps?.length || 0,
      containerDataSteps: containerData.steps?.length || 0,
      usingSteps: steps.length,
      componentStepsProps: component?.stepsProps,
      containerDataStepsProps: containerData.stepsProps,
      usingStepsProps: stepsProps,
    });

    // Update current step when stepsProps change
    React.useEffect(() => {
      setCurrentStep(stepsProps.current || 0);
    }, [stepsProps.current]);

    if (!component) return null;

    const stepItems = steps.map((step) => ({
      key: step.key,
      title: step.title,
      description: step.description,
    }));

    return (
      <div style={{ marginBottom: '16px' }}>
        <Steps
          current={currentStep}
          onChange={(step) => {
            console.log('Preview: Step navigation clicked:', step);
            setCurrentStep(step);
          }}
          items={stepItems}
          direction={stepsProps.direction || 'horizontal'}
          size={stepsProps.size || 'default'}
          type='navigation'
          style={{
            marginBottom: '24px',
            cursor: 'pointer',
          }}
        />
        <div
          style={{
            padding: '16px',
            border: '1px solid #f0f0f0',
            borderRadius: '6px',
            background: '#fafafa',
            minHeight: '200px',
          }}
        >
          {steps[currentStep] ? (
            renderContainerChildren(steps[currentStep].children || [])
          ) : (
            <div
              style={{
                textAlign: 'center',
                color: '#999',
                padding: '40px 20px',
                fontSize: '14px',
              }}
            >
              <div style={{ marginBottom: '8px' }}>📋</div>
              <div>Step {currentStep + 1} content</div>
              <div style={{ fontSize: '12px', marginTop: '4px' }}>
                {steps.length > 0
                  ? 'No components in this step'
                  : 'No steps defined'}
              </div>
            </div>
          )}
        </div>

        {/* Step navigation controls */}
        <div
          style={{
            marginTop: '16px',
            display: 'flex',
            justifyContent: 'space-between',
            borderTop: '1px solid #f0f0f0',
            paddingTop: '16px',
          }}
        >
          <button
            disabled={currentStep === 0}
            onClick={() => setCurrentStep(currentStep - 1)}
            style={{
              padding: '8px 16px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              background: currentStep === 0 ? '#f5f5f5' : '#fff',
              color: currentStep === 0 ? '#999' : '#333',
              cursor: currentStep === 0 ? 'not-allowed' : 'pointer',
            }}
          >
            Previous
          </button>
          <button
            disabled={currentStep === steps.length - 1}
            onClick={() => setCurrentStep(currentStep + 1)}
            style={{
              padding: '8px 16px',
              border: '1px solid #1890ff',
              borderRadius: '4px',
              background:
                currentStep === steps.length - 1 ? '#f5f5f5' : '#1890ff',
              color: currentStep === steps.length - 1 ? '#999' : '#fff',
              cursor:
                currentStep === steps.length - 1 ? 'not-allowed' : 'pointer',
            }}
          >
            Next
          </button>
        </div>
      </div>
    );
  },
);

// Add display name for React DevTools
InteractiveStepsContainer.displayName = 'InteractiveStepsContainer';

// Memoized LayoutRenderer for better performance
const LayoutRenderer = memo(
  ({ layout, components, formInstance }) => {
    // Memoized component renderer for better performance
    const renderComponent = useCallback(
      (componentData) => {
        if (!componentData || !componentData.id) {
          return null;
        }

        // Handle container components
        if (componentData.type === TAB_CONTAINER) {
          return renderTabContainer(componentData);
        }
        if (componentData.type === CARD_CONTAINER) {
          return renderCardContainer(componentData);
        }
        if (componentData.type === FORM_SECTION) {
          return renderFormSection(componentData);
        }
        if (componentData.type === ACCORDION_CONTAINER) {
          return renderAccordionContainer(componentData);
        }
        if (componentData.type === STEPS_CONTAINER) {
          return renderStepsContainer(componentData);
        }
        if (componentData.type === GRID_CONTAINER) {
          return renderGridContainer(componentData);
        }
        if (componentData.type === FLEX_CONTAINER) {
          return renderFlexContainer(componentData);
        }

        const component = components[componentData.id];
        if (!component) {
          return (
            <div style={{ color: 'red', padding: '8px' }}>
              Component not found: {componentData.id}
            </div>
          );
        }

        return (
          <ComponentRenderer
            key={componentData.id}
            component={component}
            formInstance={formInstance}
          />
        );
      },
      [components, formInstance],
    );

    // Smart container children renderer that detects column layouts
    const renderContainerChildren = useCallback(
      (children) => {
        if (!children || children.length === 0) {
          return null;
        }

        // Check if children should be rendered as columns
        // If there are multiple components and none are rows, render as columns
        const hasRows = children.some((child) => child.type === 'row');
        const shouldRenderAsColumns = children.length > 1 && !hasRows;

        if (shouldRenderAsColumns) {
          // Render as flex columns (side by side)
          return (
            <div
              style={{
                display: 'flex',
                gap: '16px',
                width: '100%',
                flexWrap: 'wrap', // Allow wrapping on smaller screens
              }}
            >
              {children.map((child, index) => (
                <div
                  key={child.id || index}
                  style={{
                    flex: 1,
                    minWidth: '200px', // Minimum width for responsiveness
                    maxWidth: `${100 / children.length}%`, // Equal width distribution
                  }}
                >
                  {renderComponent(child)}
                </div>
              ))}
            </div>
          );
        } else {
          // Render as vertical stack (default behavior)
          return children.map(renderComponent);
        }
      },
      [renderComponent],
    );

    // Render Tab Container
    const renderTabContainer = useCallback(
      (containerData) => {
        const component = components[containerData.id];
        if (!component) return null;

        // ALWAYS prioritize component registry for updated data (inline edits)
        const tabs = component.tabs || containerData.tabs || [];

        console.log('🔄 [TabContainer] Data sync check:', {
          containerId: containerData.id,
          componentTabs: component.tabs?.length || 0,
          containerDataTabs: containerData.tabs?.length || 0,
          usingTabs: tabs.length,
          componentStyling: component.styling,
          containerDataStyling: containerData.styling,
        });

        const tabItems = tabs.map((tab) => ({
          key: tab.key,
          label: tab.label,
          children: (
            <div style={{ padding: '16px' }}>
              {renderContainerChildren(tab.children || [])}
            </div>
          ),
        }));

        return (
          <Tabs
            key={containerData.id}
            items={tabItems}
            type={component.styling?.type || 'line'}
            size={component.styling?.size || 'default'}
            tabPosition={component.styling?.tabPosition || 'top'}
            style={{ marginBottom: '16px' }}
          />
        );
      },
      [components, renderContainerChildren],
    );

    // Render Card Container
    const renderCardContainer = useCallback(
      (containerData) => {
        const component = components[containerData.id];
        if (!component) return null;

        // ALWAYS prioritize component registry for updated data (inline edits)
        const cardProps = component.cardProps || containerData.cardProps || {};
        const styling = component.styling || containerData.styling || {};

        console.log('🔄 [CardContainer] Data sync check:', {
          containerId: containerData.id,
          componentCardProps: component.cardProps,
          containerDataCardProps: containerData.cardProps,
          usingCardProps: cardProps,
          componentStyling: component.styling,
          containerDataStyling: containerData.styling,
          usingStyling: styling,
        });

        return (
          <Card
            key={containerData.id}
            title={cardProps.title}
            extra={cardProps.extra}
            {...(styling.bordered !== false && { bordered: true })}
            hoverable={styling.hoverable === true}
            size={styling.size || 'default'}
            style={{ marginBottom: '16px' }}
          >
            {renderContainerChildren(containerData.children || [])}
          </Card>
        );
      },
      [components, renderContainerChildren],
    );

    // Render Accordion Container
    const renderAccordionContainer = useCallback(
      (containerData) => {
        const component = components[containerData.id];
        if (!component) return null;

        // ALWAYS prioritize component registry for updated data (inline edits)
        const panels = component.panels || containerData.panels || [];
        const accordionProps =
          component.accordionProps || containerData.accordionProps || {};

        console.log('🔄 [AccordionContainer] Data sync check:', {
          containerId: containerData.id,
          componentPanels: component.panels?.length || 0,
          containerDataPanels: containerData.panels?.length || 0,
          usingPanels: panels.length,
          componentAccordionProps: component.accordionProps,
          containerDataAccordionProps: containerData.accordionProps,
          usingAccordionProps: accordionProps,
        });

        const panelItems = panels.map((panel) => ({
          key: panel.key,
          label: panel.header,
          children: renderContainerChildren(panel.children || []),
        }));

        return (
          <Collapse
            key={containerData.id}
            items={panelItems}
            defaultActiveKey={accordionProps.defaultActiveKey}
            ghost={accordionProps.ghost}
            bordered={accordionProps.bordered !== false}
            style={{ marginBottom: '16px' }}
          />
        );
      },
      [components, renderContainerChildren],
    );

    // Render Steps Container with interactive navigation
    const renderStepsContainer = useCallback(
      (containerData) => {
        return (
          <InteractiveStepsContainer
            key={containerData.id}
            containerData={containerData}
            components={components}
            renderContainerChildren={renderContainerChildren}
          />
        );
      },
      [components, renderContainerChildren],
    );

    // Render Grid Container
    const renderGridContainer = useCallback(
      (containerData) => {
        const component = components[containerData.id];
        if (!component) return null;

        const children = containerData.children || [];
        const gridProps = component.gridProps || containerData.gridProps || {};
        const styling = component.styling || containerData.styling || {};

        return (
          <Row
            key={containerData.id}
            gutter={styling.gutter || [16, 16]}
            align={styling.align || 'top'}
            justify={styling.justify || 'start'}
            style={{ marginBottom: '16px' }}
          >
            {children.map((child) => (
              <Col
                key={child.id}
                span={Math.floor(24 / (gridProps.columns || 3))}
                xs={24}
                sm={12}
                md={Math.floor(24 / (gridProps.columns || 3))}
              >
                {renderComponent(child)}
              </Col>
            ))}
          </Row>
        );
      },
      [components, renderComponent],
    );

    // Render Flex Container
    const renderFlexContainer = useCallback(
      (containerData) => {
        const component = components[containerData.id];
        if (!component) return null;

        const children = containerData.children || [];
        const styling = component.styling || containerData.styling || {};

        return (
          <div
            key={containerData.id}
            style={{
              display: 'flex',
              flexDirection: styling.direction || 'row',
              flexWrap: styling.wrap || 'wrap',
              justifyContent: styling.justify || 'flex-start',
              alignItems: styling.align || 'flex-start',
              gap: styling.gap || '16px',
              marginBottom: '16px',
            }}
          >
            {children.map((child) => (
              <div
                key={child.id}
                style={{ flex: styling.equalHeight ? '1' : 'none' }}
              >
                {renderComponent(child)}
              </div>
            ))}
          </div>
        );
      },
      [components, renderComponent],
    );

    // Render Form Section
    const renderFormSection = useCallback(
      (containerData) => {
        const component = components[containerData.id];
        if (!component) return null;

        // ALWAYS prioritize component registry for updated data (inline edits)
        const sectionProps =
          component.sectionProps || containerData.sectionProps || {};
        const styling = component.styling || containerData.styling || {};

        console.log('🔄 [FormSection] Data sync check:', {
          containerId: containerData.id,
          componentSectionProps: component.sectionProps,
          containerDataSectionProps: containerData.sectionProps,
          usingSectionProps: sectionProps,
          componentStyling: component.styling,
          containerDataStyling: containerData.styling,
          usingStyling: styling,
        });

        const content = (
          <div style={{ padding: '16px' }}>
            {sectionProps.description && (
              <div style={{ marginBottom: '16px', color: '#666' }}>
                {sectionProps.description}
              </div>
            )}
            {renderContainerChildren(containerData.children || [])}
          </div>
        );

        if (styling.collapsible) {
          return (
            <Collapse
              key={containerData.id}
              defaultActiveKey={styling.defaultCollapsed ? [] : ['1']}
              bordered={styling.bordered !== false}
              style={{ marginBottom: '16px' }}
              items={[
                {
                  key: '1',
                  label: sectionProps.title || 'Section',
                  children: content,
                },
              ]}
            />
          );
        }

        return (
          <div
            key={containerData.id}
            style={{
              border: styling.bordered !== false ? '1px solid #d9d9d9' : 'none',
              borderRadius: '6px',
              marginBottom: '16px',
            }}
          >
            {sectionProps.title && (
              <div
                style={{
                  padding: '12px 16px',
                  borderBottom: '1px solid #d9d9d9',
                  fontWeight: '500',
                  background: '#fafafa',
                }}
              >
                {sectionProps.title}
              </div>
            )}
            {content}
          </div>
        );
      },
      [components, renderContainerChildren],
    );

    // Memoized column renderer for better performance
    const renderColumn = useCallback(
      (columnData) => {
        if (!columnData || !columnData.id) {
          return null;
        }

        const children = columnData.children || [];

        // Column props calculation without useMemo inside callback
        const { key, ...colProps } = {
          key: columnData.id,
          span:
            columnData.layout?.span ||
            Math.floor(24 / (columnData.parent?.children?.length || 1)),
          offset: columnData.layout?.offset || 0,
          xs: columnData.layout?.xs,
          sm: columnData.layout?.sm,
          md: columnData.layout?.md,
          lg: columnData.layout?.lg,
          xl: columnData.layout?.xl,
          xxl: columnData.layout?.xxl,
          flex: columnData.layout?.flex,
          order: columnData.layout?.order,
          pull: columnData.layout?.pull,
          push: columnData.layout?.push,
          style: {
            padding: '8px',
            minHeight: children.length === 0 ? '60px' : 'auto',
            ...columnData.styling?.style,
          },
        };

        return (
          <Col key={key} {...colProps}>
            {children.map(renderComponent)}
          </Col>
        );
      },
      [renderComponent],
    );

    // Memoized row renderer for better performance
    const renderRow = useCallback(
      (rowData) => {
        if (!rowData || !rowData.id) {
          return null;
        }

        const children = rowData.children || [];

        // Row props calculation without useMemo inside callback
        const { key, ...rowProps } = {
          key: rowData.id,
          gutter: rowData.layout?.gutter || [16, 16],
          align: rowData.layout?.align || 'top',
          justify: rowData.layout?.justify || 'start',
          wrap: rowData.layout?.wrap !== false,
          style: {
            marginBottom: '16px',
            ...rowData.styling?.style,
          },
        };

        // Children with parent reference for span calculation
        const childrenWithParent = children.map((child) => ({
          ...child,
          parent: rowData,
        }));

        return (
          <Row key={key} {...rowProps}>
            {childrenWithParent.map(renderColumn)}
          </Row>
        );
      },
      [renderColumn],
    );

    // Memoized container style
    const containerStyle = useMemo(() => ({ width: '100%' }), []);

    // Early return after all hooks
    if (!layout || !Array.isArray(layout)) {
      return <div>No layout data available</div>;
    }

    return <div style={containerStyle}>{layout.map(renderRow)}</div>;
  },
  (prevProps, nextProps) => {
    // Custom comparison to ensure re-render when layout or components change - avoid JSON.stringify
    if (prevProps.formInstance !== nextProps.formInstance) {
      return false;
    }

    // Compare layout array length and structure (shallow comparison)
    const prevLayout = prevProps.layout || [];
    const nextLayout = nextProps.layout || [];

    if (prevLayout.length !== nextLayout.length) {
      return false;
    }

    // Compare layout items by ID and type only
    for (let i = 0; i < prevLayout.length; i++) {
      if (
        prevLayout[i]?.id !== nextLayout[i]?.id ||
        prevLayout[i]?.type !== nextLayout[i]?.type ||
        prevLayout[i]?.children?.length !== nextLayout[i]?.children?.length
      ) {
        return false;
      }
    }

    // Compare components object keys (shallow comparison)
    const prevComponentKeys = Object.keys(prevProps.components || {});
    const nextComponentKeys = Object.keys(nextProps.components || {});

    if (prevComponentKeys.length !== nextComponentKeys.length) {
      return false;
    }

    // Check if component keys are the same
    for (const key of prevComponentKeys) {
      if (
        !nextProps.components[key] ||
        prevProps.components[key] !== nextProps.components[key]
      ) {
        return false;
      }
    }

    return true;
  },
);

// Set display name for debugging
LayoutRenderer.displayName = 'LayoutRenderer';

export default LayoutRenderer;
