/**
 * Form Default Configurations
 * 
 * Default values and configurations for FormRenderer components.
 * Provides consistent defaults across the form rendering system.
 */

/**
 * Default form props configuration
 * 
 * Base configuration object for Ant Design Form component.
 */
export const DEFAULT_FORM_PROPS = {
  layout: 'vertical',
  autoComplete: 'off',
  validateTrigger: 'onChange',
  scrollToFirstError: true,
  preserve: false,
};

/**
 * Default form renderer props
 * 
 * Default props for the main FormRenderer component.
 */
export const DEFAULT_FORM_RENDERER_PROPS = {
  initialValues: {},
  formProps: {},
  showSubmitButton: true,
  submitButtonText: 'Submit',
  showResetButton: true,
  resetButtonText: 'Reset',
  buttonProps: {},
};

/**
 * Default button configurations
 * 
 * Default props for form action buttons.
 */
export const DEFAULT_BUTTON_PROPS = {
  submit: {
    type: 'primary',
    htmlType: 'submit',
    size: 'middle',
  },
  reset: {
    type: 'default',
    size: 'middle',
  },
};

/**
 * Default form item styling
 * 
 * Default styling configuration for form items.
 */
export const DEFAULT_FORM_ITEM_STYLE = {
  marginTop: '24px',
  marginBottom: 0,
};

/**
 * Default form layouts
 * 
 * Predefined form layout configurations.
 */
export const FORM_LAYOUTS = {
  VERTICAL: 'vertical',
  HORIZONTAL: 'horizontal',
  INLINE: 'inline',
};

/**
 * Default form sizes
 * 
 * Available form size options.
 */
export const FORM_SIZES = {
  SMALL: 'small',
  MIDDLE: 'middle',
  LARGE: 'large',
};

/**
 * Default validation trigger options
 * 
 * Available validation trigger configurations.
 */
export const VALIDATION_TRIGGERS = {
  CHANGE: 'onChange',
  BLUR: 'onBlur',
  SUBMIT: 'onSubmit',
};

/**
 * Default form spacing configuration
 * 
 * Consistent spacing values for form elements.
 */
export const FORM_SPACING = {
  ITEM_MARGIN_BOTTOM: '16px',
  BUTTON_MARGIN_TOP: '24px',
  SECTION_MARGIN_BOTTOM: '32px',
  FIELD_GAP: '16px',
};

/**
 * Default responsive breakpoints for forms
 * 
 * Breakpoint values for responsive form behavior.
 */
export const FORM_BREAKPOINTS = {
  XS: 480,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1600,
};

/**
 * Default form performance settings
 * 
 * Performance-related default configurations.
 */
export const PERFORMANCE_DEFAULTS = {
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 100,
  MAX_RENDER_DEPTH: 10,
  MEMO_COMPARISON_DEPTH: 3,
};
