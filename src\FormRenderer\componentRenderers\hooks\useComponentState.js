import { useState, useCallback, useMemo } from 'react';

/**
 * Custom hook for managing component-specific state and lifecycle
 * 
 * Manages component state, event handlers, and lifecycle events
 * for individual form components.
 * 
 * @param {Object} component - Component configuration object
 * @param {*} value - Current component value
 * @param {Function} onChange - Change handler function
 * @param {Object} formInstance - Ant Design form instance
 * @returns {Object} Component state and handlers
 */
export const useComponentState = (component, value, onChange, formInstance) => {
  // Local component state
  const [localState, setLocalState] = useState({
    loading: false,
    error: null,
    touched: false,
    focused: false,
  });

  /**
   * Handles component value changes
   * 
   * @param {*} newValue - New component value
   * @param {Object} event - Event object (optional)
   */
  const handleChange = useCallback((newValue, event) => {
    // Mark as touched
    setLocalState(prev => ({ ...prev, touched: true }));
    
    // Call external onChange handler
    if (onChange) {
      onChange(newValue, event);
    }
    
    // Trigger form validation if needed
    if (formInstance && component?.validation?.validateOnChange) {
      formInstance.validateFields([component.name || component.id]);
    }
  }, [onChange, formInstance, component?.validation?.validateOnChange, component?.name, component?.id]);

  /**
   * Handles component focus events
   */
  const handleFocus = useCallback((event) => {
    setLocalState(prev => ({ ...prev, focused: true }));
    
    // Call component-specific focus handler
    if (component?.events?.onFocus) {
      component.events.onFocus(event);
    }
  }, [component?.events?.onFocus]);

  /**
   * Handles component blur events
   */
  const handleBlur = useCallback((event) => {
    setLocalState(prev => ({ ...prev, focused: false, touched: true }));
    
    // Trigger validation on blur if configured
    if (formInstance && component?.validation?.validateOnBlur) {
      formInstance.validateFields([component.name || component.id]);
    }
    
    // Call component-specific blur handler
    if (component?.events?.onBlur) {
      component.events.onBlur(event);
    }
  }, [formInstance, component?.validation?.validateOnBlur, component?.name, component?.id, component?.events?.onBlur]);

  /**
   * Sets loading state for the component
   */
  const setLoading = useCallback((loading) => {
    setLocalState(prev => ({ ...prev, loading }));
  }, []);

  /**
   * Sets error state for the component
   */
  const setError = useCallback((error) => {
    setLocalState(prev => ({ ...prev, error }));
  }, []);

  /**
   * Resets component state
   */
  const resetState = useCallback(() => {
    setLocalState({
      loading: false,
      error: null,
      touched: false,
      focused: false,
    });
  }, []);

  /**
   * Memoized component status
   * 
   * Determines the current status of the component based on
   * validation state, errors, and interaction state.
   */
  const componentStatus = useMemo(() => {
    if (localState.error) return 'error';
    if (localState.loading) return 'loading';
    if (component?.styling?.validateStatus) return component.styling.validateStatus;
    return 'normal';
  }, [localState.error, localState.loading, component?.styling?.validateStatus]);

  /**
   * Memoized component props with state
   * 
   * Combines component configuration with current state.
   */
  const componentWithState = useMemo(() => ({
    ...component,
    value,
    status: componentStatus,
    loading: localState.loading,
    error: localState.error,
    touched: localState.touched,
    focused: localState.focused,
  }), [component, value, componentStatus, localState]);

  return {
    // State
    localState,
    componentStatus,
    componentWithState,
    
    // Handlers
    handleChange,
    handleFocus,
    handleBlur,
    setLoading,
    setError,
    resetState,
  };
};
