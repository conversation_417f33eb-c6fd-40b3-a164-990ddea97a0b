/**
 * <PERSON><PERSON>er Console Test Runner for AI Form Generation
 * Use this in the browser console to test AI functionality
 */

import { generateFormSchema, testAIConnection } from '../services/aiService';
import { validateAIGeneratedSchema } from '../utils/aiSchemaValidator';
import { detectComplexity, suggestComponents } from '../services/promptTemplates';

// Make test functions available globally for browser console
window.AIFormTests = {
  
  /**
   * Quick AI connection test
   */
  async testConnection() {
    console.log('🔌 Testing AI Connection...');
    try {
      const result = await testAIConnection();
      if (result.success) {
        console.log('✅ AI Connected successfully!');
        console.log('Model:', result.model);
        return true;
      } else {
        console.error('❌ AI Connection failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Connection test error:', error);
      return false;
    }
  },

  /**
   * Test simple form generation
   */
  async testSimpleForm() {
    console.log('📝 Testing Simple Form Generation...');
    const prompt = "Create a contact form with name, email, phone, and message";
    
    try {
      const result = await generateFormSchema(prompt);
      if (result.success) {
        console.log('✅ Simple form generated successfully!');
        console.log('Components:', Object.keys(result.schema.components).length);
        console.log('Schema:', result.schema);
        return result.schema;
      } else {
        console.error('❌ Simple form generation failed:', result.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Simple form test error:', error);
      return null;
    }
  },

  /**
   * Test complex multi-step form generation
   */
  async testComplexForm() {
    console.log('🏗️ Testing Complex Form Generation...');
    const prompt = "Create a 5-step supplier onboarding form with company information, contact details, financial information, compliance certifications, and final review";
    
    try {
      const result = await generateFormSchema(prompt);
      if (result.success) {
        console.log('✅ Complex form generated successfully!');
        console.log('Components:', Object.keys(result.schema.components).length);
        console.log('Layout depth:', this.calculateLayoutDepth(result.schema.layout));
        console.log('Schema:', result.schema);
        
        // Validate the schema
        const validation = validateAIGeneratedSchema(result.schema);
        console.log('Validation score:', validation.score + '/100');
        
        return result.schema;
      } else {
        console.error('❌ Complex form generation failed:', result.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Complex form test error:', error);
      return null;
    }
  },

  /**
   * Test complexity detection
   */
  testComplexityDetection() {
    console.log('🧠 Testing Complexity Detection...');
    
    const testCases = [
      { prompt: "Create a simple contact form", expected: "SIMPLE" },
      { prompt: "Create a user registration form with multiple sections", expected: "INTERMEDIATE" },
      { prompt: "Create a complex multi-step wizard form", expected: "ENTERPRISE" }
    ];
    
    testCases.forEach(testCase => {
      const detected = detectComplexity(testCase.prompt);
      const status = detected === testCase.expected ? '✅' : '❌';
      console.log(`${status} "${testCase.prompt}" → ${detected} (expected: ${testCase.expected})`);
    });
  },

  /**
   * Test component suggestions
   */
  testComponentSuggestions() {
    console.log('🎯 Testing Component Suggestions...');
    
    const testCases = [
      "Create a form with name, email, and phone fields",
      "Create a survey with multiple choice questions",
      "Create a file upload form for documents",
      "Create a form with date picker and time selection"
    ];
    
    testCases.forEach(prompt => {
      const suggestions = suggestComponents(prompt);
      console.log(`📝 "${prompt}"`);
      console.log(`   Suggested: [${suggestions.join(', ')}]`);
    });
  },

  /**
   * Generate and apply a form to the current builder
   */
  async generateAndApply(prompt) {
    console.log('🚀 Generating and applying form...');
    console.log('Prompt:', prompt);
    
    try {
      const result = await generateFormSchema(prompt);
      if (result.success) {
        console.log('✅ Form generated successfully!');
        
        // Try to apply to the current form builder if available
        if (window.formBuilderInstance && window.formBuilderInstance.applySchema) {
          window.formBuilderInstance.applySchema(result.schema);
          console.log('✅ Form applied to builder!');
        } else {
          console.log('📋 Form schema ready (no builder instance found):');
          console.log(JSON.stringify(result.schema, null, 2));
        }
        
        return result.schema;
      } else {
        console.error('❌ Form generation failed:', result.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Generate and apply error:', error);
      return null;
    }
  },

  /**
   * Run a comprehensive test suite
   */
  async runFullTest() {
    console.log('🧪 Running Full AI Test Suite...');
    console.log('This may take a few minutes...\n');
    
    const results = {
      connection: false,
      simple: false,
      complex: false,
      validation: false
    };
    
    // Test connection
    results.connection = await this.testConnection();
    if (!results.connection) {
      console.log('❌ Stopping tests due to connection failure');
      return results;
    }
    
    // Test simple form
    console.log('\n' + '='.repeat(50));
    const simpleSchema = await this.testSimpleForm();
    results.simple = simpleSchema !== null;
    
    // Test complex form
    console.log('\n' + '='.repeat(50));
    const complexSchema = await this.testComplexForm();
    results.complex = complexSchema !== null;
    
    // Test validation
    if (simpleSchema) {
      console.log('\n' + '='.repeat(50));
      console.log('🔍 Testing Schema Validation...');
      const validation = validateAIGeneratedSchema(simpleSchema);
      results.validation = validation.isValid;
      console.log(`Validation result: ${validation.isValid ? '✅' : '❌'}`);
      console.log(`Score: ${validation.score}/100`);
    }
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 Test Results Summary:');
    console.log(`Connection: ${results.connection ? '✅' : '❌'}`);
    console.log(`Simple Form: ${results.simple ? '✅' : '❌'}`);
    console.log(`Complex Form: ${results.complex ? '✅' : '❌'}`);
    console.log(`Validation: ${results.validation ? '✅' : '❌'}`);
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      console.log('🎉 All tests passed! AI form generation is working perfectly!');
    } else {
      console.log('⚠️ Some tests failed. Check the console for details.');
    }
    
    return results;
  },

  /**
   * Helper function to calculate layout depth
   */
  calculateLayoutDepth(layout, depth = 0) {
    if (!layout || !Array.isArray(layout)) return depth;
    
    let maxDepth = depth;
    layout.forEach(item => {
      if (item.children && Array.isArray(item.children)) {
        const childDepth = this.calculateLayoutDepth(item.children, depth + 1);
        maxDepth = Math.max(maxDepth, childDepth);
      }
    });
    
    return maxDepth;
  },

  /**
   * Quick examples for testing
   */
  examples: {
    simple: "Create a contact form with name, email, phone, and message",
    registration: "Create a user registration form with personal details and preferences",
    survey: "Create a customer satisfaction survey with rating questions",
    application: "Create a job application form with personal info, experience, and file uploads",
    onboarding: "Create a 3-step employee onboarding form with personal info, documents, and preferences",
    supplier: "Create a comprehensive supplier onboarding form with company details, compliance, and contracts",
    ecommerce: "Create a product order form with item selection, customer details, and payment",
    feedback: "Create a detailed feedback form with ratings, comments, and suggestions"
  },

  /**
   * Show usage instructions
   */
  help() {
    console.log(`
🤖 AI Form Generation Test Console

Available Commands:
==================

Basic Tests:
- AIFormTests.testConnection()           // Test AI connection
- AIFormTests.testSimpleForm()          // Generate a simple form
- AIFormTests.testComplexForm()         // Generate a complex form
- AIFormTests.testComplexityDetection() // Test complexity detection
- AIFormTests.testComponentSuggestions() // Test component suggestions

Advanced:
- AIFormTests.generateAndApply("prompt") // Generate and apply form
- AIFormTests.runFullTest()             // Run comprehensive test suite

Examples:
- AIFormTests.generateAndApply(AIFormTests.examples.simple)
- AIFormTests.generateAndApply(AIFormTests.examples.onboarding)
- AIFormTests.generateAndApply(AIFormTests.examples.supplier)

Quick Start:
============
1. AIFormTests.testConnection()
2. AIFormTests.generateAndApply("Create a contact form")
3. Check the form builder for results!

For full testing: AIFormTests.runFullTest()
    `);
  }
};

// Show help on load
console.log('🤖 AI Form Generation Test Console Loaded!');
console.log('Type AIFormTests.help() for usage instructions');

export default window.AIFormTests;
