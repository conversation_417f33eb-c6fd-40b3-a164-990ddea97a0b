/**
 * Advanced Layout Tree Navigator
 * Enterprise-grade tree navigation system for unlimited nesting support
 * 
 * Features:
 * - UUID-based component resolution with O(1) lookup
 * - Path-based navigation with container awareness
 * - Optimized tree traversal algorithms
 * - Component relationship mapping
 * - Validation and error recovery
 * - Performance monitoring and caching
 * 
 * <AUTHOR> Builder Team
 * @version 2.0.0
 */

import { COMPONENT_CAPABILITIES } from '../constants';

/**
 * Advanced Layout Tree Navigator Class
 * Provides sophisticated tree navigation with enterprise performance
 */
export class AdvancedLayoutTreeNavigator {
  constructor() {
    this.componentIndex = new Map(); // UUID -> Component mapping
    this.pathIndex = new Map();      // Path -> Component mapping
    this.parentIndex = new Map();    // Child ID -> Parent ID mapping
    this.childrenIndex = new Map();  // Parent ID -> Children IDs mapping
    this.debugMode = process.env.NODE_ENV === 'development';
    
    // Performance metrics
    this.metrics = {
      indexBuilds: 0,
      lookups: 0,
      cacheHits: 0,
      traversals: 0,
    };
  }

  /**
   * Builds comprehensive indices for fast navigation
   * @param {Array} layout - Layout tree structure
   * @param {Object} components - Components registry
   */
  buildIndices(layout, components = {}) {
    this.metrics.indexBuilds++;
    
    // Clear existing indices
    this.componentIndex.clear();
    this.pathIndex.clear();
    this.parentIndex.clear();
    this.childrenIndex.clear();

    // Build indices recursively
    this.buildIndicesRecursive(layout, components, 'root', null);

    if (this.debugMode) {
      console.log('🗂️ [TreeNavigator] Indices built:', {
        componentCount: this.componentIndex.size,
        pathCount: this.pathIndex.size,
        parentCount: this.parentIndex.size,
        childrenCount: this.childrenIndex.size,
      });
    }
  }

  /**
   * Recursive index building
   * @private
   */
  buildIndicesRecursive(items, components, currentPath, parentId) {
    if (!Array.isArray(items)) return;

    items.forEach((item, index) => {
      const itemPath = `${currentPath}.${item.type}${index}`;
      
      // Index component by ID and path
      this.componentIndex.set(item.id, item);
      this.pathIndex.set(itemPath, item);
      
      // Index parent-child relationships
      if (parentId) {
        this.parentIndex.set(item.id, parentId);
        
        if (!this.childrenIndex.has(parentId)) {
          this.childrenIndex.set(parentId, []);
        }
        this.childrenIndex.get(parentId).push(item.id);
      }

      // Handle container-specific children
      this.indexContainerChildren(item, components, itemPath);
      
      // Index regular children
      if (item.children) {
        this.buildIndicesRecursive(item.children, components, itemPath, item.id);
      }
    });
  }

  /**
   * Indexes container-specific children (steps, tabs, etc.)
   * @private
   */
  indexContainerChildren(item, components, itemPath) {
    const component = components[item.id];
    if (!component) return;

    // Handle StepsContainer
    if (item.steps || component.steps) {
      const steps = item.steps || component.steps || [];
      steps.forEach((step, stepIndex) => {
        const stepPath = `${itemPath}.step${stepIndex}`;
        this.pathIndex.set(stepPath, step);
        
        if (step.children) {
          this.buildIndicesRecursive(step.children, components, stepPath, item.id);
        }
      });
    }

    // Handle TabContainer
    if (item.tabs || component.tabs) {
      const tabs = item.tabs || component.tabs || [];
      tabs.forEach((tab, tabIndex) => {
        const tabPath = `${itemPath}.tab${tabIndex}`;
        this.pathIndex.set(tabPath, tab);
        
        if (tab.children) {
          this.buildIndicesRecursive(tab.children, components, tabPath, item.id);
        }
      });
    }

    // Handle AccordionContainer
    if (item.panels || component.panels) {
      const panels = item.panels || component.panels || [];
      panels.forEach((panel, panelIndex) => {
        const panelPath = `${itemPath}.panel${panelIndex}`;
        this.pathIndex.set(panelPath, panel);
        
        if (panel.children) {
          this.buildIndicesRecursive(panel.children, components, panelPath, item.id);
        }
      });
    }
  }

  /**
   * Finds component by ID with O(1) lookup
   * @param {string} componentId - Component UUID
   * @returns {Object|null} Component data
   */
  findComponentById(componentId) {
    this.metrics.lookups++;
    
    const component = this.componentIndex.get(componentId);
    
    if (component) {
      this.metrics.cacheHits++;
    }

    return component || null;
  }

  /**
   * Finds component by hierarchical path
   * @param {string} path - Hierarchical path
   * @returns {Object|null} Component data
   */
  findComponentByPath(path) {
    this.metrics.lookups++;
    
    const component = this.pathIndex.get(path);
    
    if (component) {
      this.metrics.cacheHits++;
    }

    return component || null;
  }

  /**
   * Navigates to target location and returns navigation context
   * @param {string} targetPath - Target hierarchical path
   * @param {Array} layout - Layout structure
   * @param {Object} components - Components registry
   * @returns {Object} Navigation result
   */
  navigateToPath(targetPath, layout, components = {}) {
    this.metrics.traversals++;

    try {
      const pathSegments = targetPath.split('.');
      let currentContext = { layout, path: 'root' };
      
      // Navigate through each path segment
      for (let i = 1; i < pathSegments.length; i++) {
        const segment = pathSegments[i];
        const nextContext = this.navigateSegment(segment, currentContext, components);
        
        if (!nextContext) {
          return {
            success: false,
            error: `Navigation failed at segment: ${segment}`,
            path: pathSegments.slice(0, i + 1).join('.'),
          };
        }
        
        currentContext = nextContext;
      }

      return {
        success: true,
        context: currentContext,
        path: targetPath,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: targetPath,
      };
    }
  }

  /**
   * Navigates a single path segment
   * @private
   */
  navigateSegment(segment, currentContext, components) {
    // Parse segment type and index
    const match = segment.match(/^(\w+)(\d+)$/);
    if (!match) return null;

    const [, type, indexStr] = match;
    const index = parseInt(indexStr, 10);

    switch (type) {
      case 'row':
        return this.navigateToRow(index, currentContext);
      
      case 'col':
        return this.navigateToColumn(index, currentContext);
      
      case 'comp':
        return this.navigateToComponent(index, currentContext);
      
      case 'step':
        return this.navigateToStep(index, currentContext, components);
      
      case 'tab':
        return this.navigateToTab(index, currentContext, components);
      
      case 'panel':
        return this.navigateToPanel(index, currentContext, components);
      
      default:
        // Handle container types
        return this.navigateToContainer(type, index, currentContext);
    }
  }

  /**
   * Navigation helpers for different component types
   * @private
   */
  navigateToRow(index, context) {
    const { layout } = context;
    if (!layout || !layout[index]) return null;

    return {
      layout: layout[index].children || [],
      component: layout[index],
      path: `${context.path}.row${index}`,
      type: 'row',
      index,
    };
  }

  navigateToColumn(index, context) {
    const { layout } = context;
    if (!layout || !layout[index]) return null;

    return {
      layout: layout[index].children || [],
      component: layout[index],
      path: `${context.path}.col${index}`,
      type: 'column',
      index,
    };
  }

  navigateToComponent(index, context) {
    const { layout } = context;
    if (!layout || !layout[index]) return null;

    return {
      layout: [],
      component: layout[index],
      path: `${context.path}.comp${index}`,
      type: 'component',
      index,
    };
  }

  navigateToStep(index, context, components) {
    const { component } = context;
    if (!component) return null;

    const componentData = components[component.id];
    const steps = component.steps || componentData?.steps || [];
    
    if (!steps[index]) return null;

    return {
      layout: steps[index].children || [],
      component: steps[index],
      path: `${context.path}.step${index}`,
      type: 'step',
      index,
      parentContainer: component,
    };
  }

  navigateToTab(index, context, components) {
    const { component } = context;
    if (!component) return null;

    const componentData = components[component.id];
    const tabs = component.tabs || componentData?.tabs || [];
    
    if (!tabs[index]) return null;

    return {
      layout: tabs[index].children || [],
      component: tabs[index],
      path: `${context.path}.tab${index}`,
      type: 'tab',
      index,
      parentContainer: component,
    };
  }

  navigateToPanel(index, context, components) {
    const { component } = context;
    if (!component) return null;

    const componentData = components[component.id];
    const panels = component.panels || componentData?.panels || [];
    
    if (!panels[index]) return null;

    return {
      layout: panels[index].children || [],
      component: panels[index],
      path: `${context.path}.panel${index}`,
      type: 'panel',
      index,
      parentContainer: component,
    };
  }

  navigateToContainer(type, index, context) {
    const { layout } = context;
    if (!layout || !layout[index]) return null;

    return {
      layout: layout[index].children || [],
      component: layout[index],
      path: `${context.path}.${type}${index}`,
      type,
      index,
    };
  }

  /**
   * Gets all children of a component
   * @param {string} componentId - Parent component ID
   * @returns {Array} Array of child component IDs
   */
  getChildren(componentId) {
    return this.childrenIndex.get(componentId) || [];
  }

  /**
   * Gets parent of a component
   * @param {string} componentId - Child component ID
   * @returns {string|null} Parent component ID
   */
  getParent(componentId) {
    return this.parentIndex.get(componentId) || null;
  }

  /**
   * Gets performance metrics
   */
  getMetrics() {
    const hitRate = this.metrics.cacheHits / this.metrics.lookups * 100;

    return {
      ...this.metrics,
      hitRate: hitRate.toFixed(2) + '%',
      indexSize: this.componentIndex.size,
    };
  }

  /**
   * Clears all indices and metrics
   */
  clear() {
    this.componentIndex.clear();
    this.pathIndex.clear();
    this.parentIndex.clear();
    this.childrenIndex.clear();
    
    this.metrics = {
      indexBuilds: 0,
      lookups: 0,
      cacheHits: 0,
      traversals: 0,
    };
  }
}

// Export singleton instance
export const treeNavigator = new AdvancedLayoutTreeNavigator();

// Export for testing
export default AdvancedLayoutTreeNavigator;
