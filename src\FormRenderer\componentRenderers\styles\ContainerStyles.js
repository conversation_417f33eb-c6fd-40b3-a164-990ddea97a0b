/**
 * Container Component Styling
 * 
 * Styled components for container components like tabs, cards, sections, etc.
 * Provides consistent styling for complex layout components.
 */

import styled from 'styled-components';

/**
 * Base container wrapper with common styling
 * 
 * Provides consistent base styling for all container components.
 */
export const ContainerWrapper = styled.div`
  width: 100%;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

/**
 * Tab container styling
 * 
 * Provides styling specific to tab container components.
 */
export const TabContainerWrapper = styled(ContainerWrapper)`
  .ant-tabs {
    .ant-tabs-tab {
      padding: 12px 16px;
      font-weight: 500;
      border-radius: 6px 6px 0 0;
      transition: all 0.3s ease;
      
      &:hover {
        color: #1890ff;
        background-color: #f0f8ff;
      }
      
      &.ant-tabs-tab-active {
        background-color: #fff;
        border-bottom: 2px solid #1890ff;
      }
    }
    
    .ant-tabs-content-holder {
      background-color: #fff;
      border-radius: 0 6px 6px 6px;
      border: 1px solid #d9d9d9;
      border-top: none;
    }
    
    .ant-tabs-tabpane {
      padding: 16px;
    }
  }
  
  /* Card type tabs */
  .ant-tabs-card {
    .ant-tabs-tab {
      border: 1px solid #d9d9d9;
      border-bottom: none;
      background-color: #fafafa;
      
      &.ant-tabs-tab-active {
        background-color: #fff;
        border-bottom: 1px solid #fff;
      }
    }
  }
`;

/**
 * Card container styling
 * 
 * Provides styling specific to card container components.
 */
export const CardContainerWrapper = styled(ContainerWrapper)`
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      border-radius: 8px 8px 0 0;
      
      .ant-card-head-title {
        font-weight: 600;
        font-size: 16px;
        color: #262626;
      }
    }
    
    .ant-card-body {
      padding: 20px;
    }
  }
  
  /* Small card styling */
  .ant-card-small {
    .ant-card-head {
      min-height: 48px;
      padding: 0 16px;
      
      .ant-card-head-title {
        font-size: 14px;
      }
    }
    
    .ant-card-body {
      padding: 16px;
    }
  }
`;

/**
 * Form section styling
 * 
 * Provides styling specific to form section components.
 */
export const FormSectionWrapper = styled(ContainerWrapper)`
  .form-section {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    background-color: #fff;
    overflow: hidden;
    
    .form-section-header {
      padding: 16px 20px;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      border-bottom: 1px solid #d9d9d9;
      
      .form-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        margin: 0;
      }
      
      .form-section-description {
        font-size: 14px;
        color: #8c8c8c;
        margin: 4px 0 0 0;
      }
    }
    
    .form-section-content {
      padding: 20px;
    }
  }
  
  /* Collapsible section styling */
  .ant-collapse {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    background-color: #fff;
    
    .ant-collapse-item {
      border-bottom: none;
      
      &:last-child {
        border-radius: 0 0 8px 8px;
      }
    }
    
    .ant-collapse-header {
      padding: 16px 20px;
      font-weight: 600;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      
      &:hover {
        background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
      }
    }
    
    .ant-collapse-content {
      border-top: 1px solid #d9d9d9;
      
      .ant-collapse-content-box {
        padding: 20px;
      }
    }
  }
`;

/**
 * Accordion container styling
 * 
 * Provides styling specific to accordion container components.
 */
export const AccordionContainerWrapper = styled(ContainerWrapper)`
  .ant-collapse {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    background-color: #fff;
    
    .ant-collapse-item {
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
        border-radius: 0 0 8px 8px;
      }
      
      &:first-child {
        border-radius: 8px 8px 0 0;
      }
    }
    
    .ant-collapse-header {
      padding: 16px 20px;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: #f0f8ff;
      }
      
      .ant-collapse-arrow {
        color: #1890ff;
        font-size: 12px;
      }
    }
    
    .ant-collapse-content {
      .ant-collapse-content-box {
        padding: 16px 20px 20px;
        background-color: #fafafa;
      }
    }
  }
  
  /* Ghost accordion styling */
  .ant-collapse-ghost {
    border: none;
    background: transparent;
    
    .ant-collapse-item {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
`;

/**
 * Steps container styling
 * 
 * Provides styling specific to steps container components.
 */
export const StepsContainerWrapper = styled(ContainerWrapper)`
  .ant-steps {
    margin-bottom: 24px;
    
    .ant-steps-item {
      .ant-steps-item-icon {
        border-radius: 50%;
        transition: all 0.3s ease;
      }
      
      &.ant-steps-item-active {
        .ant-steps-item-icon {
          background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
          border-color: #1890ff;
        }
        
        .ant-steps-item-title {
          color: #1890ff;
          font-weight: 600;
        }
      }
      
      &.ant-steps-item-finish {
        .ant-steps-item-icon {
          background-color: #52c41a;
          border-color: #52c41a;
        }
        
        .ant-steps-item-title {
          color: #52c41a;
        }
      }
    }
  }
  
  .steps-content {
    min-height: 200px;
    padding: 24px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    background-color: #fff;
    
    .steps-content-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 150px;
      color: #8c8c8c;
      font-style: italic;
      text-align: center;
    }
  }
`;

/**
 * Grid container styling
 * 
 * Provides styling specific to grid container components.
 */
export const GridContainerWrapper = styled(ContainerWrapper)`
  .grid-container {
    display: grid;
    gap: 16px;
    padding: 16px;
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    background-color: #fafafa;
    
    .grid-item {
      padding: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      background-color: #fff;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      }
    }
  }
  
  /* Responsive grid */
  @media (max-width: 768px) {
    .grid-container {
      grid-template-columns: 1fr !important;
      gap: 12px;
    }
  }
`;

/**
 * Flex container styling
 * 
 * Provides styling specific to flex container components.
 */
export const FlexContainerWrapper = styled(ContainerWrapper)`
  .flex-container {
    display: flex;
    gap: 16px;
    padding: 16px;
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    background-color: #fafafa;
    
    .flex-item {
      padding: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      background-color: #fff;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      }
    }
  }
  
  /* Responsive flex */
  @media (max-width: 768px) {
    .flex-container {
      flex-direction: column;
      gap: 12px;
    }
  }
`;
