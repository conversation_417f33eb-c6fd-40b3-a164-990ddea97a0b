/**
 * @fileoverview Form Builder constants and configuration
 *
 * This module provides constants, configuration values, and enums
 * used throughout the form builder application.
 *
 * @module formBuilderConstants
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

/**
 * Tab configuration constants
 *
 * @constant {Object} TABS
 */
export const TABS = {
  BUILDER: 'builder',
  PREVIEW: 'preview',
};

/**
 * Modal types for state management
 *
 * @constant {Object} MODAL_TYPES
 */
export const MODAL_TYPES = {
  JSON_VIEW: 'jsonView',
  JSON_IMPORT: 'jsonImport',
};

/**
 * Form builder UI text constants
 *
 * @constant {Object} UI_TEXT
 */
export const UI_TEXT = {
  TABS: {
    BUILDER: '🛠️ Form Builder',
    PREVIEW: '👁️ Form Preview',
  },
  BUTTONS: {
    VIEW_JSON: 'View JSON',
    IMPORT_JSON: 'Import JSON',
    EXPORT_JSON: 'Export JSON',
    TEST_CONTAINERS: '🧪 Test Enhanced Containers',
    COPY_CLIPBOARD: 'Copy to Clipboard',
    IMPORT_SCHEMA: 'Import Schema',
    CLOSE: 'Close',
    RELOAD_PAGE: '🔄 Reload Page',
  },
  HEADERS: {
    FORM_PREVIEW: 'Form Preview',
    JSON_SCHEMA: 'Form JSON Schema',
    IMPORT_SCHEMA: 'Import Form Schema',
    FORM_ERROR: 'Form Rendering Error',
  },
  MESSAGES: {
    LOADING_AI: 'Loading AI Chat...',
    JSON_COPIED: 'JSON copied to clipboard!',
    FORM_SUBMITTED: 'Form submitted successfully!',
    SCHEMA_UPDATED: 'Schema updated successfully!',
    EXPORT_SUCCESS: '✅ Form schema exported successfully!',
    IMPORT_SUCCESS: '✅ Form schema imported successfully!',
    TEST_SCHEMA_LOADED: '✅ Enhanced Container Test Schema Loaded',
    AI_FORM_SUCCESS: '✅ AI-generated form applied successfully!',
  },
  PLACEHOLDERS: {
    JSON_IMPORT:
      'Paste your JSON schema here... (e.g., {"metadata": {...}, "layout": [...], "components": {...}})',
  },
  DESCRIPTIONS: {
    JSON_IMPORT:
      '📋 Paste your form JSON schema below. The schema will be validated before import.',
    FORM_ERROR:
      'There was an issue rendering the form. This might be due to an invalid schema or missing components. Please check the console for details.',
    TEST_SCHEMA:
      'Test schema with enhanced drop zones for all container components has been loaded successfully.',
  },
};

/**
 * Animation and timing constants
 *
 * @deprecated Use Framer Motion animation constants from ./animations.js instead
 * This constant is kept for backward compatibility but will be removed in future versions.
 * Please migrate to the new animation system in ./animations.js
 */

/**
 * Message display durations
 *
 * @constant {Object} MESSAGE_DURATIONS
 */
export const MESSAGE_DURATIONS = {
  SHORT: 3,
  MEDIUM: 5,
  LONG: 8,
  VERY_LONG: 10,
};

/**
 * Layout and sizing constants
 *
 * @constant {Object} LAYOUT
 */
export const LAYOUT = {
  SPLITTER: {
    AI_CHAT: {
      DEFAULT_SIZE: '25%',
      MIN_SIZE: '280px',
      MAX_SIZE: '400px',
    },
    SIDEBAR: {
      DEFAULT_SIZE: '25%',
      MIN_SIZE: '250px',
      MAX_SIZE: '350px',
    },
    CANVAS: {
      DEFAULT_SIZE: '50%',
      MIN_SIZE: '400px',
    },
  },
  MODAL: {
    WIDTH: 900,
    TOP_OFFSET: 20,
  },
  TEXTAREA: {
    JSON_VIEW_ROWS: 24,
    JSON_IMPORT_ROWS: 18,
  },
};

/**
 * Styling constants
 *
 * @constant {Object} STYLES
 */
export const STYLES = {
  COLORS: {
    PRIMARY: '#0066cc',
    PRIMARY_DARK: '#0052a3',
    SUCCESS: '#52c41a',
    ERROR: '#ef4444',
    ERROR_DARK: '#dc2626',
    WARNING: '#faad14',
    TEXT_PRIMARY: '#212121',
    TEXT_SECONDARY: '#616161',
    TEXT_DISABLED: '#666',
    BORDER: '#e0e0e0',
    BACKGROUND: '#fafafa',
    BACKGROUND_LIGHT: '#ffffff',
  },
  GRADIENTS: {
    PRIMARY: 'linear-gradient(135deg, #0066cc 0%, #0052a3 100%)',
    ERROR: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
    BACKGROUND: 'linear-gradient(135deg, #fafafa 0%, #ffffff 100%)',
  },
  SHADOWS: {
    SMALL: '0 2px 8px rgba(0, 0, 0, 0.06)',
    MEDIUM: '0 2px 8px rgba(0, 102, 204, 0.3)',
    ERROR: '0 2px 8px rgba(239, 68, 68, 0.3)',
  },
  BORDER_RADIUS: {
    SMALL: '6px',
    MEDIUM: '8px',
    LARGE: '12px',
  },
  FONT: {
    FAMILY: {
      MONO: '"JetBrains Mono", "Fira Code", "SF Mono", Consolas, monospace',
    },
    SIZES: {
      SMALL: '12px',
      MEDIUM: '13px',
      NORMAL: '14px',
      LARGE: '16px',
      XLARGE: '18px',
      XXLARGE: '20px',
    },
    WEIGHTS: {
      NORMAL: 400,
      MEDIUM: 500,
      SEMIBOLD: 600,
    },
  },
  SPACING: {
    SMALL: '8px',
    MEDIUM: '12px',
    LARGE: '16px',
    XLARGE: '20px',
    XXLARGE: '24px',
    XXXLARGE: '32px',
  },
};

/**
 * Validation constants
 *
 * @constant {Object} VALIDATION
 */
export const VALIDATION = {
  REQUIRED_SCHEMA_PROPS: ['layout', 'components'],
  MAX_COMPONENT_NAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  MIN_LAYOUT_ITEMS: 0,
  MAX_LAYOUT_DEPTH: 10,
  MAX_COMPONENTS_COUNT: 1000,
};

/**
 * Performance constants
 *
 * @constant {Object} PERFORMANCE
 */
export const PERFORMANCE = {
  DEBOUNCE_DELAY: 300,
  THROTTLE_LIMIT: 100,
  MAX_RENDER_ITEMS: 100,
  LAZY_LOAD_THRESHOLD: 50,
};

/**
 * Error messages
 *
 * @constant {Object} ERROR_MESSAGES
 */
export const ERROR_MESSAGES = {
  INVALID_SCHEMA:
    'Invalid schema format. Must contain layout and components properties.',
  VALIDATION_FAILED: 'Schema validation failed',
  IMPORT_FAILED: 'Import failed',
  EXPORT_FAILED: 'Export failed',
  AI_GENERATION_FAILED: 'Failed to apply AI-generated form',
  SCHEMA_UPDATE_FAILED: 'Failed to update schema',
  JSON_PARSE_ERROR: 'JSON Parse Error',
  COMPONENT_NOT_FOUND: 'Component not found',
  LAYOUT_ERROR: 'Layout processing error',
};

/**
 * Success messages
 *
 * @constant {Object} SUCCESS_MESSAGES
 */
export const SUCCESS_MESSAGES = {
  SCHEMA_EXPORTED: 'Form schema exported successfully!',
  SCHEMA_IMPORTED: 'Form schema imported successfully!',
  SCHEMA_UPDATED: 'Schema updated successfully!',
  FORM_SUBMITTED: 'Form submitted successfully!',
  AI_FORM_APPLIED: 'AI-generated form applied successfully!',
  TEST_SCHEMA_LOADED: 'Enhanced Container Test Schema Loaded',
  JSON_COPIED: 'JSON copied to clipboard!',
};

/**
 * Default configuration values
 *
 * @constant {Object} DEFAULTS
 */
export const DEFAULTS = {
  FORM_PROPS: {
    layout: 'vertical',
    size: 'large',
  },
  SCHEMA_METADATA: {
    version: '1.0.0',
    title: 'Generated Form',
  },
  COMPONENT_UPDATE_COUNTER: 0,
  FORCE_RENDER_KEY: 0,
  AI_GENERATION_ATTEMPTS: 0,
};
