/**
 * LoadingMessage Component
 * 
 * Displays a loading message with animated typing indicator and progress bar
 * during AI form generation. Provides visual feedback about the current
 * processing stage and estimated progress.
 * 
 * Features:
 * - Animated typing dots indicator
 * - Dynamic progress bar based on processing stage
 * - Smooth entrance and exit animations
 * - Professional loading state design
 * - Real-time processing stage updates
 * 
 * @param {Object} props - Component props
 * @param {string} props.processingStage - Current processing stage text
 * @param {number} props.processingProgress - Progress percentage (0-100)
 */

import React, { memo } from 'react';
import { Avatar, Typography, Progress } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { MessageBubble, MessageContent } from '../styles/StyledComponents';
import { ProcessingIndicator, TypingIndicator } from '../styles/StatusComponents';
import { loadingMessageVariants, processingVariants } from '../constants/animations';

const { Text } = Typography;

/**
 * LoadingMessage component for AI processing states
 * 
 * @param {Object} props - Component props
 * @returns {JSX.Element} Rendered loading message component
 */
const LoadingMessage = memo(({ processingStage, processingProgress }) => {
  /**
   * Gets the processing stage text with fallback
   */
  const getProcessingText = () => {
    return processingStage || 'AI is thinking...';
  };

  /**
   * Gets the progress percentage with validation
   */
  const getProgressPercentage = () => {
    // Ensure progress is within valid range
    const progress = Math.max(0, Math.min(100, processingProgress || 15));
    return progress;
  };

  /**
   * Gets the progress bar color based on stage
   */
  const getProgressColor = () => {
    return {
      '0%': '#3b82f6',
      '100%': '#2563eb',
    };
  };

  return (
    <MessageBubble
      isUser={false}
      variants={loadingMessageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
    >
      {/* Loading Avatar with Spinning Icon */}
      <Avatar
        size={28}
        icon={<LoadingOutlined spin />}
        style={{
          backgroundColor: '#3b82f6',
          boxShadow: '0 2px 6px rgba(59, 130, 246, 0.15)',
          flexShrink: 0,
        }}
      />

      {/* Loading Message Content */}
      <MessageContent isUser={false}>
        <ProcessingIndicator
          variants={processingVariants}
          initial="initial"
          animate="animate"
        >
          {/* Animated Typing Dots */}
          <TypingIndicator>
            <div className="dot" />
            <div className="dot" />
            <div className="dot" />
          </TypingIndicator>

          {/* Processing Status and Progress */}
          <div style={{ flex: 1 }}>
            {/* Processing Stage Text */}
            <Text
              strong
              style={{
                fontSize: 13,
                color: '#374151',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
                display: 'block',
                marginBottom: 6,
              }}
            >
              {getProcessingText()}
            </Text>

            {/* Progress Bar */}
            <Progress
              percent={getProgressPercentage()}
              size="small"
              showInfo={false}
              strokeColor={getProgressColor()}
              style={{ 
                marginTop: 6,
                marginBottom: 2,
              }}
              strokeWidth={3}
            />

            {/* Progress Percentage Display */}
            <Text
              type="secondary"
              style={{
                fontSize: 10,
                color: '#9ca3af',
                display: 'block',
                textAlign: 'right',
                marginTop: 2,
              }}
            >
              {getProgressPercentage()}% complete
            </Text>
          </div>
        </ProcessingIndicator>

        {/* Additional Processing Information */}
        <div style={{ marginTop: 8 }}>
          <Text
            type="secondary"
            style={{
              fontSize: 11,
              color: '#6b7280',
              fontStyle: 'italic',
            }}
          >
            {processingStage?.includes('Analyzing') && '🔍 Understanding your requirements...'}
            {processingStage?.includes('Generating') && '🤖 Creating form structure...'}
            {processingStage?.includes('Validating') && '✅ Ensuring quality and compatibility...'}
            {processingStage?.includes('Applying') && '🚀 Preparing your form...'}
            {!processingStage && '💭 Processing your request...'}
          </Text>
        </div>
      </MessageContent>
    </MessageBubble>
  );
});

// Set display name for debugging
LoadingMessage.displayName = 'LoadingMessage';

export default LoadingMessage;
