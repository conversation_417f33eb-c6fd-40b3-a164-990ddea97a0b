import React, { useState } from 'react';
import { FormRenderer } from './FormRenderer';
import { Card, Typography, Button, Space, Alert, Divider } from 'antd';
import { complexFormSchema, advancedNestedFormSchema } from './data';

const { Title, Paragraph, Text } = Typography;

const FormRendererDemo = () => {
  const [currentSchema, setCurrentSchema] = useState('basic');
  const [submittedData, setSubmittedData] = useState(null);

  const handleSubmit = async (values) => {
    console.log('Standalone form submitted:', values);
    setSubmittedData(values);
    alert('Form submitted successfully! Check console for values.');
  };

  const getActiveSchema = () => {
    return currentSchema === 'advanced'
      ? advancedNestedFormSchema
      : complexFormSchema;
  };

  const getSchemaInfo = () => {
    const schema = getActiveSchema();
    return {
      title: schema.metadata?.title || 'Form Schema',
      description: schema.metadata?.description || 'Form description',
      version: schema.metadata?.version || '1.0.0',
      nestingDepth: schema.metadata?.maxNestingDepth || 'Basic',
      containers: schema.metadata?.supportedContainers || ['Basic containers'],
    };
  };

  const schemaInfo = getSchemaInfo();

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>
          🚀 Universal Transformer Demo - Unlimited Nesting
        </Title>
        <Paragraph>
          This demonstrates the <strong>Universal Transformer Component</strong>{' '}
          with <strong>unlimited nesting capabilities</strong>. The form below
          is rendered entirely from a JSON schema - showcasing complex nested
          structures with tabs, cards, sections, accordions, and steps.
        </Paragraph>

        <Alert
          message="🎯 New Features Demonstrated"
          description="This demo now showcases unlimited nesting with tabs containing cards containing accordions containing form sections containing steps - any level of complexity!"
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />

        <Title level={4}>Enhanced Features:</Title>
        <ul>
          <li>
            <strong>Unlimited Nesting:</strong> Any level of container nesting
            (tabs → cards → accordions → sections → steps)
          </li>
          <li>
            <strong>Advanced Containers:</strong> Accordion panels, multi-step
            forms, responsive grids, flex layouts
          </li>
          <li>
            <strong>Perfect Schema Sync:</strong> Complex nested structures
            stored and rendered perfectly
          </li>
          <li>
            <strong>Enterprise Scale:</strong> Handles the most complex
            enterprise forms
          </li>
          <li>
            <strong>Complete Portability:</strong> Works anywhere with just JSON
            input
          </li>
          <li>
            <strong>Real-time Validation:</strong> Built-in form validation at
            any nesting level
          </li>
        </ul>

        <Divider />

        <Space style={{ marginBottom: '16px' }}>
          <Text strong>Select Demo Schema:</Text>
          <Button
            type={currentSchema === 'basic' ? 'primary' : 'default'}
            onClick={() => setCurrentSchema('basic')}
          >
            Basic Form
          </Button>
          <Button
            type={currentSchema === 'advanced' ? 'primary' : 'default'}
            onClick={() => setCurrentSchema('advanced')}
          >
            Advanced Nested Form
          </Button>
        </Space>

        <Card size="small" style={{ background: '#f8f9fa' }}>
          <Title level={5}>Current Schema Info:</Title>
          <Text>
            <strong>Title:</strong> {schemaInfo.title}
          </Text>
          <br />
          <Text>
            <strong>Version:</strong> {schemaInfo.version}
          </Text>
          <br />
          <Text>
            <strong>Max Nesting Depth:</strong> {schemaInfo.nestingDepth}
          </Text>
          <br />
          <Text>
            <strong>Supported Containers:</strong>{' '}
            {Array.isArray(schemaInfo.containers)
              ? schemaInfo.containers.join(', ')
              : schemaInfo.containers}
          </Text>
        </Card>
      </Card>

      <Card style={{ marginTop: '24px' }}>
        <Title level={3}>
          Live Form Example -{' '}
          {currentSchema === 'advanced' ? 'Advanced Nested' : 'Basic'} Schema
        </Title>

        {currentSchema === 'advanced' && (
          <Alert
            message="🔥 Advanced Nested Form"
            description="This form demonstrates unlimited nesting: Tabs → Cards → Accordions → Form Sections → Steps → More Tabs → More Cards. Navigate through the tabs and expand accordion panels to see the complex nested structure in action!"
            type="success"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        <FormRenderer
          layout={getActiveSchema().layout}
          components={getActiveSchema().components}
          onSubmit={handleSubmit}
          formProps={{
            layout: 'vertical',
            size: 'large',
          }}
          submitButtonText={`Submit ${
            currentSchema === 'advanced' ? 'Advanced' : 'Basic'
          } Form`}
          resetButtonText="Clear Form"
        />
      </Card>

      {submittedData && (
        <Card style={{ marginTop: '24px' }}>
          <Title level={4}>📋 Last Submitted Data</Title>
          <Alert
            message="Form Submission Successful"
            description="The form data below shows how the Universal Transformer captures all nested form values perfectly, regardless of complexity level."
            type="success"
            showIcon
            style={{ marginBottom: '16px' }}
          />
          <pre
            style={{
              background: '#f6f8fa',
              padding: '16px',
              borderRadius: '6px',
              overflow: 'auto',
              fontSize: '12px',
            }}
          >
            {JSON.stringify(submittedData, null, 2)}
          </pre>
        </Card>
      )}
    </div>
  );
};

export default FormRendererDemo;
