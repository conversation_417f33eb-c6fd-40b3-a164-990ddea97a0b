/**
 * Advanced Component Tree Utilities for UUID-based Form Builder
 * Provides robust tree traversal, manipulation, and validation for scalable nested components
 */

import shortid from 'shortid';
import {
  ROW,
  COLUMN,
  COMPONENT,
  TAB_CONTAINER,
  CARD_CONTAINER,
  FORM_SECTION,
  ACCORDION_CONTAINER,
  COLLAPSE_CONTAINER,
  STEPS_CONTAINER,
  GRID_CONTAINER,
  FLEX_CONTAINER,
  MODAL_CONTAINER,
  DRAWER_CONTAINER,
  COMPONENT_CAPABILITIES,
} from '../constants';

/**
 * Finds a component by ID in the layout tree
 * @param {Array} layout - The layout array
 * @param {string} targetId - The ID to find
 * @param {Object} components - Optional components registry for fallback search
 * @returns {Object|null} - { component, parent, index, path }
 */
export const findComponentById = (layout, targetId, components = null) => {
  console.log('🔍 [findComponentById] Searching for ID:', targetId);
  console.log('🔍 [findComponentById] Layout length:', layout?.length || 0);
  console.log(
    '🔍 [findComponentById] Components registry keys:',
    components ? Object.keys(components).length : 'not provided',
  );

  const search = (items, parent = null, parentIndex = -1, currentPath = []) => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const itemPath = [...currentPath, i];

      if (item.id === targetId) {
        console.log('✅ [findComponentById] Found in layout:', {
          id: targetId,
          type: item.type,
          path: itemPath,
        });
        return {
          component: item,
          parent,
          index: i,
          path: itemPath,
          parentIndex,
        };
      }

      // Search in children based on component type
      if (item.children && Array.isArray(item.children)) {
        const result = search(item.children, item, i, itemPath);
        if (result) return result;
      }

      // Search in tabs for tab containers
      if (item.type === TAB_CONTAINER && item.tabs) {
        for (let tabIndex = 0; tabIndex < item.tabs.length; tabIndex++) {
          const tab = item.tabs[tabIndex];
          if (tab.children && Array.isArray(tab.children)) {
            const result = search(tab.children, item, i, [
              ...itemPath,
              'tabs',
              tabIndex,
            ]);
            if (result) return result;
          }
        }
      }

      // Search in steps for steps containers
      if (item.type === STEPS_CONTAINER && item.steps) {
        for (let stepIndex = 0; stepIndex < item.steps.length; stepIndex++) {
          const step = item.steps[stepIndex];
          if (step.children && Array.isArray(step.children)) {
            const result = search(step.children, item, i, [
              ...itemPath,
              'steps',
              stepIndex,
            ]);
            if (result) return result;
          }
        }
      }

      // Search in panels for accordion containers
      if (item.type === ACCORDION_CONTAINER && item.panels) {
        for (
          let panelIndex = 0;
          panelIndex < item.panels.length;
          panelIndex++
        ) {
          const panel = item.panels[panelIndex];
          if (panel.children && Array.isArray(panel.children)) {
            const result = search(panel.children, item, i, [
              ...itemPath,
              'panels',
              panelIndex,
            ]);
            if (result) return result;
          }
        }
      }
    }
    return null;
  };

  // First, search in the layout structure
  const layoutResult = search(layout);
  if (layoutResult) {
    return layoutResult;
  }

  // If not found in layout and components registry is provided, search there as fallback
  if (components && components[targetId]) {
    console.log(
      '🔄 [findComponentById] Found in components registry as fallback:',
      targetId,
    );

    // Create a synthetic result for components registry items
    // This helps when layout structure is not yet synchronized
    return {
      component: {
        id: targetId,
        type: components[targetId].type,
        ...components[targetId],
      },
      parent: null,
      index: -1,
      path: [],
      parentIndex: -1,
      isFromRegistry: true, // Flag to indicate this came from registry
    };
  }

  console.log('❌ [findComponentById] Not found anywhere:', targetId);
  return null;
};

/**
 * Finds the parent container that can accept a specific component type
 * @param {Array} layout - The layout array
 * @param {string} targetId - The target container ID
 * @param {string} componentType - The type of component to be added
 * @returns {Object|null} - Parent container info
 */
export const findValidParentContainer = (layout, targetId, componentType) => {
  const containerInfo = findComponentById(layout, targetId);
  if (!containerInfo) return null;

  const { component } = containerInfo;
  const capabilities = COMPONENT_CAPABILITIES[component.type];

  if (!capabilities || !capabilities.canContain.includes(componentType)) {
    return null;
  }

  return containerInfo;
};

/**
 * Validates if a component can be dropped into a target container
 * @param {Object} sourceComponent - The component being dragged
 * @param {Object} targetContainer - The target container
 * @param {number} targetIndex - The target index
 * @returns {Object} - { isValid, reason }
 */
export const validateDrop = (
  sourceComponent,
  targetContainer,
  targetIndex = -1,
) => {
  if (!sourceComponent || !targetContainer) {
    return { isValid: false, reason: 'Invalid source or target' };
  }

  const targetCapabilities = COMPONENT_CAPABILITIES[targetContainer.type];
  if (!targetCapabilities) {
    return { isValid: false, reason: 'Unknown target container type' };
  }

  // Check if target can contain source type
  if (!targetCapabilities.canContain.includes(sourceComponent.type)) {
    return {
      isValid: false,
      reason: `${targetContainer.type} cannot contain ${sourceComponent.type}`,
    };
  }

  // Check max children limit
  const currentChildrenCount = getChildrenCount(targetContainer);
  if (currentChildrenCount >= targetCapabilities.maxChildren) {
    return {
      isValid: false,
      reason: `Maximum children limit (${targetCapabilities.maxChildren}) reached`,
    };
  }

  // Prevent dropping component into itself or its descendants
  if (isDescendant(sourceComponent, targetContainer)) {
    return {
      isValid: false,
      reason: 'Cannot drop component into itself or its descendants',
    };
  }

  return { isValid: true, reason: 'Valid drop' };
};

/**
 * Gets the children count for a container component
 * @param {Object} container - The container component
 * @returns {number} - Number of children
 */
export const getChildrenCount = (container) => {
  if (!container) return 0;

  switch (container.type) {
    case TAB_CONTAINER:
      return container.tabs
        ? container.tabs.reduce(
            (total, tab) => total + (tab.children ? tab.children.length : 0),
            0,
          )
        : 0;
    case STEPS_CONTAINER:
      return container.steps
        ? container.steps.reduce(
            (total, step) => total + (step.children ? step.children.length : 0),
            0,
          )
        : 0;
    case ACCORDION_CONTAINER:
      return container.panels
        ? container.panels.reduce(
            (total, panel) =>
              total + (panel.children ? panel.children.length : 0),
            0,
          )
        : 0;
    default:
      return container.children ? container.children.length : 0;
  }
};

/**
 * Checks if a component is a descendant of another component
 * @param {Object} ancestor - The potential ancestor component
 * @param {Object} descendant - The potential descendant component
 * @returns {boolean} - True if descendant is a child of ancestor
 */
export const isDescendant = (ancestor, descendant) => {
  if (!ancestor || !descendant || ancestor.id === descendant.id) {
    return false;
  }

  const checkChildren = (children) => {
    if (!children || !Array.isArray(children)) return false;

    for (const child of children) {
      if (child.id === descendant.id) return true;
      if (child.children && checkChildren(child.children)) return true;

      // Check tab containers
      if (child.type === TAB_CONTAINER && child.tabs) {
        for (const tab of child.tabs) {
          if (tab.children && checkChildren(tab.children)) return true;
        }
      }

      // Check steps containers
      if (child.type === STEPS_CONTAINER && child.steps) {
        for (const step of child.steps) {
          if (step.children && checkChildren(step.children)) return true;
        }
      }

      // Check accordion containers
      if (child.type === ACCORDION_CONTAINER && child.panels) {
        for (const panel of child.panels) {
          if (panel.children && checkChildren(panel.children)) return true;
        }
      }
    }
    return false;
  };

  return (
    checkChildren(ancestor.children) ||
    (ancestor.type === TAB_CONTAINER &&
      ancestor.tabs &&
      ancestor.tabs.some((tab) => checkChildren(tab.children))) ||
    (ancestor.type === STEPS_CONTAINER &&
      ancestor.steps &&
      ancestor.steps.some((step) => checkChildren(step.children))) ||
    (ancestor.type === ACCORDION_CONTAINER &&
      ancestor.panels &&
      ancestor.panels.some((panel) => checkChildren(panel.children)))
  );
};

/**
 * Adds a component to a container at a specific position
 * @param {Array} layout - The layout array
 * @param {string} containerId - The container ID
 * @param {Object} component - The component to add
 * @param {number} index - The index to insert at (-1 for end)
 * @param {string} tabId - Optional tab ID for tab containers
 * @param {Object} components - Optional components registry for fallback search
 * @returns {Array} - Updated layout
 */
export const addComponentToContainer = (
  layout,
  containerId,
  component,
  index = -1,
  tabId = null,
  components = null,
) => {
  console.log('🔧 [addComponentToContainer] Called with:', {
    containerId,
    componentId: component?.id,
    componentType: component?.type,
    index,
    tabId,
    hasComponentsRegistry: !!components,
  });

  const newLayout = JSON.parse(JSON.stringify(layout));
  const containerInfo = findComponentById(newLayout, containerId, components);

  if (!containerInfo) {
    console.error('❌ [addComponentToContainer] Container not found:', {
      containerId,
      layoutLength: newLayout.length,
      componentsRegistryKeys: components
        ? Object.keys(components)
        : 'not provided',
    });
    throw new Error(`Container with ID ${containerId} not found`);
  }

  console.log('✅ [addComponentToContainer] Container found:', {
    containerId,
    containerType: containerInfo.component?.type,
    isFromRegistry: containerInfo.isFromRegistry,
    containerSteps: containerInfo.component?.steps?.length || 0,
    containerTabs: containerInfo.component?.tabs?.length || 0,
    targetTabId: tabId,
  });

  const { component: container } = containerInfo;

  // Special handling for containers found in registry but not in layout
  if (containerInfo.isFromRegistry) {
    console.log(
      '⚠️ [addComponentToContainer] Container found in registry but not in layout structure',
    );
    console.log(
      'This suggests a synchronization issue between layout and components registry',
    );

    // For now, we'll try to find the container in the layout structure and add it if needed
    // This is a fallback mechanism to handle race conditions
    const layoutContainer = findComponentById(newLayout, containerId);
    if (!layoutContainer) {
      console.log(
        '🔧 [addComponentToContainer] Attempting to add container to layout structure',
      );
      // This is a complex case that might need special handling
      // For now, we'll throw an error with more context
      throw new Error(
        `Container ${containerId} exists in components registry but not in layout structure. This indicates a synchronization issue.`,
      );
    }
  }

  const validation = validateDrop(component, container, index);

  if (!validation.isValid) {
    throw new Error(`Invalid drop: ${validation.reason}`);
  }

  // Handle different container types
  switch (container.type) {
    case TAB_CONTAINER:
      if (!tabId || !container.tabs) {
        throw new Error('Tab ID required for tab container');
      }
      const tab = container.tabs.find((t) => t.id === tabId);
      if (!tab) {
        throw new Error(`Tab with ID ${tabId} not found`);
      }
      if (!tab.children) tab.children = [];

      if (index === -1 || index >= tab.children.length) {
        tab.children.push(component);
      } else {
        tab.children.splice(index, 0, component);
      }
      break;

    case STEPS_CONTAINER:
      console.log('🔧 [addComponentToContainer] Processing STEPS_CONTAINER:', {
        tabId,
        containerSteps: container.steps?.length || 0,
        stepsIds: container.steps?.map((s) => s.id) || [],
        componentType: component.type,
      });

      if (!tabId || !container.steps) {
        console.error(
          '❌ [addComponentToContainer] Step ID or steps array missing:',
          {
            tabId,
            hasSteps: !!container.steps,
            stepsLength: container.steps?.length || 0,
          },
        );
        throw new Error('Step ID required for steps container');
      }

      const step = container.steps.find((s) => s.id === tabId);
      if (!step) {
        console.error('❌ [addComponentToContainer] Step not found:', {
          targetStepId: tabId,
          availableSteps: container.steps.map((s) => ({
            id: s.id,
            title: s.title,
          })),
        });
        throw new Error(`Step with ID ${tabId} not found`);
      }

      console.log(
        '✅ [addComponentToContainer] Step found, adding component:',
        {
          stepId: step.id,
          stepTitle: step.title,
          currentChildren: step.children?.length || 0,
          componentToAdd: component.id,
          componentType: component.type,
          insertIndex: index,
        },
      );

      if (!step.children) step.children = [];

      // CRITICAL FIX: StepsContainer uses row/column structure like main canvas
      // Wrap non-ROW components in row/column structure
      let componentToAdd = component;
      if (component.type !== ROW) {
        console.log('🔄 [addComponentToContainer] Wrapping component in row/column structure');
        componentToAdd = {
          id: `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: ROW,
          children: [
            {
              id: `col_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              type: COLUMN,
              children: [component],
            },
          ],
        };
      }

      if (index === -1 || index >= step.children.length) {
        step.children.push(componentToAdd);
      } else {
        step.children.splice(index, 0, componentToAdd);
      }

      console.log('✅ [addComponentToContainer] Component added to step:', {
        stepId: step.id,
        newChildrenCount: step.children.length,
        addedComponent: componentToAdd.id,
        addedComponentType: componentToAdd.type,
        wasWrapped: component.type !== ROW,
      });
      break;

    case ACCORDION_CONTAINER:
      if (!tabId || !container.panels) {
        throw new Error('Panel ID required for accordion container');
      }
      const panel = container.panels.find((p) => p.id === tabId);
      if (!panel) {
        throw new Error(`Panel with ID ${tabId} not found`);
      }
      if (!panel.children) panel.children = [];

      if (index === -1 || index >= panel.children.length) {
        panel.children.push(component);
      } else {
        panel.children.splice(index, 0, component);
      }
      break;

    default:
      if (!container.children) container.children = [];

      if (index === -1 || index >= container.children.length) {
        container.children.push(component);
      } else {
        container.children.splice(index, 0, component);
      }
      break;
  }

  return newLayout;
};

/**
 * Removes a component from the layout
 * @param {Array} layout - The layout array
 * @param {string} componentId - The component ID to remove
 * @returns {Array} - Updated layout
 */
export const removeComponentFromLayout = (layout, componentId) => {
  const newLayout = JSON.parse(JSON.stringify(layout));
  const componentInfo = findComponentById(newLayout, componentId);

  if (!componentInfo) {
    throw new Error(`Component with ID ${componentId} not found`);
  }

  const { parent, index, path } = componentInfo;

  if (!parent) {
    // Component is at root level
    newLayout.splice(index, 1);
  } else {
    // Component is nested
    if (parent.type === TAB_CONTAINER) {
      // Handle tab container removal
      const pathSegments = path.slice(0, -1);
      if (
        pathSegments.length >= 2 &&
        pathSegments[pathSegments.length - 2] === 'tabs'
      ) {
        const tabIndex = pathSegments[pathSegments.length - 1];
        const tab = parent.tabs[tabIndex];
        if (tab && tab.children) {
          tab.children.splice(index, 1);
        }
      }
    } else if (parent.type === STEPS_CONTAINER) {
      // Handle steps container removal
      const pathSegments = path.slice(0, -1);
      if (
        pathSegments.length >= 2 &&
        pathSegments[pathSegments.length - 2] === 'steps'
      ) {
        const stepIndex = pathSegments[pathSegments.length - 1];
        const step = parent.steps[stepIndex];
        if (step && step.children) {
          step.children.splice(index, 1);
        }
      }
    } else if (parent.type === ACCORDION_CONTAINER) {
      // Handle accordion container removal
      const pathSegments = path.slice(0, -1);
      if (
        pathSegments.length >= 2 &&
        pathSegments[pathSegments.length - 2] === 'panels'
      ) {
        const panelIndex = pathSegments[pathSegments.length - 1];
        const panel = parent.panels[panelIndex];
        if (panel && panel.children) {
          panel.children.splice(index, 1);
        }
      }
    } else if (parent.children) {
      parent.children.splice(index, 1);
    }
  }

  return newLayout;
};

/**
 * Moves a component from one location to another
 * @param {Array} layout - The layout array
 * @param {string} sourceId - The source component ID
 * @param {string} targetContainerId - The target container ID
 * @param {number} targetIndex - The target index
 * @param {string} targetTabId - Optional target tab ID
 * @param {Object} components - Optional components registry for fallback search
 * @returns {Array} - Updated layout
 */
export const moveComponent = (
  layout,
  sourceId,
  targetContainerId,
  targetIndex = -1,
  targetTabId = null,
  components = null,
) => {
  const sourceInfo = findComponentById(layout, sourceId, components);
  if (!sourceInfo) {
    throw new Error(`Source component with ID ${sourceId} not found`);
  }

  // Remove from source location
  let updatedLayout = removeComponentFromLayout(layout, sourceId);

  // Add to target location
  updatedLayout = addComponentToContainer(
    updatedLayout,
    targetContainerId,
    sourceInfo.component,
    targetIndex,
    targetTabId,
    components, // Pass components registry for fallback search
  );

  return updatedLayout;
};

/**
 * Calculates the nesting depth of a component in the layout tree
 * @param {Array} layout - The layout array
 * @param {string} componentId - The component ID to find depth for
 * @returns {number} - The nesting depth (0 for root level)
 */
export const calculateNestingDepth = (layout, componentId) => {
  const findDepth = (items, targetId, currentDepth = 0) => {
    for (const item of items) {
      if (item.id === targetId) {
        return currentDepth;
      }

      // Check children array
      if (item.children && Array.isArray(item.children)) {
        const depth = findDepth(item.children, targetId, currentDepth + 1);
        if (depth !== -1) return depth;
      }

      // Check tabs structure
      if (item.tabs && Array.isArray(item.tabs)) {
        for (const tab of item.tabs) {
          if (tab.children && Array.isArray(tab.children)) {
            const depth = findDepth(tab.children, targetId, currentDepth + 2); // +2 for tab container + tab
            if (depth !== -1) return depth;
          }
        }
      }

      // Check steps structure
      if (item.steps && Array.isArray(item.steps)) {
        for (const step of item.steps) {
          if (step.children && Array.isArray(step.children)) {
            const depth = findDepth(step.children, targetId, currentDepth + 2); // +2 for steps container + step
            if (depth !== -1) return depth;
          }
        }
      }

      // Check panels structure (accordion)
      if (item.panels && Array.isArray(item.panels)) {
        for (const panel of item.panels) {
          if (panel.children && Array.isArray(panel.children)) {
            const depth = findDepth(panel.children, targetId, currentDepth + 2); // +2 for accordion + panel
            if (depth !== -1) return depth;
          }
        }
      }
    }
    return -1;
  };

  return findDepth(layout, componentId);
};

/**
 * Validates if adding a component would exceed maximum nesting depth
 * @param {Array} layout - The layout array
 * @param {string} targetContainerId - The target container ID
 * @param {string} componentType - The type of component being added
 * @param {number} maxDepth - Maximum allowed nesting depth (default: 10)
 * @returns {Object} - Validation result
 */
export const validateNestingDepth = (
  layout,
  targetContainerId,
  componentType,
  maxDepth = 10,
) => {
  const currentDepth = calculateNestingDepth(layout, targetContainerId);

  if (currentDepth === -1) {
    return {
      isValid: false,
      reason: 'Target container not found',
      currentDepth: 0,
      maxDepth,
    };
  }

  const newDepth = currentDepth + 1;

  if (newDepth > maxDepth) {
    return {
      isValid: false,
      reason: `Maximum nesting depth (${maxDepth}) would be exceeded. Current depth: ${currentDepth}`,
      currentDepth,
      maxDepth,
    };
  }

  return {
    isValid: true,
    reason: 'Nesting depth is valid',
    currentDepth,
    maxDepth,
    newDepth,
  };
};

/**
 * Checks if a component can be nested within another based on type compatibility
 * @param {string} parentType - The parent container type
 * @param {string} childType - The child component type
 * @returns {Object} - Compatibility result
 */
export const validateTypeCompatibility = (parentType, childType) => {
  const parentCapabilities = COMPONENT_CAPABILITIES[parentType];

  if (!parentCapabilities) {
    return {
      isValid: false,
      reason: `Unknown parent type: ${parentType}`,
    };
  }

  if (!parentCapabilities.canContain.includes(childType)) {
    return {
      isValid: false,
      reason: `${parentType} cannot contain ${childType}`,
      allowedTypes: parentCapabilities.canContain,
    };
  }

  return {
    isValid: true,
    reason: 'Type compatibility validated',
    allowedTypes: parentCapabilities.canContain,
  };
};

/**
 * Creates a new component with proper structure
 * @param {string} type - The component type
 * @param {Object} props - Additional properties
 * @returns {Object} - New component
 */
export const createComponent = (type, props = {}) => {
  const baseComponent = {
    id: shortid.generate(),
    type,
    ...props,
  };

  const capabilities = COMPONENT_CAPABILITIES[type];
  if (capabilities && capabilities.isContainer) {
    baseComponent.children = [];
  }

  // Add type-specific properties
  switch (type) {
    case TAB_CONTAINER:
      baseComponent.tabs = props.tabs || [
        {
          id: shortid.generate(),
          key: 'tab1',
          label: 'Tab 1',
          children: [],
        },
      ];
      break;
    case CARD_CONTAINER:
      baseComponent.cardProps = props.cardProps || {
        title: 'Card Title',
        extra: null,
        actions: [],
      };
      break;
    case FORM_SECTION:
      baseComponent.sectionProps = props.sectionProps || {
        title: 'Section Title',
        description: '',
      };
      baseComponent.conditionalLogic = props.conditionalLogic || {
        enabled: false,
        conditions: [],
      };
      break;
    case ACCORDION_CONTAINER:
      baseComponent.panels = props.panels || [
        {
          id: shortid.generate(),
          key: 'panel1',
          header: 'Panel 1',
          children: [],
        },
      ];
      baseComponent.accordionProps = props.accordionProps || {
        defaultActiveKey: ['panel1'],
        ghost: false,
        bordered: true,
      };
      break;
    case STEPS_CONTAINER:
      baseComponent.steps = props.steps || [
        {
          id: shortid.generate(),
          key: 'step1',
          title: 'Step 1',
          description: 'First step',
          children: [],
        },
        {
          id: shortid.generate(),
          key: 'step2',
          title: 'Step 2',
          description: 'Second step',
          children: [],
        },
      ];
      baseComponent.stepsProps = props.stepsProps || {
        current: 0,
        direction: 'horizontal',
        size: 'default',
      };
      break;
    case GRID_CONTAINER:
      baseComponent.gridProps = props.gridProps || {
        gutter: [16, 16],
        justify: 'start',
        align: 'top',
      };
      break;
    case FLEX_CONTAINER:
      baseComponent.flexProps = props.flexProps || {
        direction: 'row',
        justify: 'flex-start',
        align: 'flex-start',
        wrap: 'nowrap',
      };
      break;
  }

  return baseComponent;
};
