/**
 * Form<PERSON>enderer Constants - Barrel Export
 *
 * Centralized export point for all FormRenderer constants and configurations.
 * Provides a clean import interface for consuming components.
 */

// Export specific constants to avoid conflicts
export {
  DEFAULT_FORM_PROPS,
  DEFAULT_FORM_RENDERER_PROPS,
  DEFAULT_BUTTON_PROPS,
  DEFAULT_FORM_ITEM_STYLE,
  FORM_LAYOUTS,
  FORM_SIZES,
  VALIDATION_TRIGGERS,
  FORM_SPACING,
  FORM_BREAKPOINTS,
  PERFORMANCE_DEFAULTS,
} from './formDefaults';

export {
  DEFAULT_VALIDATION_MESSAGES,
  VALIDATION_MESSAGE_TEMPLATES,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_MESSAGES,
  FORM_STATE_MESSAGES,
  FIELD_VALIDATION_MESSAGES,
} from './validationMessages';

export {
  FORM_COMPONENT_TYPES,
  VAL<PERSON>ATION_TYPES,
  FORM_LAYOUT_TYPES,
  FORM_SIZE_TYPES,
  BUTTO<PERSON>_TYPES as FOR<PERSON>_BUTTON_TYPES,
  SU<PERSON>IS<PERSON>ON_STATES,
  <PERSON><PERSON><PERSON><PERSON><PERSON>_STATES,
  FIELD_STATES,
  FORM_EVENTS,
} from './formTypes';
