/**
 * AIChatSection Status Components
 *
 * This module contains styled components related to AI status, connection indicators,
 * and processing states. Separated for better organization and maintainability.
 *
 * Features:
 * - AI connection status indicators with animations
 * - Processing state visualizations
 * - Modern typing indicators with smooth animations
 * - Enterprise-grade status bar design
 */

import styled from 'styled-components';
import { motion } from 'framer-motion';
import { colors } from '../../../styles/theme';

/**
 * AI Status Bar with connection-based styling
 * Features dynamic background gradients based on connection state
 */
export const AIStatusBar = styled(motion.div)`
  padding: 16px 20px;
  background: ${(props) =>
    props.connected
      ? 'linear-gradient(135deg, rgba(16, 185, 129, 0.03) 0%, rgba(59, 130, 246, 0.02) 100%)'
      : 'linear-gradient(135deg, rgba(245, 158, 11, 0.03) 0%, rgba(239, 68, 68, 0.02) 100%)'};
  border-top: 1px solid rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  position: relative;
  backdrop-filter: blur(20px);

  /* Modern status indicator line */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: ${(props) =>
      props.connected
        ? 'linear-gradient(90deg, #10b981 0%,rgb(33, 98, 202) 100%)'
        : 'linear-gradient(90deg, #f59e0b 0%, #ef4444 100%)'};
    opacity: ${(props) => (props.connected ? 1 : 0.8)};
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
`;

/**
 * Connection status indicator with animated dot and ripple effects
 * Features different animations based on connection state
 */
export const ConnectionIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;

  .status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: ${(props) =>
      props.connected ? colors.aiSuccess : colors.error};
    box-shadow: ${(props) =>
      props.connected
        ? `0 0 8px ${colors.aiSuccess}40`
        : `0 0 8px ${colors.error}40`};
    animation: ${(props) =>
      props.connected ? 'aiPulse 2s infinite' : 'errorBlink 1s infinite'};
    position: relative;

    /* Ripple effect for connected state */
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: 50%;
      background: ${(props) =>
        props.connected ? colors.aiSuccess : colors.error};
      opacity: 0.3;
      animation: ${(props) =>
        props.connected ? 'aiRipple 2s infinite' : 'none'};
    }
  }

  /* Animation keyframes for connection states */
  @keyframes aiPulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  @keyframes aiRipple {
    0% {
      transform: scale(1);
      opacity: 0.3;
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }

  @keyframes errorBlink {
    0%,
    50% {
      opacity: 1;
    }
    51%,
    100% {
      opacity: 0.3;
    }
  }
`;

/**
 * Processing indicator with animated background and progress visualization
 * Used during AI form generation with smooth animations
 */
export const ProcessingIndicator = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.03) 0%,
    rgba(147, 197, 253, 0.02) 100%
  );
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  margin: 8px 0;
  backdrop-filter: blur(20px);
  position: relative;

  /* Animated glow effect during processing */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.02) 0%,
      rgba(147, 197, 253, 0.01) 100%
    );
    animation: aiProcessingGlow 2s ease-in-out infinite;
  }

  @keyframes aiProcessingGlow {
    0%,
    100% {
      opacity: 0.3;
    }
    50% {
      opacity: 0.8;
    }
  }
`;

/**
 * Typing indicator with animated dots
 * Provides visual feedback when AI is processing user input
 */
export const TypingIndicator = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #9ca3af;
    animation: typingDot 1.4s infinite ease-in-out;

    /* Staggered animation delays for wave effect */
    &:nth-child(1) {
      animation-delay: 0s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }

  @keyframes typingDot {
    0%,
    60%,
    100% {
      transform: translateY(0);
      opacity: 0.4;
    }
    30% {
      transform: translateY(-8px);
      opacity: 1;
    }
  }
`;

/**
 * Action buttons container for message interactions
 * Used for copy, modify, and other message-related actions
 */
export const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 8px;
`;

/**
 * Complexity tag with consistent styling
 * Shows the complexity level of generated forms
 */
export const ComplexityTag = styled.div`
  margin: 4px 0;
  font-size: 11px;
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  font-weight: 500;
`;
