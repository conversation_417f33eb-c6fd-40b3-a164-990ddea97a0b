/**
 * ComponentRenderer Utils - Barrel Export
 *
 * Centralized export point for all ComponentRenderer utility functions.
 * Provides a clean import interface for consuming components.
 */

// Export specific functions to avoid conflicts
export {
  isContainerComponent,
  isFormInputComponent,
  isDisplayComponent,
  getComponentCategory as getUtilsComponentCategory,
  validateComponent,
  getDefaultPropsForType,
  mergeComponentDefaults,
  getFieldName,
  shouldWrapInFormItem as shouldWrapInFormItemUtil,
} from './componentUtils';

export {
  processStylingProps,
  processValidationProps,
  processOptionsProps,
  processContainerProps,
  processDataProps,
  processEventHandlers,
  mergeFinalProps,
} from './propProcessors';

export {
  COMPONENT_CATEGORIES,
  detectComponentCategory,
  isValidComponentType as isValidComponentTypeUtil,
  getComponentTypesByCategory,
  requiresFormBinding as requiresFormBindingUtil,
  supportsValidation as supportsValidationUtil,
  canHaveChildren as canHaveChildrenUtil,
  getR<PERSON>erFunctionName,
  getTypeSpecificDefaults,
  validateComponentByType,
} from './typeDetection';

export {
  createSafeRenderer,
  renderErrorComponent,
  renderPlaceholderComponent,
  conditionalFormItemWrapper,
  createMemoizedRenderer,
  applyConditionalStyling,
  validateRenderProps,
  createRenderContext,
  createAsyncRenderer,
  createDebugRenderer,
} from './renderHelpers';
