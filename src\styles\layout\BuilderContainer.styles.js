import styled from 'styled-components';
import { colors } from '../theme';

// Enhanced responsive builder container with modern design
export const BuilderContainer = styled.div`
  display: flex;
  flex-direction: row;
  height: calc(100vh - 56px);
  overflow: hidden;
  background: linear-gradient(
    135deg,
    ${colors.backgroundTertiary} 0%,
    ${colors.background} 100%
  );
  margin: 0;
  position: relative;

  /* Enhanced Ant Design Splitter customization */
  .ant-splitter {
    background: transparent;

    .ant-splitter-bar {
      background: linear-gradient(
        180deg,
        ${colors.border} 0%,
        ${colors.borderLight} 100%
      );
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 2px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 3px;
        height: 20px;
        background: ${colors.textTertiary};
        border-radius: 2px;
        opacity: 0.5;
        transition: opacity 0.2s ease;
      }

      &:hover {
        background: linear-gradient(
          180deg,
          ${colors.primary}40 0%,
          ${colors.primary}20 100%
        );
        box-shadow: 0 0 8px ${colors.primary}20;

        &::before {
          opacity: 1;
          background: ${colors.primary};
        }
      }

      &.ant-splitter-bar-active {
        background: linear-gradient(
          180deg,
          ${colors.primary}60 0%,
          ${colors.primary}40 100%
        );
        box-shadow: 0 0 12px ${colors.primary}30;
      }
    }

    .ant-splitter-bar-dragger {
      background: transparent;
      border-radius: 2px;
    }
  }

  /* Responsive design for mobile and tablet */
  @media (max-width: 1024px) {
    height: calc(100vh - 48px);

    .ant-splitter {
      .ant-splitter-bar {
        width: 6px;

        &::before {
          width: 2px;
          height: 16px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    height: calc(100vh - 48px);

    .ant-splitter {
      flex-direction: column;

      .ant-splitter-bar {
        height: 6px;
        width: auto;

        &::before {
          width: 20px;
          height: 2px;
        }
      }
    }
  }
`;

// Enhanced AI Chat Section Container with responsive design
export const AIChatSectionContainer = styled.div`
  height: 100%;
  background: ${colors.background};
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-right: 1px solid ${colors.border};
  position: relative;

  /* Modern glass effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      ${colors.background}95 0%,
      ${colors.backgroundSecondary}90 100%
    );
    backdrop-filter: blur(10px);
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  @media (max-width: 768px) {
    border-right: none;
    border-bottom: 1px solid ${colors.border};
    min-height: 200px;
    max-height: 300px;
  }
`;

// Enhanced Components Section Container with responsive design
export const ComponentsSectionContainer = styled.div`
  height: 100%;
  background: ${colors.background};
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-right: 1px solid ${colors.border};
  position: relative;

  /* Modern glass effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      ${colors.background}95 0%,
      ${colors.backgroundSecondary}90 100%
    );
    backdrop-filter: blur(10px);
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  @media (max-width: 768px) {
    border-right: none;
    border-bottom: 1px solid ${colors.border};
    min-height: 250px;
    max-height: 350px;
  }
`;

// Enhanced Form Builder Canvas Container with responsive design
export const FormBuilderCanvasContainer = styled.div`
  height: 100%;
  background: linear-gradient(
    135deg,
    ${colors.backgroundTertiary} 0%,
    ${colors.background} 100%
  );
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;

  /* Modern canvas effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 25% 25%,
        ${colors.primary}03 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 75% 75%,
        ${colors.aiPrimary}03 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  @media (max-width: 768px) {
    flex: 1;
    min-height: calc(100vh - 600px);
  }

  @media (max-width: 480px) {
    min-height: calc(100vh - 500px);
  }
`;
