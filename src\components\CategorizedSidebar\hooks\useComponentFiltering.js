/**
 * useComponentFiltering Hook
 *
 * Custom hook for handling component categorization and filtering logic.
 * This is the core data processing engine for the CategorizedSidebar,
 * managing the complex pipeline from raw component data to filtered,
 * categorized results ready for UI consumption.
 *
 * Data Processing Pipeline:
 * 1. Validation - Ensures component data integrity
 * 2. Categorization - Groups components by type and functionality
 * 3. Filtering - Applies search-based filtering across multiple fields
 * 4. Statistics - Calculates metrics for UI feedback
 * 5. Performance Monitoring - Tracks processing efficiency
 *
 * Features:
 * - Memoized component categorization for performance
 * - Advanced search filtering with multiple field matching
 * - Statistics calculation for filtered results
 * - Performance monitoring and optimization
 * - Comprehensive error handling for malformed component data
 * - Empty state detection and management
 *
 * Performance Optimizations:
 * - useMemo for expensive categorization operations (prevents recalculation)
 * - Efficient dependency arrays to minimize re-computations
 * - Early return patterns for empty data sets
 * - Optimized array operations with minimal iterations
 * - Memory-efficient object creation patterns
 *
 * Search Algorithm:
 * - Multi-field search across component properties
 * - Case-insensitive matching for better UX
 * - Category-aware filtering with auto-expansion
 * - Real-time filtering with minimal performance impact
 *
 * Error Handling Strategy:
 * - Graceful degradation for malformed data
 * - Console logging for debugging without UI disruption
 * - Fallback values for all computed properties
 * - Validation results exposed for external error handling
 *
 * @param {Array} sidebarItems - Array of sidebar component items with structure:
 *   [{
 *     id: string,
 *     component: {
 *       type: string,
 *       content: string,
 *       label?: string
 *     }
 *   }]
 * @param {string} searchTerm - Current search filter term (case-insensitive)
 * @returns {Object} Comprehensive filtering results:
 *   {
 *     categorizedItems: Object,     // All components organized by category
 *     filteredItems: Object,        // Search-filtered components by category
 *     totalComponents: number,      // Count of filtered components
 *     categoryStats: Object,        // Statistics about categories
 *     validationResult: Object,     // Component validation results
 *     isFiltered: boolean,          // Whether filtering is active
 *     emptyStateInfo: Object,       // Empty state metadata
 *     performanceMetrics: Object    // Performance tracking data
 *   }
 */

import { useMemo } from 'react';
import {
  categorizeComponents,
  filterComponentsBySearch,
  calculateCategoryStats,
  validateComponentStructure,
} from '../utils/componentUtils';

/**
 * Custom hook for component filtering and categorization
 *
 * This hook processes sidebar items through categorization and filtering
 * pipelines, providing optimized results for the sidebar interface.
 *
 * Processing Pipeline:
 * 1. Validate component structure
 * 2. Categorize components by type
 * 3. Apply search filtering
 * 4. Calculate statistics
 * 5. Return processed results
 *
 * @param {Array} sidebarItems - Raw sidebar component items
 * @param {string} searchTerm - Current search filter term
 * @returns {Object} Object containing:
 *   - categorizedItems: Components organized by category
 *   - filteredItems: Search-filtered components
 *   - totalComponents: Total count of filtered components
 *   - categoryStats: Statistics about categories
 *   - validationResult: Component validation results
 *   - isFiltered: Boolean indicating if filtering is active
 */
export const useComponentFiltering = (sidebarItems, searchTerm) => {
  /**
   * Validate component structure
   *
   * Ensures all sidebar items have the required structure for
   * proper categorization and display. This validation runs
   * only when sidebarItems change.
   */
  const validationResult = useMemo(() => {
    if (!Array.isArray(sidebarItems)) {
      return {
        isValid: false,
        errors: ['sidebarItems must be an array'],
        warnings: [],
        validItems: [],
        invalidItems: sidebarItems ? [sidebarItems] : [],
      };
    }

    return validateComponentStructure(sidebarItems);
  }, [sidebarItems]);

  /**
   * Categorize components by type
   *
   * Groups valid components into categories based on their type.
   * This operation is memoized to prevent unnecessary recalculation
   * when only search terms change.
   *
   * Uses only valid items from validation to ensure data integrity.
   */
  const categorizedItems = useMemo(() => {
    // Use only valid items for categorization
    const validItems = validationResult.validItems || [];

    if (validItems.length === 0) {
      console.warn('No valid sidebar items found for categorization');
      return {};
    }

    try {
      return categorizeComponents(validItems);
    } catch (error) {
      console.error('Error categorizing components:', error);
      return {};
    }
  }, [validationResult.validItems]);

  /**
   * Apply search filtering
   *
   * Filters categorized components based on the search term.
   * This operation is memoized with both categorizedItems and
   * searchTerm as dependencies for optimal performance.
   *
   * When no search term is provided, returns all categorized items.
   */
  const filteredItems = useMemo(() => {
    try {
      return filterComponentsBySearch(categorizedItems, searchTerm);
    } catch (error) {
      console.error('Error filtering components:', error);
      return categorizedItems;
    }
  }, [categorizedItems, searchTerm]);

  /**
   * Calculate statistics for filtered results
   *
   * Computes various metrics about the filtered components,
   * including total counts, category distributions, and other
   * useful information for the UI.
   */
  const categoryStats = useMemo(() => {
    try {
      return calculateCategoryStats(filteredItems);
    } catch (error) {
      console.error('Error calculating category stats:', error);
      return {
        totalComponents: 0,
        categoryCounts: {},
        nonEmptyCategories: 0,
        largestCategory: null,
        largestCategorySize: 0,
      };
    }
  }, [filteredItems]);

  /**
   * Determine if filtering is currently active
   *
   * Filtering is considered active when there is a search term
   * or when the filtered results differ from the original categorized items.
   */
  const isFiltered = useMemo(() => {
    const hasSearchTerm = searchTerm && searchTerm.trim().length > 0;
    const hasFilteredResults =
      Object.keys(filteredItems).length !==
      Object.keys(categorizedItems).length;

    return hasSearchTerm || hasFilteredResults;
  }, [searchTerm, filteredItems, categorizedItems]);

  /**
   * Get total component count
   *
   * Provides the total number of components after filtering.
   * This is used for displaying counts in the UI.
   */
  const totalComponents = useMemo(() => {
    return categoryStats.totalComponents;
  }, [categoryStats.totalComponents]);

  /**
   * Get performance metrics
   *
   * Provides information about the filtering performance,
   * useful for monitoring and optimization.
   */
  const performanceMetrics = useMemo(() => {
    const originalCount = validationResult.validItems?.length || 0;
    const filteredCount = totalComponents;
    const filterRatio = originalCount > 0 ? filteredCount / originalCount : 0;

    return {
      originalCount,
      filteredCount,
      filterRatio,
      categoriesWithResults: Object.keys(filteredItems).length,
      averageComponentsPerCategory:
        Object.keys(filteredItems).length > 0
          ? filteredCount / Object.keys(filteredItems).length
          : 0,
    };
  }, [validationResult.validItems, totalComponents, filteredItems]);

  /**
   * Get empty state information
   *
   * Provides information about empty states for better UX.
   */
  const emptyStateInfo = useMemo(() => {
    const hasNoItems = totalComponents === 0;
    const hasNoValidItems = validationResult.validItems.length === 0;
    const hasNoSearchResults = isFiltered && totalComponents === 0;

    return {
      hasNoItems,
      hasNoValidItems,
      hasNoSearchResults,
      shouldShowEmptyState: hasNoItems,
      emptyStateType: hasNoValidItems
        ? 'no-data'
        : hasNoSearchResults
        ? 'no-results'
        : 'empty',
    };
  }, [totalComponents, validationResult.validItems.length, isFiltered]);

  return {
    // Core filtered data
    categorizedItems,
    filteredItems,
    totalComponents,

    // Statistics and metrics
    categoryStats,
    performanceMetrics,

    // Validation results
    validationResult,

    // State indicators
    isFiltered,
    emptyStateInfo,

    // Utility data
    hasValidItems: validationResult.validItems.length > 0,
    hasErrors: validationResult.errors.length > 0,
    hasWarnings: validationResult.warnings.length > 0,
  };
};
