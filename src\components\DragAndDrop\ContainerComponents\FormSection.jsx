import React, { useRef, memo, useMemo, useCallback, useState } from 'react';
import { useDrag } from 'react-dnd';
import { Collapse, Button, Modal, Input, Form, Switch, Typography } from 'antd';
import { EditOutlined, SettingOutlined } from '@ant-design/icons';
import { FORM_SECTION } from '../../../constants';
import DropZone from '../DropZone';
import Component from '../Component';
import { useDoubleClickHandler } from '../../FormBuilderApp/components/PropertiesPanel';

import styled from 'styled-components';

// Styled component for inline editable text
const InlineEditableText = styled.span`
  .editable-text {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: inline-block;
    min-width: 20px;

    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
      border: 1px solid rgba(24, 144, 255, 0.3);
    }

    &.placeholder-text {
      color: #999;
      font-style: italic;
    }
  }

  .editing-input {
    border: 1px solid #1890ff;
    border-radius: 3px;
    padding: 2px 4px;
    outline: none;
    background: white;
    box-shadow: 0 0 4px rgba(24, 144, 255, 0.3);
    min-width: 100px;
  }
`;

const { TextArea } = Input;
const { Text } = Typography;

// Memoized FormSection component for advanced nested drag-and-drop
const FormSection = memo(
  ({ data, components, handleDrop, path, onUpdateComponent }) => {
    const ref = useRef(null);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [isConditionsModalVisible, setIsConditionsModalVisible] =
      useState(false);
    const [form] = Form.useForm();
    const [conditionsForm] = Form.useForm();

    // Double-click handler for properties panel
    const { getContainerDoubleClickProps } = useDoubleClickHandler({
      componentData: data,
      componentId: data?.id,
      components,
    });

    // Inline editing state
    const [isEditing, setIsEditing] = useState(false);
    const [editingValue, setEditingValue] = useState('');
    const [originalValue, setOriginalValue] = useState('');
    const [editingProperty, setEditingProperty] = useState(null);

    // Memoized drag item
    const dragItem = useMemo(
      () => ({
        id: data?.id,
        type: FORM_SECTION,
        children: data?.children || [],
        path: path || `section-${data?.id}`, // Ensure path is always defined
      }),
      [data?.id, data?.children, path],
    );

    // Memoized component renderer
    const renderComponent = useCallback(
      (component, currentPath) => {
        return (
          <Component
            key={component.id}
            data={component}
            components={components}
            path={currentPath}
            handleDrop={handleDrop}
            onUpdateComponent={onUpdateComponent}
          />
        );
      },
      [components, handleDrop, onUpdateComponent],
    );

    const [{ isDragging }, drag] = useDrag({
      type: FORM_SECTION,
      item: dragItem,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    // Memoized style
    const containerStyle = useMemo(
      () => ({
        opacity: isDragging ? 0.5 : 1,
        margin: '8px 0',
        border: isDragging ? '2px dashed #1890ff' : '2px dashed #d9d9d9',
        borderRadius: '6px',
        background: '#fafafa',
      }),
      [isDragging],
    );

    // Section configuration - get from components registry for styling, layout for children
    const component = components[data.id] || {};

    // Memoize configuration objects to prevent unnecessary re-renders
    const sectionProps = useMemo(
      () => component.sectionProps || data.sectionProps || {},
      [component.sectionProps, data.sectionProps],
    );

    const styling = useMemo(
      () => component.styling || data.styling || {},
      [component.styling, data.styling],
    );

    const conditionalLogic = useMemo(
      () => component.conditionalLogic || data.conditionalLogic || {},
      [component.conditionalLogic, data.conditionalLogic],
    );

    const children = useMemo(() => data.children || [], [data.children]);

    // Handle section settings update
    const handleUpdateSettings = useCallback(() => {
      form.setFieldsValue({
        title: sectionProps.title || '',
        description: sectionProps.description || '',
        bordered: styling.bordered !== false,
        collapsible: styling.collapsible === true,
        defaultCollapsed: styling.defaultCollapsed === true,
      });
      setIsEditModalVisible(true);
    }, [sectionProps, styling, form]);

    const handleSaveSettings = useCallback(() => {
      form.validateFields().then((values) => {
        const updatedSectionProps = {
          ...sectionProps,
          title: values.title,
          description: values.description,
        };

        const updatedStyling = {
          ...styling,
          bordered: values.bordered,
          collapsible: values.collapsible,
          defaultCollapsed: values.defaultCollapsed,
        };

        onUpdateComponent?.(data.id, {
          sectionProps: updatedSectionProps,
          styling: updatedStyling,
        });

        setIsEditModalVisible(false);
        form.resetFields();
      });
    }, [form, data.id, sectionProps, styling, onUpdateComponent]);

    // Handle conditional logic
    const handleManageConditions = useCallback(() => {
      conditionsForm.setFieldsValue({
        enabled: conditionalLogic.enabled || false,
        conditions: conditionalLogic.conditions || [],
      });
      setIsConditionsModalVisible(true);
    }, [conditionalLogic, conditionsForm]);

    const handleSaveConditions = useCallback(() => {
      conditionsForm.validateFields().then((values) => {
        const updatedConditionalLogic = {
          ...conditionalLogic,
          enabled: values.enabled,
          conditions: values.conditions || [],
        };

        onUpdateComponent?.(data.id, {
          conditionalLogic: updatedConditionalLogic,
        });

        setIsConditionsModalVisible(false);
        conditionsForm.resetFields();
      });
    }, [conditionsForm, data.id, conditionalLogic, onUpdateComponent]);

    // Inline editing functions
    const handleLabelClick = useCallback((e, currentText, propertyName) => {
      e.stopPropagation();
      setIsEditing(true);
      setEditingValue(currentText || '');
      setOriginalValue(currentText || '');
      setEditingProperty(propertyName);
    }, []);

    const handleEditingSave = useCallback(() => {
      if (
        onUpdateComponent &&
        editingValue !== originalValue &&
        editingProperty
      ) {
        const updatedSectionProps = {
          ...sectionProps,
          [editingProperty]: editingValue,
        };
        onUpdateComponent(data.id, { sectionProps: updatedSectionProps });
      }
      setIsEditing(false);
      setEditingValue('');
      setOriginalValue('');
      setEditingProperty(null);
    }, [
      onUpdateComponent,
      data.id,
      editingValue,
      originalValue,
      editingProperty,
      sectionProps,
    ]);

    const handleEditingCancel = useCallback(() => {
      setIsEditing(false);
      setEditingValue('');
      setOriginalValue('');
      setEditingProperty(null);
    }, []);

    const handleEditingKeyDown = useCallback(
      (e) => {
        if (e.key === 'Enter') {
          handleEditingSave();
        } else if (e.key === 'Escape') {
          handleEditingCancel();
        }
      },
      [handleEditingSave, handleEditingCancel],
    );

    const handleEditingBlur = useCallback(() => {
      handleEditingSave();
    }, [handleEditingSave]);

    // Helper function to render editable text
    const renderEditableText = useCallback(
      (text, propertyName, placeholder = 'Click to edit') => {
        const displayText = text || placeholder;
        const isPlaceholder = !text;
        const isCurrentlyEditing =
          isEditing && editingProperty === propertyName;

        if (isCurrentlyEditing) {
          return (
            <input
              className='editing-input'
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              onKeyDown={handleEditingKeyDown}
              onBlur={handleEditingBlur}
              autoFocus
              style={{
                fontSize: 'inherit',
                fontFamily: 'inherit',
                fontWeight: 'inherit',
                color: 'inherit',
              }}
            />
          );
        }

        return (
          <InlineEditableText>
            <span
              className={`editable-text ${
                isPlaceholder ? 'placeholder-text' : ''
              }`}
              onClick={(e) => handleLabelClick(e, text, propertyName)}
              title='Click to edit'
            >
              {displayText}
            </span>
          </InlineEditableText>
        );
      },
      [
        isEditing,
        editingValue,
        editingProperty,
        handleEditingKeyDown,
        handleEditingBlur,
        handleLabelClick,
      ],
    );

    // Memoized section header
    const sectionHeader = useMemo(
      () => (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '12px 16px',
            background: '#fff',
            borderBottom: '1px solid #d9d9d9',
          }}
        >
          <div>
            <Text strong style={{ fontSize: '14px' }}>
              {renderEditableText(sectionProps.title, 'title', 'Form Section')}
            </Text>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              {renderEditableText(
                sectionProps.description,
                'description',
                'Section description',
              )}
            </div>
            {conditionalLogic.enabled && (
              <div
                style={{ fontSize: '11px', color: '#1890ff', marginTop: '2px' }}
              >
                ⚡ Conditional Logic Enabled
              </div>
            )}
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              type='text'
              size='small'
              icon={<EditOutlined />}
              onClick={handleUpdateSettings}
              title='Edit Section'
            />
            <Button
              type='text'
              size='small'
              icon={<SettingOutlined />}
              onClick={handleManageConditions}
              title='Conditional Logic'
              style={{
                color: conditionalLogic.enabled ? '#1890ff' : undefined,
              }}
            />
          </div>
        </div>
      ),
      [
        sectionProps,
        conditionalLogic,
        handleUpdateSettings,
        handleManageConditions,
        renderEditableText,
      ],
    );

    // Enhanced content renderer with improved drop zones and nested support
    const renderContent = useCallback(
      () => (
        <div style={{ padding: '16px', minHeight: '150px' }}>
          {/* Enhanced drop zone handling for better UX */}
          {children.length === 0 ? (
            // Empty state with prominent drop zone
            <DropZone
              data={{
                path: `${path}-0`,
                childrenCount: 0,
                containerId: data.id,
                containerType: 'section-content',
                index: 0,
              }}
              onDrop={handleDrop}
              className='empty-section-drop-zone'
              style={{
                minHeight: '150px',
                border: '2px dashed #d9d9d9',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)',
                transition: 'all 0.2s ease',
              }}
            >
              <div style={{ textAlign: 'center', color: '#999' }}>
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>📝</div>
                <div style={{ fontSize: '14px', fontWeight: 500 }}>
                  Drop components into this section
                </div>
                <div style={{ fontSize: '12px', marginTop: '4px' }}>
                  This section can contain form fields, containers, and other
                  sections
                </div>
              </div>
            </DropZone>
          ) : (
            // Render existing components with enhanced drop zones
            <div style={{ minHeight: '150px' }}>
              {/* Check if we need to render as rows or columns */}
              {children.some((child) => child.type === 'row') ? (
                // Vertical layout for rows
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '8px',
                  }}
                >
                  {children.map((component, index) => {
                    const currentPath = `${path}-${index}`;
                    const dropZoneData = {
                      path: currentPath,
                      childrenCount: children.length,
                      containerId: data.id,
                      containerType: 'section-content',
                      index: index,
                    };

                    return (
                      <React.Fragment key={component.id}>
                        {/* Vertical drop zone before each component */}
                        <DropZone
                          data={dropZoneData}
                          onDrop={handleDrop}
                          className='section-vertical-drop-zone'
                          style={{
                            minHeight: '8px',
                            margin: '4px 0',
                            borderRadius: '4px',
                          }}
                        />
                        {/* Component wrapper with enhanced styling */}
                        <div
                          style={{
                            border: '1px solid #f0f0f0',
                            borderRadius: '6px',
                            padding: '12px',
                            background: '#fafafa',
                            transition: 'all 0.2s ease',
                          }}
                        >
                          {renderComponent(component, currentPath)}
                        </div>
                      </React.Fragment>
                    );
                  })}
                  {/* Final vertical drop zone */}
                  <DropZone
                    data={{
                      path: `${path}-${children.length}`,
                      childrenCount: children.length,
                      containerId: data.id,
                      containerType: 'section-content',
                      index: children.length,
                    }}
                    onDrop={handleDrop}
                    className='section-vertical-drop-zone'
                    style={{
                      minHeight: '12px',
                      margin: '8px 0',
                      borderRadius: '4px',
                    }}
                    isLast
                  />
                </div>
              ) : (
                // Horizontal layout for columns
                <div
                  style={{
                    display: 'flex',
                    gap: '12px',
                    minHeight: '150px',
                    alignItems: 'stretch',
                  }}
                >
                  {children.map((component, index) => {
                    const currentPath = `${path}-${index}`;
                    const dropZoneData = {
                      path: currentPath,
                      childrenCount: children.length,
                      containerId: data.id,
                      containerType: 'section-content',
                      index: index,
                    };

                    return (
                      <React.Fragment key={component.id}>
                        {/* Horizontal drop zone for column creation */}
                        <DropZone
                          data={dropZoneData}
                          onDrop={handleDrop}
                          className='horizontalDrag'
                          style={{
                            minHeight: '150px',
                            width: '24px',
                            borderRadius: '6px',
                            background:
                              'linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%)',
                            border: '2px dashed transparent',
                            transition: 'all 0.2s ease',
                          }}
                        />
                        {/* Enhanced column content wrapper */}
                        <div
                          style={{
                            flex: 1,
                            minHeight: '150px',
                            border: '2px solid #f0f0f0',
                            borderRadius: '8px',
                            padding: '16px',
                            background: '#ffffff',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.02)',
                            transition: 'all 0.2s ease',
                            position: 'relative',
                          }}
                        >
                          {renderComponent(component, currentPath)}
                        </div>
                      </React.Fragment>
                    );
                  })}
                  {/* Final horizontal drop zone with enhanced styling */}
                  <DropZone
                    data={{
                      path: `${path}-${children.length}`,
                      childrenCount: children.length,
                      containerId: data.id,
                      containerType: 'section-content',
                      index: children.length,
                    }}
                    onDrop={handleDrop}
                    className='horizontalDrag'
                    style={{
                      minHeight: '150px',
                      width: '24px',
                      borderRadius: '6px',
                      background:
                        'linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%)',
                      border: '2px dashed transparent',
                      transition: 'all 0.2s ease',
                    }}
                    isLast
                  />
                </div>
              )}
            </div>
          )}
        </div>
      ),
      [children, data.id, path, handleDrop, renderComponent],
    );

    drag(ref);

    return (
      <div ref={ref} style={containerStyle} {...getContainerDoubleClickProps()}>
        {styling.collapsible ? (
          <Collapse
            defaultActiveKey={styling.defaultCollapsed ? [] : ['1']}
            bordered={styling.bordered !== false}
            items={[
              {
                key: '1',
                label: renderEditableText(
                  sectionProps.title,
                  'title',
                  'Form Section',
                ),
                children: renderContent(),
              },
            ]}
          />
        ) : (
          <div>
            {sectionHeader}
            {renderContent()}
          </div>
        )}

        {/* Settings Modal */}
        <Modal
          title='Section Settings'
          open={isEditModalVisible}
          onOk={handleSaveSettings}
          onCancel={() => {
            setIsEditModalVisible(false);
            form.resetFields();
          }}
          width={500}
        >
          <Form form={form} layout='vertical'>
            <Form.Item
              name='title'
              label='Section Title'
              rules={[
                { required: true, message: 'Please enter section title' },
              ]}
            >
              <Input placeholder='Enter section title' />
            </Form.Item>

            <Form.Item name='description' label='Description'>
              <TextArea
                placeholder='Enter section description (optional)'
                rows={3}
              />
            </Form.Item>

            <Form.Item name='bordered' valuePropName='checked' label='Bordered'>
              <Switch />
            </Form.Item>

            <Form.Item
              name='collapsible'
              valuePropName='checked'
              label='Collapsible'
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name='defaultCollapsed'
              valuePropName='checked'
              label='Default Collapsed'
            >
              <Switch />
            </Form.Item>
          </Form>
        </Modal>

        {/* Conditional Logic Modal */}
        <Modal
          title='Conditional Logic'
          open={isConditionsModalVisible}
          onOk={handleSaveConditions}
          onCancel={() => {
            setIsConditionsModalVisible(false);
            conditionsForm.resetFields();
          }}
          width={600}
        >
          <Form form={conditionsForm} layout='vertical'>
            <Form.Item
              name='enabled'
              valuePropName='checked'
              label='Enable Conditional Logic'
            >
              <Switch />
            </Form.Item>

            <div
              style={{
                marginTop: '16px',
                padding: '16px',
                background: '#f5f5f5',
                borderRadius: '4px',
              }}
            >
              <Text type='secondary' style={{ fontSize: '12px' }}>
                Conditional logic allows this section to show/hide based on
                other form field values. This feature will be fully implemented
                in the next iteration.
              </Text>
            </div>
          </Form>
        </Modal>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance - avoid JSON.stringify for deep comparison
    if (
      prevProps.data?.id !== nextProps.data?.id ||
      prevProps.path !== nextProps.path ||
      prevProps.handleDrop !== nextProps.handleDrop ||
      prevProps.onUpdateComponent !== nextProps.onUpdateComponent ||
      prevProps.components !== nextProps.components
    ) {
      return false;
    }

    // Compare children array length and IDs only (shallow comparison)
    const prevChildren = prevProps.data?.children || [];
    const nextChildren = nextProps.data?.children || [];

    if (prevChildren.length !== nextChildren.length) {
      return false;
    }

    // Compare children IDs and types only (avoid deep comparison)
    for (let i = 0; i < prevChildren.length; i++) {
      if (
        prevChildren[i]?.id !== nextChildren[i]?.id ||
        prevChildren[i]?.type !== nextChildren[i]?.type
      ) {
        return false;
      }
    }

    // Compare section props and styling (shallow comparison)
    const prevSectionProps = prevProps.data?.sectionProps || {};
    const nextSectionProps = nextProps.data?.sectionProps || {};
    const prevStyling = prevProps.data?.styling || {};
    const nextStyling = nextProps.data?.styling || {};

    if (
      prevSectionProps.title !== nextSectionProps.title ||
      prevSectionProps.description !== nextSectionProps.description ||
      prevStyling.bordered !== nextStyling.bordered ||
      prevStyling.collapsible !== nextStyling.collapsible ||
      prevStyling.defaultCollapsed !== nextStyling.defaultCollapsed
    ) {
      return false;
    }

    return true;
  },
);

FormSection.displayName = 'FormSection';

export default FormSection;
