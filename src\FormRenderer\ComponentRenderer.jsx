import React from 'react';
import { Form } from 'antd';
import {
  TAB_CONTAINER,
  CARD_CONTAINER,
  FORM_SECTION,
  ACCORDION_CONTAINER,
  STEPS_CONTAINER,
  GRID_CONTAINER,
  FLEX_CONTAINER,
} from '../constants';
import { useComponentProps } from './componentRenderers/hooks';
import {
  // Data Entry Renderers
  renderInput,
  renderEmail,
  renderTextarea,
  renderSelect,
  renderRadio,
  renderCheckbox,
  renderNumber,
  renderPassword,
  renderDatePicker,
  renderRangePicker,
  renderSwitch,
  renderRate,
  renderSlider,
  renderUpload,
  // Advanced Data Entry Renderers
  renderAutoComplete,
  renderCascader,
  renderColorPicker,
  renderMentions,
  renderTimePicker,
  renderTransfer,
  renderTreeSelect,
  // Display Renderers
  renderAvatar,
  renderBadge,
  renderImage,
  renderTag,
  renderButton,
  renderTypography,
  renderStatistic,
  // Data Display Renderers
  renderTable,
  renderList,
  renderCalendar,
  renderCarousel,
  renderDescriptions,
  renderEmpty,
  renderTimeline,
  renderTree,
  // Navigation Renderers
  renderBreadcrumb,
  renderMenu,
  renderPagination,
  renderSteps,
  // Layout Renderers
  renderDivider,
  renderSpace,
  // Feedback Renderers
  renderAlert,
  renderProgress,
  renderSkeleton,
  renderSpin,
  // Container Renderers
  renderTabContainer,
  renderCardContainer,
  renderFormSection,
  renderAccordionContainer,
  renderStepsContainer,
  renderGridContainer,
  renderFlexContainer,
} from './componentRenderers';

/**
 * ComponentRenderer Component
 *
 * Renders individual form components based on their type and configuration.
 * Uses extracted renderer functions and hooks for better maintainability.
 *
 * @param {Object} props - Component props
 * @param {Object} props.component - Component configuration object
 * @param {*} props.value - Current component value
 * @param {Function} props.onChange - Change handler function

 * @returns {JSX.Element} Rendered component wrapped in Form.Item
 */
const ComponentRenderer = ({ component, value, onChange }) => {
  // Use extracted hooks for component props (must be called before any early returns)
  const { commonProps, formItemProps } = useComponentProps(component);

  // Early return if no component provided
  if (!component) {
    return <div style={{ color: 'red' }}>Component not found</div>;
  }

  /**
   * Renders the appropriate component based on its type
   *
   * Uses the extracted renderer functions to handle different component types.
   * This approach makes the code more maintainable and easier to test.
   *
   * @returns {JSX.Element} Rendered component
   */
  const renderComponent = () => {
    const { type } = component;

    // Data Entry Components
    switch (type) {
      case 'input':
        return renderInput(component, commonProps);
      case 'email':
        return renderEmail(component, commonProps);
      case 'textarea':
        return renderTextarea(component, commonProps);
      case 'select':
        return renderSelect(component, commonProps);
      case 'radio':
        return renderRadio(component, commonProps);
      case 'checkbox':
        return renderCheckbox(component, commonProps, value, onChange);
      case 'inputnumber':
      case 'number':
        return renderNumber(component, commonProps);
      case 'password':
        return renderPassword(component, commonProps);
      case 'datepicker':
      case 'date':
        return renderDatePicker(component, commonProps);
      case 'rangepicker':
        return renderRangePicker(component, commonProps);
      case 'switch':
        return renderSwitch(component, commonProps);
      case 'rate':
        return renderRate(component, commonProps);
      case 'slider':
        return renderSlider(component, commonProps);
      case 'upload':
        return renderUpload(component, commonProps);

      // Advanced Data Entry Components
      case 'autocomplete':
        return renderAutoComplete(component, commonProps);
      case 'cascader':
        return renderCascader(component, commonProps);
      case 'colorpicker':
        return renderColorPicker(component, commonProps);
      case 'mentions':
        return renderMentions(component, commonProps);
      case 'timepicker':
        return renderTimePicker(component, commonProps);
      case 'transfer':
        return renderTransfer(component, commonProps);
      case 'treeselect':
        return renderTreeSelect(component, commonProps);

      // Display Components
      case 'avatar':
        return renderAvatar(component);
      case 'badge':
        return renderBadge(component);
      case 'image':
        return renderImage(component);
      case 'tag':
        return renderTag(component);
      case 'button':
        return renderButton(component);
      case 'typography':
        return renderTypography(component);
      case 'statistic':
        return renderStatistic(component);

      // Data Display Components
      case 'table':
        return renderTable(component);
      case 'list':
        return renderList(component);
      case 'calendar':
        return renderCalendar(component);
      case 'carousel':
        return renderCarousel(component);
      case 'descriptions':
        return renderDescriptions(component);
      case 'empty':
        return renderEmpty(component);
      case 'timeline':
        return renderTimeline(component);
      case 'tree':
        return renderTree(component);

      // Navigation Components
      case 'breadcrumb':
        return renderBreadcrumb(component);
      case 'menu':
        return renderMenu(component);
      case 'pagination':
        return renderPagination(component);
      case 'steps':
        return renderSteps(component);

      // Layout Components
      case 'divider':
        return renderDivider(component);
      case 'space':
        return renderSpace(component);

      // Feedback Components
      case 'alert':
        return renderAlert(component);
      case 'progress':
        return renderProgress(component);
      case 'skeleton':
        return renderSkeleton(component);
      case 'spin':
        return renderSpin(component);

      // Container Components
      case TAB_CONTAINER:
        return renderTabContainer(component);
      case CARD_CONTAINER:
        return renderCardContainer(component);
      case FORM_SECTION:
        return renderFormSection(component);
      case ACCORDION_CONTAINER:
        return renderAccordionContainer(component);
      case STEPS_CONTAINER:
        return renderStepsContainer(component);
      case GRID_CONTAINER:
        return renderGridContainer(component);
      case FLEX_CONTAINER:
        return renderFlexContainer(component);

      // Default case for unknown component types
      default:
        return (
          <div
            style={{
              padding: '8px',
              color: '#666',
              border: '1px dashed #d9d9d9',
              borderRadius: '4px',
            }}
          >
            Unknown component type: {component.type}
          </div>
        );
    }
  };

  return <Form.Item {...formItemProps}>{renderComponent()}</Form.Item>;
};

export default ComponentRenderer;
