/**
 * @fileoverview Enterprise-Grade Drag-and-Drop Handlers
 *
 * Unified drag-and-drop system with unlimited nesting support for all container types.
 * Replaces all legacy handlers with a single, powerful, capability-based system.
 *
 * Features:
 * - Unlimited nesting support for any container type
 * - Capability-based drop validation
 * - Enterprise-grade performance with intelligent caching
 * - Atomic operations with automatic rollback
 * - Comprehensive error handling and logging
 * - Full TypeScript support
 *
 * @module useDragAndDropHandlers
 * @version 2.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useCallback, useMemo } from 'react';
import { message } from 'antd';
import { unifiedDropHandler } from '../../../utils/UnifiedDropHandler';
import { dropZoneManager } from '../../../utils/UniversalDropZoneManager';
import { treeNavigator } from '../../../utils/AdvancedLayoutTreeNavigator';
import { handleAdvancedTrashDrop } from '../../../utils/advancedDragHandlers';
import { handleRemoveItemFromLayout } from '../../../utils/helpers';
import { SIDEBAR_ITEM, COMPONENT, COLUMN, ROW } from '../../../constants';

/**
 * Enterprise-Grade Drag-and-Drop Handlers Hook
 *
 * Provides unified drop handling for unlimited nesting with all container types.
 * Replaces all legacy path-based and container-specific handlers.
 */

/**
 * Custom hook for drag-and-drop functionality
 *
 * Provides comprehensive drag-and-drop handlers for the form builder,
 * including advanced container support, legacy fallback, and component updates.
 *
 * @param {Object} params - Hook parameters
 * @param {Array} params.layout - Current layout state
 * @param {Object} params.components - Current components state
 * @param {Function} params.setLayout - Layout state setter
 * @param {Function} params.setComponents - Components state setter
 * @param {Function} params.setComponentUpdateCounter - Component update counter setter
 *
 * @returns {Object} Drag-and-drop handlers
 * @returns {Function} returns.handleDrop - Main drop handler
 * @returns {Function} returns.handleDropToTrashBin - Trash drop handler
 * @returns {Function} returns.handleComponentUpdate - Component update handler
 *
 * @example
 * ```jsx
 * const {
 *   handleDrop,
 *   handleDropToTrashBin,
 *   handleComponentUpdate
 * } = useDragAndDropHandlers({
 *   layout,
 *   components,
 *   setLayout,
 *   setComponents,
 *   setComponentUpdateCounter
 * });
 *
 * // Use in drop zones
 * <DropZone onDrop={handleDrop} />
 * <TrashDropZone onDrop={handleDropToTrashBin} />
 * ```
 */
export const useDragAndDropHandlers = ({
  layout,
  components,
  setLayout,
  setComponents,
  setComponentUpdateCounter,
}) => {
  // Initialize unified system with current data
  const systemReady = useMemo(() => {
    if (layout && layout.length > 0) {
      treeNavigator.buildIndices(layout, components);
      return true;
    }
    return false;
  }, [layout, components]);
  /**
   * Enhanced trash drop handler with support for both legacy and advanced systems
   *
   * Handles dropping items into the trash bin with support for container
   * components and fallback to legacy system when needed.
   *
   * @param {*} _ - Unused parameter (drop zone data)
   * @param {Object} item - Item being dropped
   */
  const handleDropToTrashBin = useCallback(
    (_, item) => {
      console.log('🗑️ [useDragAndDropHandlers] handleDropToTrashBin called:', {
        item,
      });

      // Check if this is a container component or component inside a container
      const isContainerComponent =
        item.id &&
        (item.type === 'tabContainer' ||
          item.type === 'cardContainer' ||
          item.type === 'formSection' ||
          item.type === 'stepsContainer' ||
          !item.path);

      // Check if this item is inside a StepsContainer (path contains step structure)
      const isInsideStepsContainer = item.path && item.path.includes('-step-');

      if (isContainerComponent || isInsideStepsContainer) {
        console.log('🔄 [Trash] Using advanced handler for:', {
          itemType: item.type,
          itemId: item.id,
          isContainerComponent,
          isInsideStepsContainer,
          path: item.path,
        });

        // Use advanced trash handler for container components and items inside containers
        const success = handleAdvancedTrashDrop(
          item,
          layout,
          setLayout,
          setComponents,
        );

        if (success) {
          return;
        }

        // If advanced handler fails, fall back to legacy system
        console.warn(
          'Advanced trash handler failed, falling back to legacy system',
        );
      }

      // Legacy trash handler for path-based components
      if (item.path) {
        const splitItemPath = item.path.split('-');
        setLayout((currentLayout) =>
          handleRemoveItemFromLayout(currentLayout, splitItemPath),
        );

        // Also remove from components registry if it's a component
        if (
          item.type === COMPONENT ||
          item.type === 'tabContainer' ||
          item.type === 'cardContainer' ||
          item.type === 'formSection'
        ) {
          setComponents((currentComponents) => {
            const newComponents = { ...currentComponents };
            delete newComponents[item.id];
            return newComponents;
          });
        }
      }
    },
    [layout, setLayout, setComponents],
  );

  /**
   * Unified Drop Handler - Enterprise-Grade System
   *
   * Handles ALL drop operations with unlimited nesting support.
   * Replaces all legacy handlers with capability-based routing.
   *
   * @param {Object} dropZone - Drop zone information
   * @param {Object} item - Item being dropped
   */
  const handleDrop = useCallback(
    async (dropZone, item) => {
      if (!systemReady) {
        console.warn('⚠️ [UnifiedDrop] System not ready, skipping drop');
        return;
      }

      console.log('🎯 [UnifiedDrop] Processing drop with enterprise system:', {
        dropZone,
        item,
        systemReady,
      });

      try {
        // Use unified drop handler for all operations
        const success = await unifiedDropHandler.handleDrop(
          dropZone,
          item,
          layout,
          components,
          setLayout,
          setComponents,
        );

        if (success) {
          console.log('✅ [UnifiedDrop] Drop operation successful');
          message.success('Component added successfully');

          // Increment update counter to trigger re-renders
          setComponentUpdateCounter((prev) => prev + 1);
        } else {
          console.error('❌ [UnifiedDrop] Drop operation failed');
          message.error('Failed to add component');
        }
      } catch (error) {
        console.error('💥 [UnifiedDrop] Drop operation error:', error);
        message.error(`Drop failed: ${error.message}`);
      }
    },
    [
      layout,
      setLayout,
      components,
      setComponents,
      systemReady,
      setComponentUpdateCounter,
    ],
  );

  /**
   * Handle component updates for container components
   *
   * Updates component registry and layout structure for container components,
   * with automatic re-render triggering.
   *
   * @param {string} componentId - ID of component to update
   * @param {Object} updates - Updates to apply to component
   */
  const handleComponentUpdate = useCallback(
    (componentId, updates) => {
      // Update components registry
      setComponents((currentComponents) => ({
        ...currentComponents,
        [componentId]: {
          ...currentComponents[componentId],
          ...updates,
        },
      }));

      // Increment update counter to force FormRenderer re-render
      setComponentUpdateCounter((prev) => prev + 1);

      // For container components, also update the layout structure
      if (
        updates.tabs ||
        updates.children ||
        updates.cardProps ||
        updates.sectionProps
      ) {
        setLayout((currentLayout) => {
          const updateLayoutComponent = (items) => {
            return items.map((item) => {
              if (item.id === componentId) {
                return {
                  ...item,
                  ...updates,
                };
              }

              // Recursively update nested items
              if (item.children) {
                return {
                  ...item,
                  children: updateLayoutComponent(item.children),
                };
              }

              // Handle tab containers
              if (item.type === 'tabContainer' && item.tabs) {
                return {
                  ...item,
                  tabs: item.tabs.map((tab) => ({
                    ...tab,
                    children: tab.children
                      ? updateLayoutComponent(tab.children)
                      : [],
                  })),
                };
              }

              return item;
            });
          };

          return updateLayoutComponent(currentLayout);
        });
      }
    },
    [setComponents, setComponentUpdateCounter, setLayout],
  );

  return {
    handleDrop,
    handleDropToTrashBin,
    handleComponentUpdate,
  };
};
