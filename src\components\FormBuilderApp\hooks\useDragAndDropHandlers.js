/**
 * @fileoverview Custom hook for drag-and-drop functionality
 *
 * This hook encapsulates all drag-and-drop related handlers including
 * drop handling, trash drop handling, and component updates.
 *
 * @module useDragAndDropHandlers
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useCallback } from 'react';
import { message } from 'antd';
import shortid from 'shortid';
import {
  handleMoveWithinParent,
  handleMoveToDifferentParent,
  handleMoveSidebarComponentIntoParent,
  handleRemoveItemFromLayout,
} from '../../../utils';
import {
  handleAdvancedDrop,
  handleAdvancedTrashDrop,
} from '../../../utils/advancedDragHandlers';
import { SIDEBAR_ITEM, COMPONENT, COLUMN } from '../../../constants';

/**
 * Custom hook for drag-and-drop functionality
 *
 * Provides comprehensive drag-and-drop handlers for the form builder,
 * including advanced container support, legacy fallback, and component updates.
 *
 * @param {Object} params - Hook parameters
 * @param {Array} params.layout - Current layout state
 * @param {Object} params.components - Current components state
 * @param {Function} params.setLayout - Layout state setter
 * @param {Function} params.setComponents - Components state setter
 * @param {Function} params.setComponentUpdateCounter - Component update counter setter
 *
 * @returns {Object} Drag-and-drop handlers
 * @returns {Function} returns.handleDrop - Main drop handler
 * @returns {Function} returns.handleDropToTrashBin - Trash drop handler
 * @returns {Function} returns.handleComponentUpdate - Component update handler
 *
 * @example
 * ```jsx
 * const {
 *   handleDrop,
 *   handleDropToTrashBin,
 *   handleComponentUpdate
 * } = useDragAndDropHandlers({
 *   layout,
 *   components,
 *   setLayout,
 *   setComponents,
 *   setComponentUpdateCounter
 * });
 *
 * // Use in drop zones
 * <DropZone onDrop={handleDrop} />
 * <TrashDropZone onDrop={handleDropToTrashBin} />
 * ```
 */
export const useDragAndDropHandlers = ({
  layout,
  components,
  setLayout,
  setComponents,
  setComponentUpdateCounter,
}) => {
  /**
   * Enhanced trash drop handler with support for both legacy and advanced systems
   *
   * Handles dropping items into the trash bin with support for container
   * components and fallback to legacy system when needed.
   *
   * @param {*} _ - Unused parameter (drop zone data)
   * @param {Object} item - Item being dropped
   */
  const handleDropToTrashBin = useCallback(
    (_, item) => {
      console.log('🗑️ [useDragAndDropHandlers] handleDropToTrashBin called:', {
        item,
      });

      // Check if this is a container component or component inside a container
      const isContainerComponent =
        item.id &&
        (item.type === 'tabContainer' ||
          item.type === 'cardContainer' ||
          item.type === 'formSection' ||
          item.type === 'stepsContainer' ||
          !item.path);

      // Check if this item is inside a StepsContainer (path contains step structure)
      const isInsideStepsContainer = item.path && item.path.includes('-step-');

      if (isContainerComponent || isInsideStepsContainer) {
        console.log('🔄 [Trash] Using advanced handler for:', {
          itemType: item.type,
          itemId: item.id,
          isContainerComponent,
          isInsideStepsContainer,
          path: item.path,
        });

        // Use advanced trash handler for container components and items inside containers
        const success = handleAdvancedTrashDrop(
          item,
          layout,
          setLayout,
          setComponents,
        );

        if (success) {
          return;
        }

        // If advanced handler fails, fall back to legacy system
        console.warn(
          'Advanced trash handler failed, falling back to legacy system',
        );
      }

      // Legacy trash handler for path-based components
      if (item.path) {
        const splitItemPath = item.path.split('-');
        setLayout((currentLayout) =>
          handleRemoveItemFromLayout(currentLayout, splitItemPath),
        );

        // Also remove from components registry if it's a component
        if (
          item.type === COMPONENT ||
          item.type === 'tabContainer' ||
          item.type === 'cardContainer' ||
          item.type === 'formSection'
        ) {
          setComponents((currentComponents) => {
            const newComponents = { ...currentComponents };
            delete newComponents[item.id];
            return newComponents;
          });
        }
      }
    },
    [layout, setLayout, setComponents],
  );

  /**
   * Optimized drop handler with column count warnings
   *
   * Main drop handler supporting both advanced container drops and
   * legacy path-based drops with comprehensive validation and warnings.
   *
   * @param {Object} dropZone - Drop zone information
   * @param {Object} item - Item being dropped
   */
  const handleDrop = useCallback(
    (dropZone, item) => {
      console.log('🎯 [useDragAndDropHandlers] handleDrop called:', {
        dropZone,
        item,
      });

      // Check if this is a drop into a container component
      if (dropZone.containerId || dropZone.containerType) {
        // Use advanced drop handler for container components
        const success = handleAdvancedDrop(
          dropZone,
          item,
          layout,
          components,
          setLayout,
          setComponents,
        );

        if (success) {
          return;
        }

        // If advanced handler fails, fall back to legacy system
        console.warn(
          'Advanced drop handler failed, falling back to legacy system',
        );
      }

      const splitDropZonePath = dropZone.path.split('-');
      const pathToDropZone = splitDropZonePath.slice(0, -1).join('-');

      const newItem = { id: item.id, type: item.type };
      if (item.type === COLUMN) {
        newItem.children = item.children;
      }

      // Check for column count warning before processing drop
      if (item.type === COLUMN && item.path) {
        const splitItemPath = item.path.split('-');
        const dropZonePathRowIndex = splitDropZonePath[0];
        const itemPathRowIndex = splitItemPath[0];
        const diffRow = dropZonePathRowIndex !== itemPathRowIndex;

        // Show warning for cross-row column moves that create many columns
        if (
          diffRow &&
          splitDropZonePath.length === 2 &&
          dropZone.childrenCount >= 3
        ) {
          message.warning({
            content: (
              <div>
                <strong>⚠️ Layout Warning</strong>
                <br />
                This row now has {dropZone.childrenCount + 1} columns. Consider
                using fewer columns for better mobile responsiveness and user
                experience.
              </div>
            ),
            duration: 5,
            style: { marginTop: '20vh' },
          });
        }
      }

      // Handle sidebar item drops
      if (item.type === SIDEBAR_ITEM) {
        // Create new component from sidebar item
        const newComponent = {
          id: shortid.generate(),
          ...item.component,
        };

        // Determine the layout item type based on component type
        const layoutItemType = [
          'tabContainer',
          'cardContainer',
          'formSection',
          'accordionContainer',
          'stepsContainer',
          'gridContainer',
          'flexContainer',
        ].includes(newComponent.type)
          ? newComponent.type
          : COMPONENT;

        const newItem = {
          id: newComponent.id,
          type: layoutItemType,
        };

        // Add container-specific properties to layout item
        if (layoutItemType !== COMPONENT) {
          if (newComponent.type === 'tabContainer') {
            newItem.tabs = newComponent.tabs || [];
          } else if (newComponent.type === 'cardContainer') {
            newItem.children = [];
            newItem.cardProps = newComponent.cardProps || {};
          } else if (newComponent.type === 'formSection') {
            newItem.children = [];
            newItem.sectionProps = newComponent.sectionProps || {};
            newItem.conditionalLogic = newComponent.conditionalLogic || {};
          } else if (newComponent.type === 'accordionContainer') {
            newItem.children = [];
            newItem.panels = newComponent.panels || [];
            newItem.accordionProps = newComponent.accordionProps || {};
          } else if (newComponent.type === 'stepsContainer') {
            newItem.children = [];
            newItem.steps = newComponent.steps || [
              {
                id: `step_${Date.now()}_1`,
                key: `step_${Date.now()}_1`,
                title: 'Step 1',
                description: 'First step',
                children: [],
              },
              {
                id: `step_${Date.now()}_2`,
                key: `step_${Date.now()}_2`,
                title: 'Step 2',
                description: 'Second step',
                children: [],
              },
            ];
            newItem.stepsProps = newComponent.stepsProps || {
              current: 0,
              direction: 'horizontal',
              size: 'default',
            };
          } else if (newComponent.type === 'gridContainer') {
            newItem.children = [];
            newItem.gridProps = newComponent.gridProps || {};
          } else if (newComponent.type === 'flexContainer') {
            newItem.children = [];
            newItem.flexProps = newComponent.flexProps || {};
          }
        }

        // Use functional updates to avoid stale closure issues
        setComponents((currentComponents) => {
          const newComponents = {
            ...currentComponents,
            [newComponent.id]: newComponent,
          };
          console.log('🔄 [useDragAndDropHandlers] Updated components:', {
            oldCount: Object.keys(currentComponents).length,
            newCount: Object.keys(newComponents).length,
            newComponentId: newComponent.id,
          });
          return newComponents;
        });
        setLayout((currentLayout) => {
          const newLayout = handleMoveSidebarComponentIntoParent(
            currentLayout,
            splitDropZonePath,
            newItem,
          );
          console.log('🔄 [useDragAndDropHandlers] Updated layout:', {
            oldLength: currentLayout.length,
            newLength: newLayout.length,
          });
          return newLayout;
        });

        // Increment update counter to trigger re-renders
        setComponentUpdateCounter((prev) => {
          console.log(
            '🔄 [useDragAndDropHandlers] Incrementing componentUpdateCounter:',
            prev + 1,
          );
          return prev + 1;
        });
        return;
      }

      // Handle existing component moves
      const splitItemPath = item.path.split('-');
      const pathToItem = splitItemPath.slice(0, -1).join('-');

      // Move within parent
      if (splitItemPath.length === splitDropZonePath.length) {
        if (pathToItem === pathToDropZone) {
          setLayout((currentLayout) =>
            handleMoveWithinParent(
              currentLayout,
              splitDropZonePath,
              splitItemPath,
            ),
          );
          return;
        }

        // Move to different parent
        setLayout((currentLayout) =>
          handleMoveToDifferentParent(
            currentLayout,
            splitDropZonePath,
            splitItemPath,
            newItem,
          ),
        );
        return;
      }

      // Move + Create
      setLayout((currentLayout) =>
        handleMoveToDifferentParent(
          currentLayout,
          splitDropZonePath,
          splitItemPath,
          newItem,
        ),
      );
    },
    [layout, components, setLayout, setComponents],
  );

  /**
   * Handle component updates for container components
   *
   * Updates component registry and layout structure for container components,
   * with automatic re-render triggering.
   *
   * @param {string} componentId - ID of component to update
   * @param {Object} updates - Updates to apply to component
   */
  const handleComponentUpdate = useCallback(
    (componentId, updates) => {
      // Update components registry
      setComponents((currentComponents) => ({
        ...currentComponents,
        [componentId]: {
          ...currentComponents[componentId],
          ...updates,
        },
      }));

      // Increment update counter to force FormRenderer re-render
      setComponentUpdateCounter((prev) => prev + 1);

      // For container components, also update the layout structure
      if (
        updates.tabs ||
        updates.children ||
        updates.cardProps ||
        updates.sectionProps
      ) {
        setLayout((currentLayout) => {
          const updateLayoutComponent = (items) => {
            return items.map((item) => {
              if (item.id === componentId) {
                return {
                  ...item,
                  ...updates,
                };
              }

              // Recursively update nested items
              if (item.children) {
                return {
                  ...item,
                  children: updateLayoutComponent(item.children),
                };
              }

              // Handle tab containers
              if (item.type === 'tabContainer' && item.tabs) {
                return {
                  ...item,
                  tabs: item.tabs.map((tab) => ({
                    ...tab,
                    children: tab.children
                      ? updateLayoutComponent(tab.children)
                      : [],
                  })),
                };
              }

              return item;
            });
          };

          return updateLayoutComponent(currentLayout);
        });
      }
    },
    [setComponents, setComponentUpdateCounter, setLayout],
  );

  return {
    handleDrop,
    handleDropToTrashBin,
    handleComponentUpdate,
  };
};
