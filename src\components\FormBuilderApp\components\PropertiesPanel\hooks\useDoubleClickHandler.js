/**
 * @fileoverview Custom hook for double-click event handling
 *
 * This hook provides double-click event handling for form builder components
 * without interfering with existing drag-and-drop functionality.
 *
 * @module useDoubleClickHandler
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useCallback, useRef } from 'react';
import { usePropertiesPanelContext } from '../context/PropertiesPanelContext';

/**
 * Custom hook for double-click event handling
 *
 * Provides double-click event handling that works alongside existing
 * drag-and-drop functionality without interference.
 *
 * @param {Object} params - Hook parameters
 * @param {Object} params.componentData - Component data
 * @param {string} params.componentId - Component ID
 * @param {Object} params.components - Components registry (for lookups)
 *
 * @returns {Object} Double-click event handlers
 * @returns {Function} returns.handleDoubleClick - Double-click event handler
 * @returns {Function} returns.getDoubleClickProps - Props to spread on component
 *
 * @example
 * ```jsx
 * const { handleDoubleClick, getDoubleClickProps } = useDoubleClickHandler({
 *   componentData,
 *   componentId,
 *   components
 * });
 * 
 * return (
 *   <div {...getDoubleClickProps()}>
 *     Component content
 *   </div>
 * );
 * ```
 */
export const useDoubleClickHandler = ({ 
  componentData, 
  componentId, 
  components 
}) => {
  const { openPanel } = usePropertiesPanelContext();
  
  // Refs for tracking click timing
  const clickTimeoutRef = useRef(null);
  const clickCountRef = useRef(0);
  const lastClickTimeRef = useRef(0);

  /**
   * Handles double-click events with proper timing and interference prevention
   *
   * @param {Event} event - Mouse event
   */
  const handleDoubleClick = useCallback(
    (event) => {
      // Prevent event bubbling to avoid triggering parent handlers
      event.stopPropagation();
      
      // Don't interfere with drag operations
      if (event.defaultPrevented) {
        return;
      }

      const currentTime = Date.now();
      const timeDiff = currentTime - lastClickTimeRef.current;
      
      // Reset click count if too much time has passed
      if (timeDiff > 500) {
        clickCountRef.current = 0;
      }
      
      clickCountRef.current += 1;
      lastClickTimeRef.current = currentTime;

      // Clear any existing timeout
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
        clickTimeoutRef.current = null;
      }

      // Handle double-click
      if (clickCountRef.current === 2) {
        clickCountRef.current = 0;
        
        console.log('🖱️ [useDoubleClickHandler] Double-click detected:', {
          componentId,
          componentType: componentData?.type,
          target: event.currentTarget?.tagName,
        });

        // Get the actual component data from components registry if needed
        const actualComponentData = components?.[componentId] || componentData;
        
        if (!actualComponentData) {
          console.warn('Component data not found for double-click handler:', componentId);
          return;
        }

        // Open the properties panel
        openPanel(event.currentTarget, actualComponentData, componentId);
        
      } else {
        // Set timeout to reset click count
        clickTimeoutRef.current = setTimeout(() => {
          clickCountRef.current = 0;
          clickTimeoutRef.current = null;
        }, 500);
      }
    },
    [componentData, componentId, components, openPanel],
  );

  /**
   * Alternative double-click handler using native onDoubleClick event
   * This is more reliable but may interfere with some drag operations
   *
   * @param {Event} event - Double-click event
   */
  const handleNativeDoubleClick = useCallback(
    (event) => {
      // Prevent event bubbling
      event.stopPropagation();
      
      // Don't interfere with drag operations
      if (event.defaultPrevented) {
        return;
      }

      console.log('🖱️ [useDoubleClickHandler] Native double-click detected:', {
        componentId,
        componentType: componentData?.type,
        target: event.currentTarget?.tagName,
      });

      // Get the actual component data from components registry if needed
      const actualComponentData = components?.[componentId] || componentData;
      
      if (!actualComponentData) {
        console.warn('Component data not found for double-click handler:', componentId);
        return;
      }

      // Open the properties panel
      openPanel(event.currentTarget, actualComponentData, componentId);
    },
    [componentData, componentId, components, openPanel],
  );

  /**
   * Gets props to spread on the component for double-click handling
   *
   * @param {Object} options - Configuration options
   * @param {boolean} options.useNativeEvent - Whether to use native onDoubleClick
   * @returns {Object} Props to spread on component
   */
  const getDoubleClickProps = useCallback(
    (options = {}) => {
      const { useNativeEvent = false } = options;

      if (useNativeEvent) {
        return {
          onDoubleClick: handleNativeDoubleClick,
          style: { cursor: 'pointer' },
        };
      }

      return {
        onClick: handleDoubleClick,
        style: { cursor: 'pointer' },
      };
    },
    [handleDoubleClick, handleNativeDoubleClick],
  );

  /**
   * Gets props specifically for container components
   * Containers may need different handling due to their nested structure
   *
   * @returns {Object} Props for container components
   */
  const getContainerDoubleClickProps = useCallback(() => {
    return {
      onDoubleClick: (event) => {
        // Only trigger if the click is directly on the container, not its children
        if (event.target === event.currentTarget) {
          handleNativeDoubleClick(event);
        }
      },
      style: { cursor: 'pointer' },
    };
  }, [handleNativeDoubleClick]);

  /**
   * Cleanup function to clear any pending timeouts
   */
  const cleanup = useCallback(() => {
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
      clickTimeoutRef.current = null;
    }
    clickCountRef.current = 0;
  }, []);

  return {
    // Event handlers
    handleDoubleClick,
    handleNativeDoubleClick,
    
    // Prop getters
    getDoubleClickProps,
    getContainerDoubleClickProps,
    
    // Utilities
    cleanup,
  };
};
