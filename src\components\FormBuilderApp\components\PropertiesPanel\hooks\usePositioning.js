/**
 * @fileoverview Custom hook for Properties Panel intelligent positioning
 *
 * This hook handles intelligent positioning of the properties panel with
 * collision detection, responsive behavior, and visual caret positioning.
 *
 * @module usePositioning
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  POSITIONING_PRIORITY,
  PANEL_DIMENSIONS,
  PANEL_DEFAULTS,
  RESPONSIVE_BREAKPOINTS,
} from '../constants/propertiesPanelConstants';

/**
 * Custom hook for intelligent panel positioning
 *
 * Provides intelligent positioning logic with collision detection,
 * responsive behavior, and visual caret positioning.
 *
 * @param {Object} params - Hook parameters
 * @param {HTMLElement} params.targetElement - Element that triggered the panel
 * @param {boolean} params.visible - Whether the panel is visible
 * @param {boolean} params.isMobile - Mobile detection state
 *
 * @returns {Object} Positioning state and styles
 * @returns {string} returns.position - Current panel position (top/right/bottom/left)
 * @returns {Object} returns.panelStyle - CSS styles for panel positioning
 * @returns {string} returns.caretPosition - Position for visual caret
 *
 * @example
 * ```jsx
 * const { position, panelStyle, caretPosition } = usePositioning({
 *   targetElement,
 *   visible,
 *   isMobile
 * });
 * ```
 */
export const usePositioning = ({ targetElement, visible, isMobile }) => {
  const [position, setPosition] = useState(PANEL_DEFAULTS.POSITION);
  const [panelStyle, setPanelStyle] = useState({});

  /**
   * Calculates optimal position based on available space
   *
   * @param {HTMLElement} target - Target element
   * @param {Object} panelDimensions - Panel width and height
   * @returns {Object} Position calculation result
   */
  const calculateOptimalPosition = useCallback((target, panelDimensions) => {
    if (!target) return { position: 'top', style: {} };

    const targetRect = target.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    const { width: panelWidth, height: panelHeight } = panelDimensions;
    const offset = PANEL_DIMENSIONS.OFFSET;
    const collisionPadding = PANEL_DEFAULTS.COLLISION_PADDING;

    // Calculate available space in each direction
    const spaces = {
      top: targetRect.top - collisionPadding,
      right: viewport.width - targetRect.right - collisionPadding,
      bottom: viewport.height - targetRect.bottom - collisionPadding,
      left: targetRect.left - collisionPadding,
    };

    // Find the best position based on priority and available space
    let bestPosition = 'top';
    let bestStyle = {};

    for (const pos of POSITIONING_PRIORITY) {
      let canFit = false;
      let style = {};

      switch (pos) {
        case 'top':
          canFit = spaces.top >= panelHeight;
          if (canFit) {
            style = {
              top: targetRect.top - panelHeight - offset,
              left: Math.max(
                collisionPadding,
                Math.min(
                  targetRect.left + targetRect.width / 2 - panelWidth / 2,
                  viewport.width - panelWidth - collisionPadding,
                ),
              ),
            };
          }
          break;

        case 'right':
          canFit = spaces.right >= panelWidth;
          if (canFit) {
            style = {
              top: Math.max(
                collisionPadding,
                Math.min(
                  targetRect.top + targetRect.height / 2 - panelHeight / 2,
                  viewport.height - panelHeight - collisionPadding,
                ),
              ),
              left: targetRect.right + offset,
            };
          }
          break;

        case 'bottom':
          canFit = spaces.bottom >= panelHeight;
          if (canFit) {
            style = {
              top: targetRect.bottom + offset,
              left: Math.max(
                collisionPadding,
                Math.min(
                  targetRect.left + targetRect.width / 2 - panelWidth / 2,
                  viewport.width - panelWidth - collisionPadding,
                ),
              ),
            };
          }
          break;

        case 'left':
          canFit = spaces.left >= panelWidth;
          if (canFit) {
            style = {
              top: Math.max(
                collisionPadding,
                Math.min(
                  targetRect.top + targetRect.height / 2 - panelHeight / 2,
                  viewport.height - panelHeight - collisionPadding,
                ),
              ),
              left: targetRect.left - panelWidth - offset,
            };
          }
          break;
      }

      if (canFit) {
        bestPosition = pos;
        bestStyle = style;
        break;
      }
    }

    // Fallback: center the panel if no position works
    if (Object.keys(bestStyle).length === 0) {
      bestPosition = 'center';
      bestStyle = {
        top: Math.max(collisionPadding, (viewport.height - panelHeight) / 2),
        left: Math.max(collisionPadding, (viewport.width - panelWidth) / 2),
      };
    }

    return {
      position: bestPosition,
      style: bestStyle,
    };
  }, []);

  /**
   * Updates panel position when target element or visibility changes
   */
  const updatePosition = useCallback(() => {
    if (!visible || !targetElement || isMobile) {
      // Mobile uses fixed positioning
      if (isMobile) {
        setPosition('bottom');
        setPanelStyle({});
      }
      return;
    }

    // Check if target element is still in the DOM
    if (!document.contains(targetElement)) {
      console.warn('Target element is no longer in the DOM');
      return;
    }

    const panelDimensions = {
      width:
        window.innerWidth <= RESPONSIVE_BREAKPOINTS.TABLET
          ? PANEL_DIMENSIONS.WIDTH.TABLET
          : PANEL_DIMENSIONS.WIDTH.DESKTOP,
      height:
        window.innerWidth <= RESPONSIVE_BREAKPOINTS.TABLET
          ? PANEL_DIMENSIONS.HEIGHT.MAX_TABLET
          : PANEL_DIMENSIONS.HEIGHT.MAX_DESKTOP,
    };

    const result = calculateOptimalPosition(targetElement, panelDimensions);

    // Add smooth transition for position changes
    const newStyle = {
      ...result.style,
      transition: 'all 0.2s ease-out',
    };

    setPosition(result.position);
    setPanelStyle(newStyle);

    console.log('📍 [usePositioning] Position updated:', {
      position: result.position,
      style: newStyle,
      targetElement: targetElement?.tagName,
      targetRect: targetElement?.getBoundingClientRect(),
    });
  }, [visible, targetElement, isMobile, calculateOptimalPosition]);

  // Update position when dependencies change
  useEffect(() => {
    updatePosition();
  }, [updatePosition]);

  // Handle window resize
  useEffect(() => {
    if (!visible) return;

    const handleResize = () => {
      // Debounce resize events
      setTimeout(updatePosition, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [visible, updatePosition]);

  // Handle scroll events to reposition panel
  useEffect(() => {
    if (!visible || isMobile) return;

    const handleScroll = () => {
      // Debounce scroll events
      setTimeout(updatePosition, 50);
    };

    // Listen to scroll events on all scrollable containers
    document.addEventListener('scroll', handleScroll, true);
    return () => document.removeEventListener('scroll', handleScroll, true);
  }, [visible, isMobile, updatePosition]);

  // Memoized caret position with offset calculation
  const caretPosition = useMemo(() => {
    // Caret position is opposite to panel position
    const caretMap = {
      top: 'bottom',
      right: 'left',
      bottom: 'top',
      left: 'right',
      center: 'top', // Default for center positioning
    };

    const calculatedPosition = caretMap[position] || 'top';

    console.log('🎯 [usePositioning] Caret position calculation:', {
      panelPosition: position,
      caretPosition: calculatedPosition,
      caretMap,
      targetElement: targetElement?.tagName,
    });

    return calculatedPosition;
  }, [position, targetElement]);

  // Calculate caret offset for better alignment with target element
  const caretOffset = useMemo(() => {
    if (!targetElement || isMobile || position === 'center') {
      console.log('🎯 [usePositioning] Caret offset skipped:', {
        hasTarget: !!targetElement,
        isMobile,
        position,
      });
      return 0;
    }

    try {
      const targetRect = targetElement.getBoundingClientRect();
      const panelRect = panelStyle;

      console.log('🎯 [usePositioning] Caret offset calculation:', {
        targetRect: {
          left: targetRect.left,
          top: targetRect.top,
          width: targetRect.width,
          height: targetRect.height,
          centerX: targetRect.left + targetRect.width / 2,
          centerY: targetRect.top + targetRect.height / 2,
        },
        panelRect: {
          left: panelRect.left,
          top: panelRect.top,
          width: panelRect.width,
          height: panelRect.height,
        },
        position,
      });

      if (!panelRect.left && !panelRect.top) {
        console.log('🎯 [usePositioning] Panel not positioned yet');
        return 0; // Panel not positioned yet
      }

      let calculatedOffset = 0;

      switch (position) {
        case 'top':
        case 'bottom':
          // Calculate horizontal offset for caret
          const targetCenterX = targetRect.left + targetRect.width / 2;
          const panelLeft = panelRect.left || 0;
          const caretX = targetCenterX - panelLeft;
          calculatedOffset = Math.max(
            30, // Increased minimum offset for better visibility
            Math.min(
              caretX,
              (panelStyle.width || PANEL_DIMENSIONS.WIDTH.DESKTOP) - 30,
            ),
          );
          break;

        case 'left':
        case 'right':
          // Calculate vertical offset for caret
          const targetCenterY = targetRect.top + targetRect.height / 2;
          const panelTop = panelRect.top || 0;
          const caretY = targetCenterY - panelTop;
          calculatedOffset = Math.max(
            30, // Increased minimum offset for better visibility
            Math.min(
              caretY,
              (panelStyle.height || PANEL_DIMENSIONS.HEIGHT.MAX_DESKTOP) - 30,
            ),
          );
          break;

        default:
          calculatedOffset = 0;
      }

      console.log('🎯 [usePositioning] Final caret offset:', {
        position,
        calculatedOffset,
        targetElement: targetElement?.tagName,
      });

      return calculatedOffset;
    } catch (error) {
      console.error(
        '❌ [usePositioning] Error calculating caret offset:',
        error,
      );
      return 0;
    }
  }, [targetElement, panelStyle, position, isMobile]);

  return {
    position,
    panelStyle,
    caretPosition,
    caretOffset,
    updatePosition,
  };
};
