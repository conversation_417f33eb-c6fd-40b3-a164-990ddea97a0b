/**
 * @fileoverview Performance optimization hook
 *
 * This hook provides performance optimization utilities including
 * memoization helpers, lazy loading, and performance monitoring.
 *
 * @module usePerformanceOptimization
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import { debounce, throttle } from 'lodash-es';

/**
 * Performance optimization hook
 *
 * @param {Object} options - Optimization options
 * @param {boolean} options.enableProfiling - Enable performance profiling
 * @param {number} options.debounceDelay - Debounce delay in milliseconds
 * @param {number} options.throttleDelay - Throttle delay in milliseconds
 * @returns {Object} Performance optimization utilities
 */
export const usePerformanceOptimization = ({
  enableProfiling = false,
  debounceDelay = 300,
  throttleDelay = 100,
} = {}) => {
  const [performanceMetrics, setPerformanceMetrics] = useState({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    memoryUsage: 0,
  });

  const renderCountRef = useRef(0);
  const renderTimesRef = useRef([]);
  const startTimeRef = useRef(0);

  /**
   * Start performance measurement
   */
  const startMeasurement = useCallback((label = 'render') => {
    if (enableProfiling) {
      startTimeRef.current = performance.now();
      performance.mark(`${label}-start`);
    }
  }, [enableProfiling]);

  /**
   * End performance measurement
   */
  const endMeasurement = useCallback((label = 'render') => {
    if (enableProfiling) {
      const endTime = performance.now();
      const renderTime = endTime - startTimeRef.current;
      
      performance.mark(`${label}-end`);
      performance.measure(label, `${label}-start`, `${label}-end`);
      
      renderCountRef.current += 1;
      renderTimesRef.current.push(renderTime);
      
      // Keep only last 100 measurements
      if (renderTimesRef.current.length > 100) {
        renderTimesRef.current.shift();
      }
      
      const averageRenderTime = renderTimesRef.current.reduce((a, b) => a + b, 0) / renderTimesRef.current.length;
      
      setPerformanceMetrics(prev => ({
        ...prev,
        renderCount: renderCountRef.current,
        lastRenderTime: renderTime,
        averageRenderTime,
      }));
      
      // Log slow renders
      if (renderTime > 16) {
        console.warn(`Slow render detected: ${renderTime.toFixed(2)}ms for ${label}`);
      }
    }
  }, [enableProfiling]);

  /**
   * Memoized debounce function
   */
  const createDebouncedCallback = useCallback((callback, delay = debounceDelay) => {
    return debounce(callback, delay);
  }, [debounceDelay]);

  /**
   * Memoized throttle function
   */
  const createThrottledCallback = useCallback((callback, delay = throttleDelay) => {
    return throttle(callback, delay);
  }, [throttleDelay]);

  /**
   * Optimized component comparison function
   */
  const createMemoComparison = useCallback((keys = []) => {
    return (prevProps, nextProps) => {
      if (keys.length === 0) {
        return Object.keys(prevProps).every(key => 
          prevProps[key] === nextProps[key]
        );
      }
      
      return keys.every(key => prevProps[key] === nextProps[key]);
    };
  }, []);

  /**
   * Deep comparison for complex objects
   */
  const deepEqual = useCallback((obj1, obj2) => {
    if (obj1 === obj2) return true;
    
    if (obj1 == null || obj2 == null) return false;
    
    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return obj1 === obj2;
    }
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    return keys1.every(key => deepEqual(obj1[key], obj2[key]));
  }, []);

  /**
   * Stable object reference with deep comparison
   */
  const useStableObject = useCallback((obj) => {
    const ref = useRef(obj);
    
    if (!deepEqual(ref.current, obj)) {
      ref.current = obj;
    }
    
    return ref.current;
  }, [deepEqual]);

  /**
   * Optimized array comparison
   */
  const useStableArray = useCallback((arr) => {
    const ref = useRef(arr);
    
    if (arr.length !== ref.current.length || 
        !arr.every((item, index) => item === ref.current[index])) {
      ref.current = arr;
    }
    
    return ref.current;
  }, []);

  /**
   * Memory usage monitoring
   */
  const measureMemoryUsage = useCallback(() => {
    if (performance.memory) {
      const memoryInfo = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
      };
      
      setPerformanceMetrics(prev => ({
        ...prev,
        memoryUsage: memoryInfo.used / 1024 / 1024, // Convert to MB
      }));
      
      return memoryInfo;
    }
    return null;
  }, []);

  /**
   * Lazy component loader with error boundary
   */
  const createLazyComponent = useCallback((importFn, fallback = null) => {
    const LazyComponent = React.lazy(importFn);
    
    return React.forwardRef((props, ref) => (
      <React.Suspense fallback={fallback}>
        <LazyComponent {...props} ref={ref} />
      </React.Suspense>
    ));
  }, []);

  /**
   * Intersection observer for lazy loading
   */
  const useIntersectionObserver = useCallback((options = {}) => {
    const [isIntersecting, setIsIntersecting] = useState(false);
    const [hasIntersected, setHasIntersected] = useState(false);
    const elementRef = useRef(null);
    
    useEffect(() => {
      const element = elementRef.current;
      if (!element) return;
      
      const observer = new IntersectionObserver(
        ([entry]) => {
          setIsIntersecting(entry.isIntersecting);
          if (entry.isIntersecting && !hasIntersected) {
            setHasIntersected(true);
          }
        },
        {
          threshold: 0.1,
          rootMargin: '50px',
          ...options,
        }
      );
      
      observer.observe(element);
      
      return () => observer.disconnect();
    }, [hasIntersected, options]);
    
    return { elementRef, isIntersecting, hasIntersected };
  }, []);

  /**
   * Virtual scrolling helper
   */
  const useVirtualScrolling = useCallback((items, itemHeight, containerHeight) => {
    const [scrollTop, setScrollTop] = useState(0);
    
    const visibleItems = useMemo(() => {
      const startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(
        startIndex + Math.ceil(containerHeight / itemHeight) + 1,
        items.length
      );
      
      return {
        startIndex,
        endIndex,
        items: items.slice(startIndex, endIndex),
        totalHeight: items.length * itemHeight,
        offsetY: startIndex * itemHeight,
      };
    }, [items, itemHeight, containerHeight, scrollTop]);
    
    const handleScroll = useCallback((e) => {
      setScrollTop(e.target.scrollTop);
    }, []);
    
    return { visibleItems, handleScroll };
  }, []);

  /**
   * Bundle size analyzer
   */
  const analyzeBundleSize = useCallback(() => {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
    
    const bundleInfo = {
      scripts: scripts.map(script => ({
        src: script.src,
        size: script.getAttribute('data-size') || 'unknown',
      })),
      styles: styles.map(style => ({
        href: style.href,
        size: style.getAttribute('data-size') || 'unknown',
      })),
    };
    
    return bundleInfo;
  }, []);

  /**
   * Performance report generator
   */
  const generatePerformanceReport = useCallback(() => {
    const report = {
      metrics: performanceMetrics,
      memory: measureMemoryUsage(),
      bundle: analyzeBundleSize(),
      timing: performance.getEntriesByType('navigation')[0],
      marks: performance.getEntriesByType('mark'),
      measures: performance.getEntriesByType('measure'),
      timestamp: new Date().toISOString(),
    };
    
    return report;
  }, [performanceMetrics, measureMemoryUsage, analyzeBundleSize]);

  // Monitor memory usage periodically
  useEffect(() => {
    if (enableProfiling) {
      const interval = setInterval(measureMemoryUsage, 5000);
      return () => clearInterval(interval);
    }
  }, [enableProfiling, measureMemoryUsage]);

  return {
    // Measurement utilities
    startMeasurement,
    endMeasurement,
    measureMemoryUsage,
    
    // Optimization utilities
    createDebouncedCallback,
    createThrottledCallback,
    createMemoComparison,
    useStableObject,
    useStableArray,
    
    // Lazy loading utilities
    createLazyComponent,
    useIntersectionObserver,
    useVirtualScrolling,
    
    // Analysis utilities
    analyzeBundleSize,
    generatePerformanceReport,
    
    // Performance metrics
    performanceMetrics,
    
    // Comparison utilities
    deepEqual,
  };
};

export default usePerformanceOptimization;
