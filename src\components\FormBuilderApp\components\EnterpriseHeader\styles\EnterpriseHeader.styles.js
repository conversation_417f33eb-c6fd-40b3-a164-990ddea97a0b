/**
 * @fileoverview Enterprise Header styled components
 *
 * This module contains all styled components for the enterprise header/toolbar
 * system with modern design patterns and responsive layouts.
 *
 * @module EnterpriseHeader.styles
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import styled from 'styled-components';
import { motion } from 'framer-motion';
import {
  colors,
  spacing,
  borderRadius,
  elevation,
} from '../../../../../styles/theme';

/**
 * Main header container with enterprise styling
 */
export const HeaderContainer = styled(motion.header)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${spacing.md} ${spacing.lg};
  background: linear-gradient(
    135deg,
    ${colors.background} 0%,
    ${colors.backgroundSecondary} 100%
  );
  border-bottom: 2px solid ${colors.border};
  box-shadow: ${elevation.sm};
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 100;
  min-height: 72px;

  /* Glass morphism effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    z-index: -1;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    padding: ${spacing.sm} ${spacing.md};
    min-height: 64px;
    flex-wrap: wrap;
    gap: ${spacing.sm};
  }

  @media (max-width: 480px) {
    padding: ${spacing.sm};
    min-height: 56px;
  }
`;

/**
 * Left section containing project info and breadcrumbs
 */
export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.lg};
  flex: 1;
  min-width: 0;

  @media (max-width: 768px) {
    gap: ${spacing.md};
    flex-basis: 100%;
    order: 1;
  }
`;

/**
 * Center section containing form metrics
 */
export const HeaderCenter = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  flex: 0 0 auto;

  @media (max-width: 1024px) {
    display: none;
  }
`;

/**
 * Right section containing actions and collaboration tools
 */
export const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  flex: 0 0 auto;

  @media (max-width: 768px) {
    flex-basis: 100%;
    order: 3;
    justify-content: space-between;
  }
`;

/**
 * Project title and metadata section
 */
export const ProjectSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.xs};
  min-width: 0;
  flex: 1;

  @media (max-width: 480px) {
    gap: 2px;
  }
`;

/**
 * Project title with inline editing capability
 */
export const ProjectTitle = styled(motion.h1)`
  font-size: 18px;
  font-weight: 600;
  color: ${colors.textPrimary};
  margin: 0;
  line-height: 1.2;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: ${borderRadius.sm};
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;

  &:hover {
    background: ${colors.backgroundTertiary};
    color: ${colors.primary};
  }

  &:focus {
    outline: 2px solid ${colors.primary};
    outline-offset: 2px;
  }

  @media (max-width: 768px) {
    font-size: 16px;
    max-width: 200px;
  }

  @media (max-width: 480px) {
    font-size: 14px;
    max-width: 150px;
  }
`;

/**
 * Project metadata (status, last saved, etc.)
 */
export const ProjectMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  font-size: 12px;
  color: ${colors.textSecondary};
  line-height: 1;

  @media (max-width: 480px) {
    font-size: 11px;
    gap: ${spacing.xs};
  }
`;

/**
 * Status badge component
 */
export const StatusBadge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: ${borderRadius.sm};
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  ${(props) => {
    switch (props.status) {
      case 'draft':
        return `
          background: ${colors.warningLight};
          color: ${colors.warningDark};
        `;
      case 'published':
        return `
          background: ${colors.successLight};
          color: ${colors.successDark};
        `;
      case 'shared':
        return `
          background: ${colors.infoLight};
          color: ${colors.infoDark};
        `;
      default:
        return `
          background: ${colors.gray200};
          color: ${colors.gray700};
        `;
    }
  }}
`;

/**
 * Metrics container with cards
 */
export const MetricsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  padding: ${spacing.sm};
  background: rgba(255, 255, 255, 0.6);
  border-radius: ${borderRadius.md};
  border: 1px solid ${colors.borderLight};
  backdrop-filter: blur(10px);
`;

/**
 * Individual metric card
 */
export const MetricCard = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: ${borderRadius.sm};
  transition: all 0.2s ease;
  cursor: pointer;
  min-width: 60px;

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
  }
`;

/**
 * Metric value display
 */
export const MetricValue = styled.span`
  font-size: 16px;
  font-weight: 700;
  color: ${colors.textPrimary};
  line-height: 1;
`;

/**
 * Metric label
 */
export const MetricLabel = styled.span`
  font-size: 10px;
  color: ${colors.textSecondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
  line-height: 1;
`;

/**
 * Action button group
 */
export const ActionGroup = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};

  .ant-btn {
    border-radius: ${borderRadius.md};
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: ${elevation.sm};
    }
  }
`;

/**
 * Breadcrumb navigation container
 */
export const BreadcrumbContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  font-size: 12px;
  color: ${colors.textSecondary};

  .ant-breadcrumb {
    font-size: 12px;

    .ant-breadcrumb-link {
      color: ${colors.textSecondary};
      transition: color 0.2s ease;

      &:hover {
        color: ${colors.primary};
      }
    }

    .ant-breadcrumb-separator {
      color: ${colors.textTertiary};
    }
  }
`;

/**
 * Collaboration tools container
 */
export const CollaborationContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};

  .ant-avatar-group {
    .ant-avatar {
      border: 2px solid ${colors.background};
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.1);
        z-index: 10;
      }
    }
  }
`;
