/**
 * @fileoverview Form metrics calculation hook
 *
 * This hook calculates and tracks form complexity metrics, component counts,
 * performance indicators, and usage analytics for the enterprise header.
 *
 * @module useFormMetrics
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useMemo, useCallback } from 'react';
import { COMPLEXITY_LEVELS } from '../constants/headerConstants';

/**
 * Custom hook for form metrics calculation
 *
 * @param {Object} options - Configuration options
 * @param {Array} options.layout - Current form layout
 * @param {Object} options.components - Component registry
 * @returns {Object} Form metrics and analysis
 *
 * @example
 * ```jsx
 * const {
 *   totalComponents,
 *   containerComponents,
 *   complexity,
 *   performanceScore,
 *   metrics,
 *   getComponentsByType,
 *   getComplexityAnalysis
 * } = useFormMetrics({
 *   layout,
 *   components
 * });
 * ```
 */
export const useFormMetrics = ({ layout = [], components = {} }) => {
  /**
   * Calculate total component count recursively
   */
  const totalComponents = useMemo(() => {
    const countComponents = (items) => {
      if (!Array.isArray(items)) return 0;
      
      return items.reduce((count, item) => {
        let itemCount = 1; // Count the item itself
        
        // Count children recursively
        if (item.children && Array.isArray(item.children)) {
          itemCount += countComponents(item.children);
        }
        
        return count + itemCount;
      }, 0);
    };
    
    return countComponents(layout);
  }, [layout]);

  /**
   * Count container components (components that can hold other components)
   */
  const containerComponents = useMemo(() => {
    const containerTypes = [
      'tabContainer',
      'cardContainer',
      'formSection',
      'accordionContainer',
      'stepsContainer',
      'row',
      'column',
    ];
    
    const countContainers = (items) => {
      if (!Array.isArray(items)) return 0;
      
      return items.reduce((count, item) => {
        let containerCount = 0;
        
        // Check if current item is a container
        if (containerTypes.includes(item.type)) {
          containerCount = 1;
        }
        
        // Count containers in children
        if (item.children && Array.isArray(item.children)) {
          containerCount += countContainers(item.children);
        }
        
        return count + containerCount;
      }, 0);
    };
    
    return countContainers(layout);
  }, [layout]);

  /**
   * Calculate form complexity based on multiple factors
   */
  const complexity = useMemo(() => {
    // Base complexity from component count
    let complexityScore = totalComponents;
    
    // Add complexity for nesting depth
    const getMaxDepth = (items, depth = 0) => {
      if (!Array.isArray(items) || items.length === 0) return depth;
      
      return Math.max(
        ...items.map(item => {
          if (item.children && Array.isArray(item.children)) {
            return getMaxDepth(item.children, depth + 1);
          }
          return depth + 1;
        })
      );
    };
    
    const maxDepth = getMaxDepth(layout);
    complexityScore += maxDepth * 2; // Weight nesting depth
    
    // Add complexity for container components
    complexityScore += containerComponents * 1.5;
    
    // Add complexity for validation rules
    const validationComplexity = Object.values(components).reduce((count, component) => {
      const rules = component?.validation?.rules || [];
      return count + rules.length;
    }, 0);
    complexityScore += validationComplexity * 0.5;
    
    // Determine complexity level
    for (const [key, config] of Object.entries(COMPLEXITY_LEVELS)) {
      if (complexityScore <= config.threshold) {
        return {
          level: config.level,
          score: Math.round(complexityScore),
          color: config.color,
          percentage: Math.min((complexityScore / config.threshold) * 100, 100),
        };
      }
    }
    
    // Default to enterprise level
    return {
      level: COMPLEXITY_LEVELS.ENTERPRISE.level,
      score: Math.round(complexityScore),
      color: COMPLEXITY_LEVELS.ENTERPRISE.color,
      percentage: 100,
    };
  }, [totalComponents, containerComponents, layout, components]);

  /**
   * Calculate performance score based on form structure
   */
  const performanceScore = useMemo(() => {
    let score = 100; // Start with perfect score
    
    // Deduct points for high component count
    if (totalComponents > 50) {
      score -= Math.min((totalComponents - 50) * 0.5, 30);
    }
    
    // Deduct points for deep nesting
    const getMaxDepth = (items, depth = 0) => {
      if (!Array.isArray(items) || items.length === 0) return depth;
      
      return Math.max(
        ...items.map(item => {
          if (item.children && Array.isArray(item.children)) {
            return getMaxDepth(item.children, depth + 1);
          }
          return depth + 1;
        })
      );
    };
    
    const maxDepth = getMaxDepth(layout);
    if (maxDepth > 5) {
      score -= Math.min((maxDepth - 5) * 5, 20);
    }
    
    // Deduct points for excessive containers
    if (containerComponents > 10) {
      score -= Math.min((containerComponents - 10) * 2, 15);
    }
    
    // Ensure score doesn't go below 0
    return Math.max(Math.round(score), 0);
  }, [totalComponents, containerComponents, layout]);

  /**
   * Get components grouped by type
   */
  const getComponentsByType = useCallback(() => {
    const typeGroups = {};
    
    const categorizeComponents = (items) => {
      if (!Array.isArray(items)) return;
      
      items.forEach(item => {
        const type = item.type || 'unknown';
        typeGroups[type] = (typeGroups[type] || 0) + 1;
        
        // Process children recursively
        if (item.children && Array.isArray(item.children)) {
          categorizeComponents(item.children);
        }
      });
    };
    
    categorizeComponents(layout);
    return typeGroups;
  }, [layout]);

  /**
   * Get detailed complexity analysis
   */
  const getComplexityAnalysis = useCallback(() => {
    const componentsByType = getComponentsByType();
    
    return {
      totalComponents,
      containerComponents,
      dataEntryComponents: Object.entries(componentsByType)
        .filter(([type]) => ['input', 'select', 'checkbox', 'radio', 'datePicker'].includes(type))
        .reduce((sum, [, count]) => sum + count, 0),
      layoutComponents: Object.entries(componentsByType)
        .filter(([type]) => ['row', 'column', 'divider', 'space'].includes(type))
        .reduce((sum, [, count]) => sum + count, 0),
      displayComponents: Object.entries(componentsByType)
        .filter(([type]) => ['text', 'title', 'image', 'alert'].includes(type))
        .reduce((sum, [, count]) => sum + count, 0),
      complexity,
      performanceScore,
      componentsByType,
    };
  }, [totalComponents, containerComponents, complexity, performanceScore, getComponentsByType]);

  /**
   * Comprehensive metrics object
   */
  const metrics = useMemo(() => ({
    totalComponents,
    containerComponents,
    complexity,
    performanceScore,
    componentsByType: getComponentsByType(),
    analysis: getComplexityAnalysis(),
  }), [totalComponents, containerComponents, complexity, performanceScore, getComponentsByType, getComplexityAnalysis]);

  return {
    // Core metrics
    totalComponents,
    containerComponents,
    complexity,
    performanceScore,
    metrics,
    
    // Analysis functions
    getComponentsByType,
    getComplexityAnalysis,
  };
};
