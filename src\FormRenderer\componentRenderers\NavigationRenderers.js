/**
 * Navigation Component Renderers
 * 
 * Renders navigation components like Bread<PERSON>rumb, Menu, Pagination, Steps, etc.
 * These components help users navigate through forms and applications.
 */

import React from 'react';
import {
  Breadcrumb,
  Menu,
  Pagination,
  Steps,
} from 'antd';

/**
 * Renders a Breadcrumb component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Breadcrumb component
 */
export const renderBreadcrumb = (component) => {
  return (
    <Breadcrumb
      separator={component.styling?.separator}
      items={component.items || []}
    />
  );
};

/**
 * Renders a Menu component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Menu component
 */
export const renderMenu = (component) => {
  const menuProps = {
    mode: component.styling?.mode,
    theme: component.styling?.theme,
    items: component.items || [],
  };

  // Only add inlineCollapsed if mode is inline
  if (
    component.styling?.mode === 'inline' &&
    component.styling?.inlineCollapsed !== undefined
  ) {
    menuProps.inlineCollapsed = component.styling.inlineCollapsed;
  }

  return <Menu {...menuProps} />;
};

/**
 * Renders a Pagination component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Pagination component
 */
export const renderPagination = (component) => {
  return (
    <Pagination
      current={component.current}
      total={component.total}
      pageSize={component.pageSize}
      size={component.styling?.size}
      showSizeChanger={component.styling?.showSizeChanger}
      showQuickJumper={component.styling?.showQuickJumper}
      showTotal={
        component.styling?.showTotal
          ? (total, range) => `${range[0]}-${range[1]} of ${total} items`
          : undefined
      }
    />
  );
};

/**
 * Renders a Steps component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Steps component
 */
export const renderSteps = (component) => {
  return (
    <Steps
      current={component.current}
      type={component.styling?.type}
      size={component.styling?.size}
      direction={component.styling?.direction}
      labelPlacement={component.styling?.labelPlacement}
      items={component.items || []}
    />
  );
};
