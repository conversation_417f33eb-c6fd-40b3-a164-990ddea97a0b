/**
 * AI Schema Validator
 * Validates AI-generated form schemas for correctness and completeness
 */

import { validateFormSchema } from './schemaValidator';

/**
 * Comprehensive validation for AI-generated schemas
 * @param {Object} schema - The AI-generated schema
 * @returns {Object} Validation result with detailed feedback
 */
export const validateAIGeneratedSchema = (schema) => {
  const errors = [];
  const warnings = [];
  const suggestions = [];

  try {
    // Basic structure validation
    if (!schema || typeof schema !== 'object') {
      errors.push('Schema must be a valid object');
      return { isValid: false, errors, warnings, suggestions };
    }

    // Required top-level properties
    const requiredProps = ['metadata', 'layout', 'components'];
    for (const prop of requiredProps) {
      if (!schema[prop]) {
        errors.push(`Missing required property: ${prop}`);
      }
    }

    // Metadata validation
    if (schema.metadata) {
      const requiredMetadata = ['title', 'version', 'createdAt'];
      for (const prop of requiredMetadata) {
        if (!schema.metadata[prop]) {
          warnings.push(`Missing recommended metadata: ${prop}`);
        }
      }

      // Version format check
      if (
        schema.metadata.version &&
        !/^\d+\.\d+\.\d+$/.test(schema.metadata.version)
      ) {
        warnings.push(
          'Version should follow semantic versioning (e.g., 1.0.0)',
        );
      }

      // Date format check
      if (schema.metadata.createdAt) {
        try {
          new Date(schema.metadata.createdAt);
        } catch (e) {
          warnings.push('createdAt should be a valid ISO date string');
        }
      }
    }

    // Layout validation
    if (schema.layout) {
      if (!Array.isArray(schema.layout)) {
        errors.push('Layout must be an array');
      } else {
        schema.layout.forEach((row, index) => {
          if (!row.id) {
            errors.push(`Layout row ${index} missing required 'id' property`);
          }
          if (!row.type) {
            errors.push(`Layout row ${index} missing required 'type' property`);
          }
          if (row.type === 'row' && !row.children) {
            errors.push(`Layout row ${index} missing 'children' array`);
          }
        });
      }
    }

    // Components validation
    if (schema.components) {
      if (typeof schema.components !== 'object') {
        errors.push('Components must be an object');
      } else {
        const componentIds = Object.keys(schema.components);

        if (componentIds.length === 0) {
          warnings.push('No components defined in schema');
        }

        componentIds.forEach((id) => {
          const component = schema.components[id];

          // Required component properties
          if (!component.type) {
            errors.push(`Component ${id} missing required 'type' property`);
          }
          if (!component.id) {
            warnings.push(`Component ${id} missing 'id' property`);
          }
          if (!component.label && !component.content) {
            warnings.push(
              `Component ${id} missing 'label' or 'content' property`,
            );
          }

          // Validate component type
          const validTypes = [
            'input',
            'textarea',
            'password',
            'select',
            'radio',
            'checkbox',
            'datePicker',
            'timePicker',
            'upload',
            'button',
            'switch',
            'rate',
            'slider',
            'cascader',
            'treeSelect',
            'transfer',
            'mentions',
            'colorPicker',
            'typography',
            'divider',
            'alert',
            'progress',
            'tabContainer',
            'cardContainer',
            'formSection',
            'accordionContainer',
            'stepsContainer',
            'gridContainer',
            'flexContainer',
          ];

          if (component.type && !validTypes.includes(component.type)) {
            warnings.push(
              `Component ${id} has unknown type: ${component.type}`,
            );
          }

          // Validate form field naming
          if (
            [
              'input',
              'textarea',
              'password',
              'select',
              'radio',
              'checkbox',
              'datePicker',
            ].includes(component.type)
          ) {
            if (!component.name) {
              warnings.push(
                `Form field component ${id} missing 'name' property`,
              );
            } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(component.name)) {
              warnings.push(
                `Component ${id} name should be a valid identifier`,
              );
            }
          }

          // Validate validation rules
          if (component.validation) {
            if (typeof component.validation !== 'object') {
              errors.push(`Component ${id} validation must be an object`);
            } else {
              // Check for common validation patterns
              if (
                component.validation.required === undefined &&
                ['input', 'textarea', 'select'].includes(component.type)
              ) {
                suggestions.push(
                  `Consider adding 'required' validation to ${id}`,
                );
              }
            }
          }

          // Validate styling
          if (component.styling && typeof component.styling !== 'object') {
            warnings.push(`Component ${id} styling must be an object`);
          }

          // Validate props
          if (component.props && typeof component.props !== 'object') {
            warnings.push(`Component ${id} props must be an object`);
          }
        });
      }
    }

    // Cross-reference validation
    if (schema.layout && schema.components) {
      const componentIds = Object.keys(schema.components);
      const referencedIds = new Set();

      // Extract all component IDs referenced in layout
      const extractReferencedIds = (layoutItem) => {
        if (layoutItem.children) {
          if (Array.isArray(layoutItem.children)) {
            layoutItem.children.forEach((child) => {
              if (typeof child === 'string') {
                referencedIds.add(child);
              } else if (typeof child === 'object') {
                extractReferencedIds(child);
              }
            });
          }
        }
      };

      schema.layout.forEach(extractReferencedIds);

      // Check for orphaned components
      componentIds.forEach((id) => {
        if (!referencedIds.has(id)) {
          warnings.push(`Component ${id} is defined but not used in layout`);
        }
      });

      // Check for missing components
      referencedIds.forEach((id) => {
        if (!componentIds.includes(id)) {
          errors.push(`Layout references component ${id} which is not defined`);
        }
      });
    }

    // Use existing schema validator for additional checks
    const baseValidation = validateFormSchema(schema);
    if (!baseValidation.isValid) {
      errors.push(...baseValidation.errors);
    }

    // Performance suggestions
    if (schema.components && Object.keys(schema.components).length > 50) {
      suggestions.push(
        'Consider breaking large forms into multiple steps for better performance',
      );
    }

    // Accessibility suggestions
    if (schema.components) {
      const hasRequiredFields = Object.values(schema.components).some(
        (comp) => comp.validation && comp.validation.required,
      );
      if (hasRequiredFields) {
        suggestions.push(
          'Consider adding aria-labels and help text for better accessibility',
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score: calculateSchemaScore(errors, warnings, suggestions),
      metadata: {
        componentCount: schema.components
          ? Object.keys(schema.components).length
          : 0,
        layoutDepth: calculateLayoutDepth(schema.layout),
        validatedAt: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [`Validation error: ${error.message}`],
      warnings: [],
      suggestions: [],
      score: 0,
    };
  }
};

/**
 * Calculate a quality score for the schema (0-100)
 */
const calculateSchemaScore = (errors, warnings, suggestions) => {
  let score = 100;

  // Deduct points for errors (critical issues)
  score -= errors.length * 20;

  // Deduct points for warnings (important issues)
  score -= warnings.length * 5;

  // Deduct points for missing best practices
  score -= suggestions.length * 2;

  return Math.max(0, score);
};

/**
 * Calculate the maximum nesting depth of the layout
 */
const calculateLayoutDepth = (layout, currentDepth = 0) => {
  if (!layout || !Array.isArray(layout)) return currentDepth;

  let maxDepth = currentDepth;

  layout.forEach((item) => {
    if (item.children && Array.isArray(item.children)) {
      const childDepth = calculateLayoutDepth(item.children, currentDepth + 1);
      maxDepth = Math.max(maxDepth, childDepth);
    }
  });

  return maxDepth;
};

/**
 * Transform AI-generated schema to fix compatibility issues
 */
export const transformAISchema = (schema) => {
  const transformed = JSON.parse(JSON.stringify(schema));

  // Fix layout structure - convert string children to component objects
  if (transformed.layout && Array.isArray(transformed.layout)) {
    transformed.layout.forEach((row) => {
      if (row.children && Array.isArray(row.children)) {
        row.children.forEach((column) => {
          if (column.children && Array.isArray(column.children)) {
            // Convert string component IDs to component objects and validate they exist
            column.children = column.children
              .map((child) => {
                if (typeof child === 'string') {
                  // Check if component exists
                  if (transformed.components && transformed.components[child]) {
                    return {
                      type: 'component',
                      id: child,
                    };
                  } else {
                    console.warn(
                      `Component ${child} referenced in layout but not found in components`,
                    );
                    return null; // Will be filtered out
                  }
                }
                return child;
              })
              .filter(Boolean); // Remove null entries
          }
        });
      }
    });
  }

  // Fix component types
  if (transformed.components) {
    Object.keys(transformed.components).forEach((key) => {
      const component = transformed.components[key];

      // Fix email type -> input with email validation
      if (component.type === 'email') {
        component.type = 'input';
        if (!component.validation) component.validation = {};
        if (!component.validation.rules) component.validation.rules = [];
        component.validation.rules.push({
          type: 'email',
          message: 'Please enter a valid email address',
        });
      }

      // Fix phone type -> input with phone validation
      if (component.type === 'phone') {
        component.type = 'input';
        if (!component.validation) component.validation = {};
        if (!component.validation.rules) component.validation.rules = [];
        component.validation.rules.push({
          pattern: /^[\+]?[1-9][\d]{0,15}$/,
          message: 'Please enter a valid phone number',
        });
      }

      // Fix datePicker -> date
      if (component.type === 'datePicker') {
        component.type = 'date';
      }

      // Fix timePicker -> timepicker
      if (component.type === 'timePicker') {
        component.type = 'timepicker';
      }

      // Fix treeSelect -> treeselect
      if (component.type === 'treeSelect') {
        component.type = 'treeselect';
      }

      // Fix colorPicker -> colorpicker
      if (component.type === 'colorPicker') {
        component.type = 'colorpicker';
      }
    });
  }

  // Fix stepsContainer structure
  if (transformed.components) {
    Object.keys(transformed.components).forEach((key) => {
      const component = transformed.components[key];

      if (component.type === 'stepsContainer') {
        // Move props.steps to steps
        if (component.props && component.props.steps) {
          component.steps = component.props.steps.map((step, index) => ({
            id: `step-${index + 1}`,
            key: `step${index + 1}`,
            title: step.title,
            description: step.description,
            children: [],
          }));
          delete component.props.steps;
        }

        // Move props to stepsProps
        if (component.props) {
          component.stepsProps = { ...component.props };
          delete component.props;
        }

        // Handle children structure
        if (component.children && Array.isArray(component.children)) {
          component.children.forEach((stepData, stepIndex) => {
            if (stepData.stepIndex !== undefined && stepData.components) {
              if (component.steps && component.steps[stepData.stepIndex]) {
                component.steps[stepData.stepIndex].children =
                  stepData.components;
              }
            }
          });
          delete component.children;
        }
      }
    });
  }

  return transformed;
};

/**
 * Enhance AI-generated schema with missing properties
 */
export const enhanceAISchema = (schema) => {
  // First transform for compatibility
  const transformed = transformAISchema(schema);
  const enhanced = JSON.parse(JSON.stringify(transformed));
  const now = new Date().toISOString();

  // Ensure metadata
  if (!enhanced.metadata) {
    enhanced.metadata = {};
  }

  if (!enhanced.metadata.version) {
    enhanced.metadata.version = '1.0.0';
  }

  if (!enhanced.metadata.createdAt) {
    enhanced.metadata.createdAt = now;
  }

  enhanced.metadata.updatedAt = now;
  enhanced.metadata.enhancedByAI = true;

  // Add missing IDs
  if (enhanced.layout) {
    enhanced.layout.forEach((item, index) => {
      if (!item.id) {
        item.id = `ai-row-${index}-${Date.now()}`;
      }
    });
  }

  if (enhanced.components) {
    Object.keys(enhanced.components).forEach((key) => {
      const component = enhanced.components[key];
      if (!component.id) {
        component.id = key;
      }
      if (
        !component.name &&
        ['input', 'textarea', 'select'].includes(component.type)
      ) {
        component.name = key.replace(/[^a-zA-Z0-9]/g, '_');
      }
    });
  }

  return enhanced;
};

/**
 * Generate a validation report for display
 */
export const generateValidationReport = (validationResult) => {
  const { isValid, errors, warnings, suggestions, score, metadata } =
    validationResult;

  return {
    summary: {
      isValid,
      score,
      grade:
        score >= 90
          ? 'A'
          : score >= 80
          ? 'B'
          : score >= 70
          ? 'C'
          : score >= 60
          ? 'D'
          : 'F',
      componentCount: metadata?.componentCount || 0,
      layoutDepth: metadata?.layoutDepth || 0,
    },
    issues: {
      critical: errors,
      important: warnings,
      suggestions: suggestions,
    },
    recommendations: generateRecommendations(validationResult),
  };
};

/**
 * Generate actionable recommendations based on validation results
 */
const generateRecommendations = (validationResult) => {
  const { errors, warnings, suggestions, score } = validationResult;
  const recommendations = [];

  if (errors.length > 0) {
    recommendations.push({
      priority: 'high',
      title: 'Fix Critical Issues',
      description: 'Address all errors before using this schema in production',
      actions: errors.slice(0, 3), // Show top 3 errors
    });
  }

  if (warnings.length > 0) {
    recommendations.push({
      priority: 'medium',
      title: 'Improve Schema Quality',
      description: 'Address warnings to improve form reliability',
      actions: warnings.slice(0, 3), // Show top 3 warnings
    });
  }

  if (score < 80) {
    recommendations.push({
      priority: 'low',
      title: 'Enhance Best Practices',
      description: 'Follow suggestions to create a more professional form',
      actions: suggestions.slice(0, 3), // Show top 3 suggestions
    });
  }

  return recommendations;
};

export default {
  validateAIGeneratedSchema,
  transformAISchema,
  enhanceAISchema,
  generateValidationReport,
};
