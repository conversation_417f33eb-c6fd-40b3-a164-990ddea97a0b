import styled from 'styled-components';
import { colors, motionCurves, revealEffect } from '../theme';

// UX-optimized Drop Zone with clear affordances and progressive disclosure
export const DropZone = styled.div`
  flex: 0 0 auto;
  height: 12px; /* Minimal footprint when inactive */
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 4px;
  margin: 4px 0;
  position: relative;
  background: transparent;
  border: 1px dashed transparent;
  pointer-events: auto;

  /* UX: Subtle hint for discoverability without visual noise */
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 1px;
    background: ${colors.borderLight};
    opacity: 0;
    transition: all 0.2s ease;
  }

  /* Modern gradient background hint */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      ${colors.primary}02 0%,
      ${colors.primary}05 50%,
      ${colors.primary}02 100%
    );
    border-radius: 6px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  /* Hide every second drop zone to prevent overcrowding */
  &:nth-of-type(2n) {
    display: none;
  }

  /* Horizontal drag zones (between columns) */
  &.horizontalDrag {
    width: 14px; /* Optimized for Microsoft Fluent Design */
    height: auto;
    min-height: 90px;
    margin: 0 8px;
    border-radius: 4px;
  }

  /* Last drop zone styling */
  &:not(.horizontalDrag).isLast {
    flex: 1 1 auto;
    min-height: 56px;
  }

  /* Modern hover indication with enhanced feedback */
  &:hover:not(.active) {
    border-color: ${colors.primary}40;
    transform: translateY(-1px);

    &::before {
      opacity: 1;
    }

    /* Enhanced last drop zone hint */
    &.isLast::after {
      content: '✨ Drop components here to build your form';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: ${colors.primary};
      font-size: 13px;
      font-weight: 500;
      opacity: 0.8;
      pointer-events: none;
      white-space: nowrap;
      text-align: center;
      background: ${colors.background};
      padding: 4px 12px;
      border-radius: 20px;
      border: 1px solid ${colors.primary}20;
      box-shadow: ${colors.shadowLight};
    }
  }

  /* UX-optimized active state: Clear, confident feedback */
  &.active {
    height: 32px; /* Expand to show clear drop target */
    background: ${colors.primaryLight};
    border: 2px solid ${colors.primary};
    border-style: solid;
    box-shadow: 0 2px 8px ${colors.primary}20, 0 0 0 2px ${colors.primary}10;
    border-radius: 6px;

    /* Clear visual indicator */
    background-image: linear-gradient(
      90deg,
      ${colors.primary}20 0%,
      ${colors.primary}10 50%,
      ${colors.primary}20 100%
    );
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;

    &.horizontalDrag {
      width: 24px;
      height: auto;
      min-height: 80px;
      background-image: linear-gradient(
        180deg,
        ${colors.primary}20 0%,
        ${colors.primary}10 50%,
        ${colors.primary}20 100%
      );
      background-size: 100% 100%;
    }

    /* Clear, actionable feedback */
    &::after {
      content: '+ Drop component here';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: ${colors.primary};
      font-size: 12px;
      font-weight: 600;
      opacity: 1;
      pointer-events: none;
      white-space: nowrap;
      background: ${colors.background};
      padding: 6px 12px;
      border-radius: 16px;
      border: 1px solid ${colors.primary};
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      z-index: 2;
    }

    /* Accessibility: Ensure sufficient contrast */
    @media (prefers-contrast: high) {
      border-width: 3px;
      &::after {
        background: ${colors.primary};
        color: white;
      }
    }
  }

  /* Subtle warning state for many columns */
  &.active.warning-many-columns {
    border-color: ${colors.warning};
    background: ${colors.warningLight};
    box-shadow: 0 1px 4px ${colors.warning}20;

    &::after {
      content: '⚠️ Many columns';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: ${colors.warningDark};
      font-size: 11px;
      font-weight: 500;
      background: ${colors.background};
      padding: 4px 8px;
      border-radius: 12px;
      border: 1px solid ${colors.warning};
      z-index: 2;
      white-space: nowrap;
    }
  }

  /* Enhanced container-specific drop zones with modern styling */
  &.tab-content-drop-zone {
    background: ${colors.info}05;
    border-color: ${colors.info}30;
    border-radius: 10px;

    &.active {
      background: ${colors.infoLight};
      border-color: ${colors.info};
      box-shadow: 0 1px 4px ${colors.info}20;

      &::after {
        content: '📑 Tab Content';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: ${colors.infoDark};
        font-size: 11px;
        font-weight: 500;
        background: ${colors.background};
        padding: 4px 8px;
        border-radius: 12px;
        border: 1px solid ${colors.info};
        z-index: 2;
        white-space: nowrap;
      }
    }
  }

  &.card-content-drop-zone {
    background: ${colors.success}05;
    border-color: ${colors.success}30;
    border-radius: 10px;

    &.active {
      background: ${colors.successLight};
      border-color: ${colors.success};
      box-shadow: 0 1px 4px ${colors.success}20;

      &::after {
        content: '🃏 Card Content';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: ${colors.successDark};
        font-size: 11px;
        font-weight: 500;
        background: ${colors.background};
        padding: 4px 8px;
        border-radius: 12px;
        border: 1px solid ${colors.success};
        z-index: 2;
        white-space: nowrap;
      }
    }
  }

  &.section-content-drop-zone {
    background: ${colors.warning}05;
    border-color: ${colors.warning}30;
    border-radius: 10px;

    &.active {
      background: ${colors.warningLight};
      border-color: ${colors.warning};
      box-shadow: 0 1px 4px ${colors.warning}20;

      &::after {
        content: '📋 Section Content';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: ${colors.warningDark};
        font-size: 11px;
        font-weight: 500;
        background: ${colors.background};
        padding: 4px 8px;
        border-radius: 12px;
        border: 1px solid ${colors.warning};
        z-index: 2;
        white-space: nowrap;
      }
    }
  }

  /* Responsive adjustments for Microsoft Fluent Design */
  @media (max-width: 768px) {
    height: 16px;
    margin: 6px 0;

    &.horizontalDrag {
      width: 12px;
      min-height: 70px;
      margin: 0 6px;
    }

    &:not(.horizontalDrag).isLast {
      min-height: 48px;
    }
  }

  /* Microsoft Fluent Design focus ring */
  &:focus {
    outline: 2px solid ${colors.accent};
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Fluent Design accessibility improvements */
  &[aria-label] {
    position: relative;
  }

  /* Smooth entrance animation */
  animation: ${revealEffect} 0.3s ${motionCurves.decelerate};
`;
