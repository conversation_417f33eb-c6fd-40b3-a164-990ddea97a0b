/**
 * @fileoverview Custom hook for scroll management and indicators
 *
 * This hook provides scroll state management, scroll indicators,
 * and smooth scrolling utilities for the properties panel content.
 *
 * @module useScrollManagement
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useState, useCallback, useEffect, useRef } from 'react';

/**
 * Custom hook for scroll management
 *
 * Provides scroll state tracking, smooth scrolling utilities,
 * and scroll indicator management for content areas.
 *
 * @param {Object} params - Hook parameters
 * @param {boolean} params.enabled - Whether scroll management is enabled
 * @param {number} params.threshold - Scroll threshold for indicators
 *
 * @returns {Object} Scroll management utilities
 * @returns {React.RefObject} returns.scrollRef - Ref for the scrollable container
 * @returns {boolean} returns.canScrollUp - Whether content can scroll up
 * @returns {boolean} returns.canScrollDown - Whether content can scroll down
 * @returns {boolean} returns.isScrollable - Whether content is scrollable
 * @returns {Function} returns.scrollToTop - Scroll to top function
 * @returns {Function} returns.scrollToBottom - Scroll to bottom function
 * @returns {Function} returns.scrollToElement - Scroll to specific element
 * @returns {Object} returns.scrollProps - Props to spread on scrollable element
 *
 * @example
 * ```jsx
 * const { scrollRef, canScrollUp, canScrollDown, scrollProps } = useScrollManagement({
 *   enabled: true,
 *   threshold: 10
 * });
 * 
 * return (
 *   <div ref={scrollRef} {...scrollProps}>
 *     {content}
 *   </div>
 * );
 * ```
 */
export const useScrollManagement = ({
  enabled = true,
  threshold = 10,
} = {}) => {
  const scrollRef = useRef(null);
  const [scrollState, setScrollState] = useState({
    canScrollUp: false,
    canScrollDown: false,
    isScrollable: false,
    scrollTop: 0,
    scrollHeight: 0,
    clientHeight: 0,
  });

  /**
   * Updates scroll state based on current scroll position
   */
  const updateScrollState = useCallback(() => {
    if (!enabled || !scrollRef.current) return;

    const element = scrollRef.current;
    const { scrollTop, scrollHeight, clientHeight } = element;

    const newState = {
      scrollTop,
      scrollHeight,
      clientHeight,
      isScrollable: scrollHeight > clientHeight,
      canScrollUp: scrollTop > threshold,
      canScrollDown: scrollTop < scrollHeight - clientHeight - threshold,
    };

    setScrollState(prevState => {
      // Only update if state has actually changed
      if (
        prevState.canScrollUp !== newState.canScrollUp ||
        prevState.canScrollDown !== newState.canScrollDown ||
        prevState.isScrollable !== newState.isScrollable
      ) {
        return newState;
      }
      return prevState;
    });
  }, [enabled, threshold]);

  /**
   * Handles scroll events with throttling
   */
  const handleScroll = useCallback(() => {
    updateScrollState();
  }, [updateScrollState]);

  /**
   * Scrolls to the top of the container
   */
  const scrollToTop = useCallback(() => {
    if (!scrollRef.current) return;

    scrollRef.current.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  /**
   * Scrolls to the bottom of the container
   */
  const scrollToBottom = useCallback(() => {
    if (!scrollRef.current) return;

    scrollRef.current.scrollTo({
      top: scrollRef.current.scrollHeight,
      behavior: 'smooth',
    });
  }, []);

  /**
   * Scrolls to a specific element within the container
   *
   * @param {HTMLElement|string} target - Element or selector to scroll to
   * @param {Object} options - Scroll options
   */
  const scrollToElement = useCallback((target, options = {}) => {
    if (!scrollRef.current) return;

    let element = target;
    if (typeof target === 'string') {
      element = scrollRef.current.querySelector(target);
    }

    if (!element) return;

    const containerRect = scrollRef.current.getBoundingClientRect();
    const elementRect = element.getBoundingClientRect();
    const scrollTop = scrollRef.current.scrollTop;

    const targetScrollTop = scrollTop + elementRect.top - containerRect.top - (options.offset || 20);

    scrollRef.current.scrollTo({
      top: Math.max(0, targetScrollTop),
      behavior: options.behavior || 'smooth',
    });
  }, []);

  /**
   * Set up scroll event listeners and initial state
   */
  useEffect(() => {
    if (!enabled || !scrollRef.current) return;

    const element = scrollRef.current;
    
    // Initial state update
    updateScrollState();

    // Add scroll event listener
    element.addEventListener('scroll', handleScroll, { passive: true });

    // Add resize observer to handle content changes
    const resizeObserver = new ResizeObserver(() => {
      updateScrollState();
    });

    resizeObserver.observe(element);

    // Cleanup
    return () => {
      element.removeEventListener('scroll', handleScroll);
      resizeObserver.disconnect();
    };
  }, [enabled, handleScroll, updateScrollState]);

  /**
   * Update scroll state when content changes
   */
  useEffect(() => {
    if (!enabled) return;

    // Use MutationObserver to detect content changes
    const observer = new MutationObserver(() => {
      // Delay update to allow for DOM changes to complete
      setTimeout(updateScrollState, 100);
    });

    if (scrollRef.current) {
      observer.observe(scrollRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class'],
      });
    }

    return () => observer.disconnect();
  }, [enabled, updateScrollState]);

  /**
   * Props to spread on the scrollable element
   */
  const scrollProps = {
    className: scrollState.isScrollable ? '' : 'no-scroll',
    onScroll: handleScroll,
    style: {
      '--scroll-indicator-top': scrollState.canScrollUp ? '1' : '0',
      '--scroll-indicator-bottom': scrollState.canScrollDown ? '1' : '0',
    },
  };

  return {
    // Ref
    scrollRef,
    
    // State
    canScrollUp: scrollState.canScrollUp,
    canScrollDown: scrollState.canScrollDown,
    isScrollable: scrollState.isScrollable,
    scrollTop: scrollState.scrollTop,
    scrollHeight: scrollState.scrollHeight,
    clientHeight: scrollState.clientHeight,
    
    // Actions
    scrollToTop,
    scrollToBottom,
    scrollToElement,
    updateScrollState,
    
    // Props
    scrollProps,
  };
};
