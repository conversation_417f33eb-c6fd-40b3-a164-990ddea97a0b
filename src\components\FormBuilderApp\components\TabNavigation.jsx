/**
 * @fileoverview Tab Navigation component for form builder tabs
 *
 * This component provides the tab configuration and navigation
 * for the form builder application with memoized performance.
 * Enhanced with enterprise-grade header/toolbar system.
 *
 * @module TabNavigation
 * @version 2.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React from 'react';
import { motion } from 'framer-motion';
import BuilderTab from './BuilderTab';
import EnhancedPreviewTab from './PreviewSystem/components/EnhancedPreviewTab';
import EnterpriseHeader from './EnterpriseHeader/EnterpriseHeader';
import { tabVariants, containerVariants } from '../constants/animations';

/**
 * Tab Navigation component
 *
 * Provides the main tab navigation interface for the form builder
 * with optimized performance through memoization.
 *
 * @param {Object} props - Component props
 * @param {string} props.activeTab - Currently active tab key
 * @param {Function} props.setActiveTab - Active tab setter
 * @param {Object} props.builderTabProps - Props for builder tab
 * @param {Object} props.previewTabProps - Props for preview tab
 *
 * @returns {React.ReactNode} Tab navigation JSX
 *
 * @example
 * ```jsx
 * <TabNavigation
 *   activeTab={activeTab}
 *   setActiveTab={setActiveTab}
 *   builderTabProps={builderTabProps}
 *   previewTabProps={previewTabProps}
 * />
 * ```
 */
const TabNavigation = ({
  activeTab,
  setActiveTab,
  builderTabProps,
  previewTabProps,
}) => {
  return (
    <motion.div
      variants={containerVariants}
      initial='hidden'
      animate='visible'
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      {/* Enterprise Header/Toolbar */}
      <EnterpriseHeader
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        builderTabProps={builderTabProps}
        previewTabProps={{
          ...previewTabProps,
          currentFormSchema: builderTabProps.currentFormSchema,
        }}
        onUpdateFormSchema={builderTabProps.onUpdateFormSchema}
        componentUpdateCounter={previewTabProps.componentUpdateCounter}
      />

      {/* Content Area - No tabs, just content switching */}
      <div
        style={{
          flex: 1,
          background: 'linear-gradient(135deg, #fafafa 0%, #ffffff 100%)',
          overflow: 'hidden',
        }}
      >
        {activeTab === 'builder' ? (
          <motion.div
            key='builder-content'
            variants={tabVariants}
            initial='hidden'
            animate='visible'
            exit='exit'
            style={{ height: '100%' }}
          >
            <BuilderTab {...builderTabProps} />
          </motion.div>
        ) : (
          <motion.div
            key='preview-content'
            variants={tabVariants}
            initial='hidden'
            animate='visible'
            exit='exit'
            style={{ height: '100%' }}
          >
            <EnhancedPreviewTab
              layout={previewTabProps.layout}
              components={previewTabProps.components}
              formSchema={previewTabProps.currentFormSchema}
              onFormSubmit={previewTabProps.onFormSubmit}
              onValidationTest={(data) => console.log('Validation test:', data)}
            />
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default TabNavigation;
