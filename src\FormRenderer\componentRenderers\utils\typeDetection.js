/**
 * Component Type Detection Utilities
 * 
 * Utility functions for detecting, validating, and categorizing component types.
 * These functions help determine how components should be rendered and processed.
 */

/**
 * Component type categories for classification
 */
export const COMPONENT_CATEGORIES = {
  DATA_ENTRY: 'data_entry',
  ADVANCED_DATA_ENTRY: 'advanced_data_entry',
  DISPLAY: 'display',
  DATA_DISPLAY: 'data_display',
  NAVIGATION: 'navigation',
  LAYOUT: 'layout',
  FEEDBACK: 'feedback',
  CONTAINER: 'container',
  UNKNOWN: 'unknown',
};

/**
 * Component type mappings by category
 */
const COMPONENT_TYPE_MAPPINGS = {
  [COMPONENT_CATEGORIES.DATA_ENTRY]: [
    'input', 'email', 'textarea', 'select', 'radio', 'checkbox',
    'inputnumber', 'number', 'password', 'datepicker', 'date',
    'rangepicker', 'switch', 'rate', 'slider', 'upload'
  ],
  [COMPONENT_CATEGORIES.ADVANCED_DATA_ENTRY]: [
    'autocomplete', 'cascader', 'colorpicker', 'mentions',
    'timepicker', 'transfer', 'treeselect'
  ],
  [COMPONENT_CATEGORIES.DISPLAY]: [
    'avatar', 'badge', 'image', 'tag', 'button', 'typography', 'statistic'
  ],
  [COMPONENT_CATEGORIES.DATA_DISPLAY]: [
    'table', 'list', 'calendar', 'carousel', 'descriptions',
    'empty', 'timeline', 'tree'
  ],
  [COMPONENT_CATEGORIES.NAVIGATION]: [
    'breadcrumb', 'menu', 'pagination', 'steps'
  ],
  [COMPONENT_CATEGORIES.LAYOUT]: [
    'divider', 'space'
  ],
  [COMPONENT_CATEGORIES.FEEDBACK]: [
    'alert', 'progress', 'skeleton', 'spin'
  ],
  [COMPONENT_CATEGORIES.CONTAINER]: [
    'TAB_CONTAINER', 'CARD_CONTAINER', 'FORM_SECTION',
    'ACCORDION_CONTAINER', 'STEPS_CONTAINER', 'GRID_CONTAINER', 'FLEX_CONTAINER'
  ],
};

/**
 * Detects the category of a component based on its type
 * 
 * @param {string} componentType - Type of the component
 * @returns {string} Component category
 */
export const detectComponentCategory = (componentType) => {
  if (!componentType) return COMPONENT_CATEGORIES.UNKNOWN;
  
  for (const [category, types] of Object.entries(COMPONENT_TYPE_MAPPINGS)) {
    if (types.includes(componentType)) {
      return category;
    }
  }
  
  return COMPONENT_CATEGORIES.UNKNOWN;
};

/**
 * Checks if a component type is valid
 * 
 * @param {string} componentType - Type of the component
 * @returns {boolean} True if component type is valid
 */
export const isValidComponentType = (componentType) => {
  if (!componentType) return false;
  
  const allTypes = Object.values(COMPONENT_TYPE_MAPPINGS).flat();
  return allTypes.includes(componentType);
};

/**
 * Gets all component types for a specific category
 * 
 * @param {string} category - Component category
 * @returns {Array} Array of component types in the category
 */
export const getComponentTypesByCategory = (category) => {
  return COMPONENT_TYPE_MAPPINGS[category] || [];
};

/**
 * Checks if a component requires form binding
 * 
 * @param {string} componentType - Type of the component
 * @returns {boolean} True if component requires form binding
 */
export const requiresFormBinding = (componentType) => {
  const category = detectComponentCategory(componentType);
  return category === COMPONENT_CATEGORIES.DATA_ENTRY || 
         category === COMPONENT_CATEGORIES.ADVANCED_DATA_ENTRY;
};

/**
 * Checks if a component supports validation
 * 
 * @param {string} componentType - Type of the component
 * @returns {boolean} True if component supports validation
 */
export const supportsValidation = (componentType) => {
  return requiresFormBinding(componentType);
};

/**
 * Checks if a component can have children
 * 
 * @param {string} componentType - Type of the component
 * @returns {boolean} True if component can have children
 */
export const canHaveChildren = (componentType) => {
  const category = detectComponentCategory(componentType);
  return category === COMPONENT_CATEGORIES.CONTAINER ||
         category === COMPONENT_CATEGORIES.LAYOUT;
};

/**
 * Gets the default renderer function name for a component type
 * 
 * @param {string} componentType - Type of the component
 * @returns {string} Renderer function name
 */
export const getRendererFunctionName = (componentType) => {
  if (!componentType) return 'renderUnknown';
  
  // Convert component type to camelCase renderer function name
  const camelCase = componentType
    .split(/[-_]/)
    .map((word, index) => 
      index === 0 ? word.toLowerCase() : 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    )
    .join('');
  
  return `render${camelCase.charAt(0).toUpperCase() + camelCase.slice(1)}`;
};

/**
 * Determines if a component should be wrapped in Form.Item
 * 
 * @param {Object} component - Component configuration object
 * @returns {boolean} True if component should be wrapped in Form.Item
 */
export const shouldWrapInFormItem = (component) => {
  if (!component) return false;
  
  // Components that require form binding should be wrapped
  if (requiresFormBinding(component.type)) return true;
  
  // Components with validation should be wrapped
  if (component.validation) return true;
  
  // Components with form item props should be wrapped
  if (component.formItemProps) return true;
  
  // Components with labels should be wrapped
  if (component.label) return true;
  
  return false;
};

/**
 * Gets component-specific default props based on type
 * 
 * @param {string} componentType - Type of the component
 * @returns {Object} Default props for the component type
 */
export const getTypeSpecificDefaults = (componentType) => {
  const defaults = {
    // Data Entry Components
    input: {
      allowClear: true,
      placeholder: 'Enter text...',
    },
    email: {
      allowClear: true,
      placeholder: 'Enter email address...',
      type: 'email',
    },
    textarea: {
      allowClear: true,
      placeholder: 'Enter text...',
      rows: 4,
    },
    select: {
      allowClear: true,
      placeholder: 'Please select...',
      showSearch: true,
    },
    datepicker: {
      allowClear: true,
      placeholder: 'Select date',
    },
    upload: {
      listType: 'text',
      multiple: false,
    },
    
    // Display Components
    button: {
      type: 'default',
      size: 'middle',
    },
    avatar: {
      size: 'default',
      shape: 'circle',
    },
    
    // Feedback Components
    alert: {
      type: 'info',
      showIcon: true,
    },
    progress: {
      type: 'line',
      showInfo: true,
    },
  };
  
  return defaults[componentType] || {};
};

/**
 * Validates component configuration based on its type
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Validation result with isValid flag and errors array
 */
export const validateComponentByType = (component) => {
  const errors = [];
  
  if (!component) {
    errors.push('Component configuration is required');
    return { isValid: false, errors };
  }
  
  if (!component.type) {
    errors.push('Component type is required');
    return { isValid: false, errors };
  }
  
  if (!isValidComponentType(component.type)) {
    errors.push(`Unknown component type: ${component.type}`);
  }
  
  // Validate form binding components
  if (requiresFormBinding(component.type)) {
    if (!component.name && !component.id) {
      errors.push('Form input components must have a name or id for form binding');
    }
  }
  
  // Validate container components
  if (canHaveChildren(component.type)) {
    if (component.type === 'TAB_CONTAINER' && !component.tabs) {
      errors.push('Tab container must have tabs configuration');
    }
    
    if (component.type === 'STEPS_CONTAINER' && !component.steps) {
      errors.push('Steps container must have steps configuration');
    }
  }
  
  // Validate data display components
  const category = detectComponentCategory(component.type);
  if (category === COMPONENT_CATEGORIES.DATA_DISPLAY) {
    if (component.type === 'table' && !component.columns) {
      errors.push('Table component must have columns configuration');
    }
    
    if (component.type === 'tree' && !component.treeData) {
      errors.push('Tree component must have treeData configuration');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};
