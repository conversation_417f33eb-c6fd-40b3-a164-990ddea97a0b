import { useMemo } from 'react';
import { useValidationRules } from './useValidationRules';

/**
 * Custom hook for generating Form.Item props
 * 
 * Generates and memoizes props specifically for Ant Design Form.Item components.
 * Includes label, validation rules, help text, and other form item configurations.
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Memoized Form.Item props
 */
export const useFormItemProps = (component) => {
  // Get validation rules using the validation hook
  const validationRules = useValidationRules(component);

  /**
   * Memoized Form.Item props
   * 
   * Generates props for Ant Design Form.Item wrapper including
   * label, validation, help text, and styling configurations.
   */
  const formItemProps = useMemo(() => {
    if (!component) {
      return {};
    }

    return {
      // Basic form item props
      label: component.label,
      name: component.name || component.id,
      rules: validationRules,
      
      // Help and tooltip
      tooltip: component.tooltip,
      extra: component.extra,
      help: component.styling?.help,
      
      // Validation styling
      hasFeedback: component.styling?.hasFeedback,
      validateStatus: component.styling?.validateStatus,
      
      // Layout props
      labelAlign: component.styling?.labelAlign,
      labelCol: component.styling?.labelCol,
      wrapperCol: component.styling?.wrapperCol,
      
      // Behavior props
      preserve: component.styling?.preserve,
      hidden: component.styling?.hidden,
      
      // Custom form item props
      ...component.formItemProps,
    };
  }, [
    component?.label,
    component?.name,
    component?.id,
    component?.tooltip,
    component?.extra,
    component?.styling?.help,
    component?.styling?.hasFeedback,
    component?.styling?.validateStatus,
    component?.styling?.labelAlign,
    component?.styling?.labelCol,
    component?.styling?.wrapperCol,
    component?.styling?.preserve,
    component?.styling?.hidden,
    component?.formItemProps,
    validationRules,
  ]);

  return formItemProps;
};
