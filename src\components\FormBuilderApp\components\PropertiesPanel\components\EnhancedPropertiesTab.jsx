/**
 * @fileoverview Enhanced Properties Tab component
 *
 * This component provides an advanced properties interface with visual editors,
 * progressive disclosure, and real-time preview capabilities for enterprise
 * form builder usage.
 *
 * @module EnhancedPropertiesTab
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Form, Input, Switch, Select, Space, Divider, Button, Tooltip } from 'antd';
import {
  EyeOutlined,
  SettingOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined,
  LayoutOutlined,
  MobileOutlined,
  ExperimentOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import PropertyGroup from './VisualPropertyEditors/PropertyGroup';
import ColorPicker from './VisualPropertyEditors/ColorPicker';
import { PROPERTY_CATEGORIES } from './VisualPropertyEditors/constants/editorConstants';

const { Option } = Select;

/**
 * Enhanced Properties Tab component
 *
 * @param {Object} props - Component props
 * @param {Object} props.componentData - Current component data
 * @param {string} props.componentId - Component ID
 * @param {Function} props.onPropertyUpdate - Property update callback
 * @param {Object} props.formSchema - Current form schema for context
 * @returns {React.ReactNode} Enhanced properties tab JSX
 */
const EnhancedPropertiesTab = ({
  componentData,
  componentId,
  onPropertyUpdate,
  formSchema,
}) => {
  const [form] = Form.useForm();
  const [expandedGroups, setExpandedGroups] = useState({
    basic: true,
    appearance: false,
    layout: false,
    typography: false,
    responsive: false,
  });

  /**
   * Handle property change with real-time updates
   */
  const handlePropertyChange = useCallback((property, value) => {
    if (onPropertyUpdate) {
      onPropertyUpdate(componentId, property, value);
    }
    
    // Update form values
    form.setFieldsValue({ [property]: value });
  }, [componentId, onPropertyUpdate, form]);

  /**
   * Handle group expand/collapse
   */
  const handleGroupExpand = useCallback((groupKey, expanded) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupKey]: expanded,
    }));
  }, []);

  /**
   * Get component-specific properties
   */
  const componentProperties = useMemo(() => {
    const baseProperties = {
      label: componentData?.label || '',
      placeholder: componentData?.placeholder || '',
      required: componentData?.required || false,
      disabled: componentData?.disabled || false,
      hidden: componentData?.hidden || false,
    };

    // Add component-specific properties
    switch (componentData?.type) {
      case 'input':
        return {
          ...baseProperties,
          maxLength: componentData?.maxLength || undefined,
          allowClear: componentData?.allowClear || false,
        };
      case 'select':
        return {
          ...baseProperties,
          multiple: componentData?.multiple || false,
          showSearch: componentData?.showSearch || false,
          options: componentData?.options || [],
        };
      case 'textarea':
        return {
          ...baseProperties,
          rows: componentData?.rows || 4,
          autoSize: componentData?.autoSize || false,
        };
      default:
        return baseProperties;
    }
  }, [componentData]);

  /**
   * Get style properties
   */
  const styleProperties = useMemo(() => ({
    backgroundColor: componentData?.style?.backgroundColor || '',
    color: componentData?.style?.color || '',
    borderColor: componentData?.style?.borderColor || '',
    borderRadius: componentData?.style?.borderRadius || '',
    padding: componentData?.style?.padding || '',
    margin: componentData?.style?.margin || '',
    fontSize: componentData?.style?.fontSize || '',
    fontWeight: componentData?.style?.fontWeight || '',
    textAlign: componentData?.style?.textAlign || '',
  }), [componentData?.style]);

  return (
    <div style={{ padding: '16px 0' }}>
      <Form
        form={form}
        layout="vertical"
        size="small"
        initialValues={{
          ...componentProperties,
          ...styleProperties,
        }}
      >
        {/* Basic Properties */}
        <PropertyGroup
          title="Basic Properties"
          icon={<SettingOutlined />}
          description="Essential component configuration"
          defaultExpanded={expandedGroups.basic}
          onExpand={(expanded) => handleGroupExpand('basic', expanded)}
          propertyCount={Object.keys(componentProperties).length}
        >
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <Form.Item
              label="Label"
              name="label"
              tooltip="Display label for this component"
            >
              <Input
                placeholder="Enter component label"
                onChange={(e) => handlePropertyChange('label', e.target.value)}
              />
            </Form.Item>

            {componentData?.type !== 'button' && (
              <Form.Item
                label="Placeholder"
                name="placeholder"
                tooltip="Placeholder text shown when field is empty"
              >
                <Input
                  placeholder="Enter placeholder text"
                  onChange={(e) => handlePropertyChange('placeholder', e.target.value)}
                />
              </Form.Item>
            )}

            <Space size="large" style={{ width: '100%' }}>
              <Form.Item
                label="Required"
                name="required"
                valuePropName="checked"
                tooltip="Make this field required for form submission"
              >
                <Switch
                  onChange={(checked) => handlePropertyChange('required', checked)}
                />
              </Form.Item>

              <Form.Item
                label="Disabled"
                name="disabled"
                valuePropName="checked"
                tooltip="Disable user interaction with this component"
              >
                <Switch
                  onChange={(checked) => handlePropertyChange('disabled', checked)}
                />
              </Form.Item>

              <Form.Item
                label="Hidden"
                name="hidden"
                valuePropName="checked"
                tooltip="Hide this component from view"
              >
                <Switch
                  onChange={(checked) => handlePropertyChange('hidden', checked)}
                />
              </Form.Item>
            </Space>
          </Space>
        </PropertyGroup>

        {/* Appearance Properties */}
        <PropertyGroup
          title="Appearance"
          icon={<BgColorsOutlined />}
          description="Visual styling and colors"
          defaultExpanded={expandedGroups.appearance}
          onExpand={(expanded) => handleGroupExpand('appearance', expanded)}
          propertyCount={3}
        >
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <ColorPicker
              label="Background Color"
              value={styleProperties.backgroundColor}
              onChange={(color) => handlePropertyChange('style.backgroundColor', color)}
              showPresets={true}
              placeholder="Select background color"
            />

            <ColorPicker
              label="Text Color"
              value={styleProperties.color}
              onChange={(color) => handlePropertyChange('style.color', color)}
              showPresets={true}
              placeholder="Select text color"
            />

            <ColorPicker
              label="Border Color"
              value={styleProperties.borderColor}
              onChange={(color) => handlePropertyChange('style.borderColor', color)}
              showPresets={true}
              placeholder="Select border color"
            />
          </Space>
        </PropertyGroup>

        {/* Typography Properties */}
        <PropertyGroup
          title="Typography"
          icon={<FontSizeOutlined />}
          description="Text styling and formatting"
          defaultExpanded={expandedGroups.typography}
          onExpand={(expanded) => handleGroupExpand('typography', expanded)}
          propertyCount={3}
        >
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <Form.Item
              label="Font Size"
              name="fontSize"
              tooltip="Text size in pixels"
            >
              <Select
                placeholder="Select font size"
                onChange={(value) => handlePropertyChange('style.fontSize', value)}
                allowClear
              >
                <Option value="12px">12px - Small</Option>
                <Option value="14px">14px - Default</Option>
                <Option value="16px">16px - Medium</Option>
                <Option value="18px">18px - Large</Option>
                <Option value="20px">20px - Extra Large</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="Font Weight"
              name="fontWeight"
              tooltip="Text weight/boldness"
            >
              <Select
                placeholder="Select font weight"
                onChange={(value) => handlePropertyChange('style.fontWeight', value)}
                allowClear
              >
                <Option value="300">300 - Light</Option>
                <Option value="400">400 - Normal</Option>
                <Option value="500">500 - Medium</Option>
                <Option value="600">600 - Semi Bold</Option>
                <Option value="700">700 - Bold</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="Text Alignment"
              name="textAlign"
              tooltip="Text alignment within the component"
            >
              <Select
                placeholder="Select text alignment"
                onChange={(value) => handlePropertyChange('style.textAlign', value)}
                allowClear
              >
                <Option value="left">Left</Option>
                <Option value="center">Center</Option>
                <Option value="right">Right</Option>
                <Option value="justify">Justify</Option>
              </Select>
            </Form.Item>
          </Space>
        </PropertyGroup>

        {/* Layout Properties */}
        <PropertyGroup
          title="Layout & Spacing"
          icon={<LayoutOutlined />}
          description="Positioning and spacing controls"
          defaultExpanded={expandedGroups.layout}
          onExpand={(expanded) => handleGroupExpand('layout', expanded)}
          propertyCount={3}
        >
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <Form.Item
              label="Padding"
              name="padding"
              tooltip="Internal spacing around content"
            >
              <Input
                placeholder="e.g., 16px or 8px 16px"
                onChange={(e) => handlePropertyChange('style.padding', e.target.value)}
              />
            </Form.Item>

            <Form.Item
              label="Margin"
              name="margin"
              tooltip="External spacing around component"
            >
              <Input
                placeholder="e.g., 16px or 8px 16px"
                onChange={(e) => handlePropertyChange('style.margin', e.target.value)}
              />
            </Form.Item>

            <Form.Item
              label="Border Radius"
              name="borderRadius"
              tooltip="Corner rounding in pixels"
            >
              <Select
                placeholder="Select border radius"
                onChange={(value) => handlePropertyChange('style.borderRadius', value)}
                allowClear
              >
                <Option value="0px">0px - Square</Option>
                <Option value="4px">4px - Small</Option>
                <Option value="8px">8px - Medium</Option>
                <Option value="12px">12px - Large</Option>
                <Option value="50%">50% - Round</Option>
              </Select>
            </Form.Item>
          </Space>
        </PropertyGroup>

        {/* Preview Section */}
        <div style={{ marginTop: '24px', padding: '16px', background: '#f9f9f9', borderRadius: '8px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
            <EyeOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontWeight: 500, fontSize: '14px' }}>Live Preview</span>
          </div>
          <div style={{ 
            padding: '12px', 
            background: '#ffffff', 
            borderRadius: '6px',
            border: '1px solid #e8e8e8',
            minHeight: '60px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#666',
            fontSize: '12px'
          }}>
            Component preview would appear here with applied styles
          </div>
        </div>
      </Form>
    </div>
  );
};

export default EnhancedPropertiesTab;
