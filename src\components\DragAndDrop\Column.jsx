import { useRef, memo, useMemo, useCallback, Fragment } from 'react';
import { useDrag } from 'react-dnd';
import {
  COLUMN,
  TAB_CONTAINER,
  CARD_CONTAINER,
  FORM_SECTION,
  ACCORDION_CONTAINER,
  STEPS_CONTAINER,
  GRID_CONTAINER,
  FLEX_CONTAINER,
} from '../../constants';
import DropZone from './DropZone';
import Component from './Component';
import * as S from '../../styles';
import { useDoubleClickHandler } from '../FormBuilderApp/components/PropertiesPanel';

// Memoized Column component for better performance
const Column = memo(
  ({ data, components, handleDrop, path, onUpdateComponent }) => {
    const ref = useRef(null);

    // Double-click handler for properties panel
    const { getContainerDoubleClickProps } = useDoubleClickHandler({
      componentData: data,
      componentId: data?.id,
      components,
    });

    // Memoized drag item to prevent unnecessary re-creations - using optional chaining
    const dragItem = useMemo(
      () => ({
        id: data?.id,
        type: COLUMN,
        children: data?.children || [],
        path,
      }),
      [data?.id, data?.children, path],
    );

    // Memoized component renderer for better performance
    const renderComponent = useCallback(
      (component, currentPath) => {
        return (
          <Component
            key={component.id}
            data={component}
            components={components}
            path={currentPath}
            handleDrop={handleDrop}
            onUpdateComponent={onUpdateComponent}
          />
        );
      },
      [components, handleDrop, onUpdateComponent],
    );

    const [{ isDragging }, drag] = useDrag({
      type: COLUMN,
      item: dragItem,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    // Memoized style to prevent unnecessary re-computations
    const columnStyle = useMemo(
      () => ({
        opacity: isDragging ? 0 : 1,
      }),
      [isDragging],
    );

    // Memoized className to prevent unnecessary re-computations
    const className = useMemo(
      () => `draggable ${isDragging ? 'dragging' : ''}`,
      [isDragging],
    );

    // Ensure children is always an array - using optional chaining
    const children = data?.children || [];

    // Container component types for enhanced drop zone styling
    const containerTypes = [
      TAB_CONTAINER,
      CARD_CONTAINER,
      FORM_SECTION,
      ACCORDION_CONTAINER,
      STEPS_CONTAINER,
      GRID_CONTAINER,
      FLEX_CONTAINER,
    ];

    // Helper function to check if a component is a container
    const isContainerComponent = useCallback(
      (component) => containerTypes.includes(component?.type),
      [containerTypes],
    );

    // Memoized last drop zone data
    const lastDropZoneData = useMemo(
      () => ({
        path: `${path}-${children.length}`,
        childrenCount: children.length,
      }),
      [path, children.length],
    );

    // Add safety check for data after all hooks
    if (!data || !data.id) {
      console.error('Column component received invalid data:', data);
      return null;
    }

    drag(ref);

    return (
      <S.Column
        ref={ref}
        style={columnStyle}
        className={className}
        {...getContainerDoubleClickProps()}
      >
        {children.map((component, index) => {
          const currentPath = `${path}-${index}`;
          const isContainer = isContainerComponent(component);
          const prevComponent = index > 0 ? children[index - 1] : null;
          const isPrevContainer = isContainerComponent(prevComponent);

          // Memoized drop zone data for each component
          // CRITICAL FIX: Detect if Column is inside StepsContainer and add container info
          const dropZoneData = {
            path: currentPath,
            childrenCount: children.length,
          };

          // If path contains -step-, extract container information for advanced handler
          if (path && path.includes('-step-')) {
            const pathSegments = path.split('-');
            const stepIndex = pathSegments.findIndex(
              (segment) => segment === 'step',
            );

            if (stepIndex !== -1 && stepIndex < pathSegments.length - 1) {
              // Extract container path (everything before -step-)
              const containerPath = pathSegments.slice(0, stepIndex).join('-');
              const stepId = pathSegments[stepIndex + 1];

              // Add container information to trigger advanced handler
              dropZoneData.containerId = containerPath.match(/^\d+$/)
                ? `layout_index_${containerPath}`
                : containerPath;
              dropZoneData.stepId = stepId;
              dropZoneData.containerType = 'step-content';

              console.log(
                '🔄 [Column] Added container info for StepsContainer drop:',
                {
                  originalPath: path,
                  currentPath,
                  containerId: dropZoneData.containerId,
                  stepId: dropZoneData.stepId,
                },
              );
            }
          }

          // Add container-adjacent class if this or previous component is a container
          const dropZoneClassName =
            isContainer || isPrevContainer ? 'container-adjacent' : '';

          return (
            <Fragment key={component.id}>
              <DropZone
                data={dropZoneData}
                onDrop={handleDrop}
                className={dropZoneClassName}
              />
              {renderComponent(component, currentPath)}
            </Fragment>
          );
        })}

        {/* Enhanced last drop zone - check if last component is a container */}
        <DropZone
          data={lastDropZoneData}
          onDrop={handleDrop}
          isLast
          className={
            children.length > 0 &&
            isContainerComponent(children[children.length - 1])
              ? 'container-adjacent'
              : ''
          }
        />
      </S.Column>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function to ensure re-render when path changes - avoid JSON.stringify
    if (
      prevProps.data?.id !== nextProps.data?.id ||
      prevProps.path !== nextProps.path ||
      prevProps.handleDrop !== nextProps.handleDrop ||
      prevProps.components !== nextProps.components ||
      prevProps.onUpdateComponent !== nextProps.onUpdateComponent
    ) {
      return false;
    }

    // Compare children array length and IDs only (shallow comparison)
    const prevChildren = prevProps.data?.children || [];
    const nextChildren = nextProps.data?.children || [];

    if (prevChildren.length !== nextChildren.length) {
      return false;
    }

    // Compare children IDs and types only (avoid deep comparison)
    for (let i = 0; i < prevChildren.length; i++) {
      if (
        prevChildren[i]?.id !== nextChildren[i]?.id ||
        prevChildren[i]?.type !== nextChildren[i]?.type
      ) {
        return false;
      }
    }

    return true;
  },
);

// Set display name for debugging
Column.displayName = 'Column';

export default Column;
