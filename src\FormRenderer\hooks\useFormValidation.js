import { useCallback } from 'react';

/**
 * Custom hook for form validation handling
 * 
 * Provides utilities for managing form validation, field changes,
 * and validation state across the form lifecycle.
 * 
 * @param {Function} onValuesChange - Optional callback for when form values change
 * @returns {Object} Form validation utilities
 * @returns {Function} returns.handleValuesChange - Function to handle form value changes
 */
export const useFormValidation = (onValuesChange) => {
  /**
   * Handles form values change with optional callback
   * 
   * This function is called whenever any form field value changes.
   * It provides a centralized place to handle form state changes,
   * validation triggers, and external callbacks.
   * 
   * @param {Object} changedValues - The changed form values
   * @param {Object} allValues - All current form values
   */
  const handleValuesChange = useCallback(
    (changedValues, allValues) => {
      // Call external callback if provided
      if (onValuesChange) {
        onValuesChange(changedValues, allValues);
      }

      // Additional validation logic can be added here
      // For example: real-time validation, dependent field updates, etc.
    },
    [onValuesChange]
  );

  return {
    handleValuesChange,
  };
};
