import styled from "styled-components";
import { colors, elevation, motionCurves, fluentTrashPulse } from "../theme";

// Microsoft Fluent Design Trash drop zone
export const TrashDropZone = styled.div`
  position: fixed;
  bottom: 24px;
  right: 24px;
  text-align: center;
  padding: 16px;
  width: 80px;
  height: 80px;
  border: 2px dashed ${colors.borderInactive};
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${colors.background};
  box-shadow: ${elevation.depth8};
  transition: all 0.15s ${motionCurves.decelerate};
  cursor: pointer;
  z-index: 1000;
  font-family: 'Segoe UI', sans-serif;

  /* Fluent Design acrylic effect */
  backdrop-filter: blur(20px);
  background: ${colors.backgroundOverlay};

  &:before {
    content: "🗑️";
    font-size: 24px;
    transition: all 0.15s ${motionCurves.decelerate};
  }

  /* Microsoft Fluent Design hover effect */
  &:hover {
    border-color: ${colors.error};
    background: ${colors.errorLight};
    transform: translateY(-2px) scale(1.05);
    box-shadow: ${elevation.depth16};

    &:before {
      transform: scale(1.1);
    }
  }

  /* Microsoft Fluent Design active state */
  &.active {
    background: ${colors.error};
    border-color: ${colors.error};
    color: white;
    transform: scale(1.1) translateY(-4px);
    box-shadow: ${elevation.depth64};
    transition: all 0.1s ${motionCurves.accelerate};

    /* Fluent Design breathing animation */
    animation: ${fluentTrashPulse} 1.5s ${motionCurves.decelerate} infinite;

    &:before {
      content: "🗑️";
      filter: brightness(0) invert(1);
      transform: scale(1.3);
    }

    &::after {
      content: "Drop to delete";
      position: absolute;
      bottom: -28px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 11px;
      color: ${colors.error};
      font-weight: 600;
      background: ${colors.background};
      padding: 4px 12px;
      border-radius: 4px;
      white-space: nowrap;
      box-shadow: ${elevation.depth4};
      border: 1px solid ${colors.borderLight};
      font-family: 'Segoe UI', sans-serif;
      letter-spacing: 0.3px;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    bottom: 20px;
    right: 20px;
    width: 64px;
    height: 64px;
    padding: 12px;

    &:before {
      font-size: 20px;
    }

    &.active::after {
      font-size: 10px;
      padding: 3px 8px;
      bottom: -24px;
    }
  }

  /* Microsoft Fluent Design focus ring */
  &:focus {
    outline: 2px solid ${colors.accent};
    outline-offset: 2px;
  }
`;
