import React, { useState, useCallback, useMemo, useRef } from 'react';
import { useDrag } from 'react-dnd';
import { Collapse, Button, Form, Input, Modal } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { ACCORDION_CONTAINER } from '../../../constants';
import DropZone from '../DropZone';
import Component from '../Component';
import styled from 'styled-components';

// Styled component for inline editable text
const InlineEditableText = styled.span`
  .editable-text {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: inline-block;
    min-width: 20px;

    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
      border: 1px solid rgba(24, 144, 255, 0.3);
    }

    &.placeholder-text {
      color: #999;
      font-style: italic;
    }
  }

  .editing-input {
    border: 1px solid #1890ff;
    border-radius: 3px;
    padding: 2px 4px;
    outline: none;
    background: white;
    box-shadow: 0 0 4px rgba(24, 144, 255, 0.3);
    min-width: 100px;
  }
`;

// const { Panel } = Collapse; // Not used in current implementation

// Memoized AccordionContainer component for advanced nested drag-and-drop
const AccordionContainer = React.memo(
  ({ data, components, handleDrop, path, onUpdateComponent }) => {
    const ref = useRef(null);
    const [activeKey, setActiveKey] = useState(['1']);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [editingPanel, setEditingPanel] = useState(null);
    const [form] = Form.useForm();

    // Inline editing state
    const [isEditing, setIsEditing] = useState(false);
    const [editingValue, setEditingValue] = useState('');
    const [originalValue, setOriginalValue] = useState('');
    const [editingPanelIndex, setEditingPanelIndex] = useState(null);
    const [editingProperty, setEditingProperty] = useState(null);

    // Memoized drag item
    const dragItem = useMemo(
      () => ({
        id: data?.id,
        type: ACCORDION_CONTAINER,
        panels: data?.panels || [],
        path: path || `accordion-${data?.id}`,
      }),
      [data?.id, data?.panels, path],
    );

    // Memoized component renderer
    const renderComponent = useCallback(
      (component, currentPath) => {
        return (
          <Component
            key={component.id}
            data={component}
            components={components}
            path={currentPath}
            handleDrop={handleDrop}
            onUpdateComponent={onUpdateComponent}
          />
        );
      },
      [components, handleDrop, onUpdateComponent],
    );

    const [{ isDragging }, drag] = useDrag({
      type: ACCORDION_CONTAINER,
      item: dragItem,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    // Memoized style
    const containerStyle = useMemo(
      () => ({
        opacity: isDragging ? 0.5 : 1,
        margin: '8px 0',
        border: isDragging ? '2px dashed #1890ff' : '1px solid #d9d9d9',
        borderRadius: '6px',
        background: '#fff',
      }),
      [isDragging],
    );

    // Get panels from component registry (prioritize for inline edits)
    const component = components[data.id] || {};
    const panels = component.panels || data.panels || [];

    console.log('🔄 [AccordionContainer Builder] Data sync check:', {
      containerId: data.id,
      componentPanels: component.panels?.length || 0,
      dataPanels: data.panels?.length || 0,
      usingPanels: panels.length,
    });

    // Panel management functions
    const handleAddPanel = useCallback(() => {
      const newPanel = {
        id: `panel_${Date.now()}`,
        key: `panel_${Date.now()}`,
        header: `Panel ${(panels?.length || 0) + 1}`,
        children: [],
      };

      const updatedPanels = [...panels, newPanel];
      onUpdateComponent?.(data.id, { panels: updatedPanels });
      setActiveKey([...activeKey, newPanel.key]);
    }, [data.id, panels, activeKey, onUpdateComponent]);

    const handleEditPanel = useCallback(
      (panel) => {
        setEditingPanel(panel);
        form.setFieldsValue({ header: panel.header });
        setIsEditModalVisible(true);
      },
      [form],
    );

    const handleDeletePanel = useCallback(
      (panelKey) => {
        if (panels.length <= 1) return; // Keep at least one panel

        const updatedPanels = panels.filter((panel) => panel.key !== panelKey);
        onUpdateComponent?.(data.id, { panels: updatedPanels });
        setActiveKey(activeKey.filter((key) => key !== panelKey));
      },
      [data.id, panels, activeKey, onUpdateComponent],
    );

    const handleSavePanel = useCallback(() => {
      form.validateFields().then((values) => {
        const updatedPanels = panels.map((panel) =>
          panel.id === editingPanel.id
            ? { ...panel, header: values.header }
            : panel,
        );
        onUpdateComponent?.(data.id, { panels: updatedPanels });
        setIsEditModalVisible(false);
        setEditingPanel(null);
        form.resetFields();
      });
    }, [form, panels, editingPanel, data.id, onUpdateComponent]);

    // Inline editing functions
    const handleLabelClick = useCallback(
      (e, currentText, panelIndex, property) => {
        e.stopPropagation();
        setIsEditing(true);
        setEditingValue(currentText || '');
        setOriginalValue(currentText || '');
        setEditingPanelIndex(panelIndex);
        setEditingProperty(property);
      },
      [],
    );

    const handleEditingKeyDown = useCallback((e) => {
      if (e.key === 'Enter') {
        handleEditingSave();
      } else if (e.key === 'Escape') {
        handleEditingCancel();
      }
    }, []);

    const handleEditingBlur = useCallback(() => {
      handleEditingSave();
    }, []);

    const handleEditingSave = useCallback(() => {
      if (
        onUpdateComponent &&
        editingValue !== originalValue &&
        editingPanelIndex !== null &&
        editingProperty
      ) {
        const updatedPanels = panels.map((panel, index) =>
          index === editingPanelIndex
            ? { ...panel, [editingProperty]: editingValue }
            : panel,
        );
        onUpdateComponent(data.id, { panels: updatedPanels });
      }
      setIsEditing(false);
      setEditingValue('');
      setOriginalValue('');
      setEditingPanelIndex(null);
      setEditingProperty(null);
    }, [
      onUpdateComponent,
      data.id,
      editingValue,
      originalValue,
      editingPanelIndex,
      editingProperty,
      panels,
    ]);

    const handleEditingCancel = useCallback(() => {
      setIsEditing(false);
      setEditingValue('');
      setOriginalValue('');
      setEditingPanelIndex(null);
      setEditingProperty(null);
    }, []);

    // Helper function to render editable text
    const renderEditableText = useCallback(
      (text, panelIndex, property, placeholder = 'Click to edit') => {
        const displayText = text || placeholder;
        const isPlaceholder = !text;
        const isCurrentlyEditing =
          isEditing &&
          editingPanelIndex === panelIndex &&
          editingProperty === property;

        if (isCurrentlyEditing) {
          return (
            <input
              className='editing-input'
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              onKeyDown={handleEditingKeyDown}
              onBlur={handleEditingBlur}
              autoFocus
              style={{
                fontSize: 'inherit',
                fontFamily: 'inherit',
                fontWeight: 'inherit',
                color: 'inherit',
              }}
            />
          );
        }

        return (
          <InlineEditableText>
            <span
              className={`editable-text ${
                isPlaceholder ? 'placeholder-text' : ''
              }`}
              onClick={(e) => handleLabelClick(e, text, panelIndex, property)}
              title='Click to edit'
            >
              {displayText}
            </span>
          </InlineEditableText>
        );
      },
      [
        isEditing,
        editingValue,
        editingPanelIndex,
        editingProperty,
        handleEditingKeyDown,
        handleEditingBlur,
        handleLabelClick,
      ],
    );

    // Render panel content with nested support
    const renderPanelContent = useCallback(
      (panel, panelIndex) => {
        const children = panel.children || [];

        return (
          <div style={{ minHeight: '100px', padding: '8px' }}>
            {/* Check if we need to render as columns */}
            {children.some((child) => child.type === 'row') ? (
              // Render rows directly
              children.map((component, index) => {
                const currentPath = `${path}-panel-${panelIndex}-${index}`;

                const dropZoneData = {
                  path: currentPath,
                  childrenCount: children.length,
                  containerId: data.id,
                  panelId: panel.id,
                  containerType: 'accordion-panel',
                };

                return (
                  <React.Fragment key={component.id}>
                    <DropZone
                      data={dropZoneData}
                      onDrop={handleDrop}
                      className='accordion-panel-drop-zone'
                    />
                    {renderComponent(component, currentPath, panel.id)}
                  </React.Fragment>
                );
              })
            ) : (
              // Render as horizontal columns
              <div style={{ display: 'flex', gap: '8px', minHeight: '100px' }}>
                {children.map((component, index) => {
                  const currentPath = `${path}-panel-${panelIndex}-${index}`;

                  const dropZoneData = {
                    path: currentPath,
                    childrenCount: children.length,
                    containerId: data.id,
                    panelId: panel.id,
                    containerType: 'accordion-panel',
                  };

                  return (
                    <React.Fragment key={component.id}>
                      {/* Horizontal drop zone for column creation */}
                      <DropZone
                        data={dropZoneData}
                        onDrop={handleDrop}
                        className='horizontalDrag'
                        style={{ minHeight: '100px', width: '20px' }}
                      />
                      {/* Column content */}
                      <div
                        style={{
                          flex: 1,
                          minHeight: '100px',
                          border: '1px dashed #d9d9d9',
                          borderRadius: '4px',
                          padding: '8px',
                        }}
                      >
                        {renderComponent(component, currentPath, panel.id)}
                      </div>
                    </React.Fragment>
                  );
                })}
                {/* Final horizontal drop zone */}
                <DropZone
                  data={{
                    path: `${path}-panel-${panelIndex}-${children.length}`,
                    childrenCount: children.length,
                    containerId: data.id,
                    panelId: panel.id,
                    containerType: 'accordion-panel',
                  }}
                  onDrop={handleDrop}
                  className='horizontalDrag'
                  style={{ minHeight: '100px', width: '20px' }}
                  isLast
                />
              </div>
            )}

            {/* Empty state */}
            {children.length === 0 && (
              <DropZone
                data={{
                  path: `${path}-panel-${panelIndex}-0`,
                  childrenCount: 0,
                  containerId: data.id,
                  panelId: panel.id,
                  containerType: 'accordion-panel',
                }}
                onDrop={handleDrop}
                className='empty-panel-drop-zone'
                style={{
                  minHeight: '100px',
                  border: '2px dashed #d9d9d9',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999',
                }}
              >
                Drop components here
              </DropZone>
            )}
          </div>
        );
      },
      [path, data.id, handleDrop, renderComponent],
    );

    // Memoized panel items for Ant Design Collapse
    const panelItems = useMemo(() => {
      return panels.map((panel, index) => ({
        key: panel.key,
        label: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {renderEditableText(
              panel.header,
              index,
              'header',
              `Panel ${index + 1}`,
            )}
            <div style={{ display: 'flex', gap: '4px' }}>
              <Button
                type='text'
                size='small'
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditPanel(panel);
                }}
                style={{ padding: '0 4px' }}
              />
              {panels.length > 1 && (
                <Button
                  type='text'
                  size='small'
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeletePanel(panel.key);
                  }}
                  style={{ padding: '0 4px', color: '#ff4d4f' }}
                />
              )}
            </div>
          </div>
        ),
        children: renderPanelContent(panel, index),
      }));
    }, [
      panels,
      handleEditPanel,
      handleDeletePanel,
      renderPanelContent,
      renderEditableText,
    ]);

    drag(ref);

    return (
      <div ref={ref} style={containerStyle}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '8px 16px',
            background: '#fafafa',
            borderBottom: '1px solid #d9d9d9',
          }}
        >
          <span style={{ fontWeight: '500', color: '#666' }}>
            Accordion Container
          </span>
          <Button
            type='dashed'
            size='small'
            icon={<PlusOutlined />}
            onClick={handleAddPanel}
          >
            Add Panel
          </Button>
        </div>

        <Collapse
          activeKey={activeKey}
          onChange={setActiveKey}
          items={panelItems}
          style={{ border: 'none' }}
        />

        {/* Edit Panel Modal */}
        <Modal
          title='Edit Panel'
          open={isEditModalVisible}
          onOk={handleSavePanel}
          onCancel={() => {
            setIsEditModalVisible(false);
            setEditingPanel(null);
            form.resetFields();
          }}
          width={400}
        >
          <Form form={form} layout='vertical'>
            <Form.Item
              name='header'
              label='Panel Header'
              rules={[{ required: true, message: 'Please enter panel header' }]}
            >
              <Input placeholder='Enter panel header...' />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance - avoid JSON.stringify for deep comparison
    if (
      prevProps.data?.id !== nextProps.data?.id ||
      prevProps.path !== nextProps.path ||
      prevProps.handleDrop !== nextProps.handleDrop ||
      prevProps.onUpdateComponent !== nextProps.onUpdateComponent ||
      prevProps.components !== nextProps.components
    ) {
      return false;
    }

    // Compare panels array length and basic properties only (shallow comparison)
    const prevPanels = prevProps.data?.panels || [];
    const nextPanels = nextProps.data?.panels || [];

    if (prevPanels.length !== nextPanels.length) {
      return false;
    }

    // Compare panel IDs and headers only (avoid deep comparison)
    for (let i = 0; i < prevPanels.length; i++) {
      if (
        prevPanels[i]?.id !== nextPanels[i]?.id ||
        prevPanels[i]?.key !== nextPanels[i]?.key ||
        prevPanels[i]?.header !== nextPanels[i]?.header ||
        prevPanels[i]?.children?.length !== nextPanels[i]?.children?.length
      ) {
        return false;
      }
    }

    return true;
  },
);

AccordionContainer.displayName = 'AccordionContainer';

export default AccordionContainer;
