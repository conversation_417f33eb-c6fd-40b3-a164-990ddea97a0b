import { useMemo } from 'react';
import { Form } from 'antd';

/**
 * Custom hook for memoizing form props and configurations
 * 
 * Optimizes form rendering performance by memoizing expensive prop calculations
 * and preventing unnecessary re-renders when props haven't changed.
 * 
 * @param {Object} config - Form configuration object
 * @param {Object} config.initialValues - Initial form values
 * @param {Object} config.formProps - Additional form props
 * @param {Function} config.handleSubmit - Form submission handler
 * @param {Function} config.handleFinishFailed - Form validation failure handler
 * @param {Function} config.handleValuesChange - Form values change handler
 * @param {Object} config.buttonProps - Button configuration props
 * @param {boolean} config.loading - Loading state for submit button
 * @returns {Object} Memoized form props and configurations
 */
export const useFormProps = ({
  initialValues = {},
  formProps = {},
  handleSubmit,
  handleFinishFailed,
  handleValuesChange,
  buttonProps = {},
  loading = false,
}) => {
  const [form] = Form.useForm();

  /**
   * Memoized form props to prevent unnecessary re-renders
   * 
   * Combines default form configuration with user-provided props
   * and memoizes the result for optimal performance.
   */
  const defaultFormProps = useMemo(
    () => ({
      form,
      layout: 'vertical',
      initialValues,
      onFinish: handleSubmit,
      onFinishFailed: handleFinishFailed,
      onValuesChange: handleValuesChange,
      autoComplete: 'off',
      ...formProps,
    }),
    [
      form,
      initialValues,
      handleSubmit,
      handleFinishFailed,
      handleValuesChange,
      formProps,
    ]
  );

  /**
   * Memoized form item style for consistent spacing
   */
  const formItemStyle = useMemo(
    () => ({
      marginTop: '24px',
      marginBottom: 0,
    }),
    []
  );

  /**
   * Memoized submit button props
   */
  const submitButtonProps = useMemo(
    () => ({
      type: 'primary',
      htmlType: 'submit',
      loading,
      ...buttonProps.submit,
    }),
    [loading, buttonProps.submit]
  );

  /**
   * Memoized reset button props
   */
  const resetButtonProps = useMemo(
    () => ({
      ...buttonProps.reset,
    }),
    [buttonProps.reset]
  );

  return {
    form,
    defaultFormProps,
    formItemStyle,
    submitButtonProps,
    resetButtonProps,
  };
};
