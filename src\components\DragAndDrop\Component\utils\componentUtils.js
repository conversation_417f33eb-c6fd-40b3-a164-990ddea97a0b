/**
 * Component Utilities
 * 
 * Utility functions for component type checking, validation, and information extraction.
 * Provides helper functions for determining component behavior and rendering requirements.
 * 
 * Features:
 * - Component type classification and validation
 * - Form component identification for proper labeling
 * - Container component detection for special handling
 * - Component information extraction and formatting
 * - Nested property access utilities
 * 
 * @module ComponentUtils
 */

import {
  TAB_CONTAINER,
  CARD_CONTAINER,
  FORM_SECTION,
  ACCORDION_CONTAINER,
  STEPS_CONTAINER,
  GRID_CONTAINER,
  FLEX_CONTAINER,
} from '../../../../constants';

/**
 * Form component types that should have Form.Item wrapper with labels
 * 
 * These components are typically used in forms and benefit from
 * Ant Design's Form.Item wrapper for consistent styling and validation.
 */
const FORM_COMPONENT_TYPES = [
  'input',
  'email',
  'textarea',
  'select',
  'radio',
  'checkbox',
  'inputnumber',
  'number',
  'password',
  'datepicker',
  'date',
  'rangepicker',
  'switch',
  'rate',
  'slider',
  'upload',
  'autocomplete',
  'cascader',
  'colorpicker',
  'mentions',
  'timepicker',
  'transfer',
  'treeselect',
];

/**
 * Container component types that can contain other components
 * 
 * These components have special rendering logic and can accept
 * dropped components from the sidebar or other containers.
 */
const CONTAINER_COMPONENT_TYPES = [
  TAB_CONTAINER,
  CARD_CONTAINER,
  FORM_SECTION,
  ACCORDION_CONTAINER,
  STEPS_CONTAINER,
  GRID_CONTAINER,
  FLEX_CONTAINER,
];

/**
 * Component type utilities
 */

/**
 * Determines if a component should have a Form.Item wrapper with label
 * 
 * Form components benefit from Ant Design's Form.Item wrapper which provides
 * consistent styling, validation display, and label positioning.
 * 
 * @param {string} componentType - The type of component to check
 * @returns {boolean} True if component should have Form.Item wrapper
 * 
 * @example
 * shouldHaveFormLabel('input') // returns true
 * shouldHaveFormLabel('button') // returns false
 */
export const shouldHaveFormLabel = (componentType) => {
  if (!componentType || typeof componentType !== 'string') {
    return false;
  }
  
  return FORM_COMPONENT_TYPES.includes(componentType.toLowerCase());
};

/**
 * Determines if a component is a container component
 * 
 * Container components have special rendering logic and can accept
 * dropped components. They require different handling in the drag-and-drop system.
 * 
 * @param {string} componentType - The type of component to check
 * @returns {boolean} True if component is a container type
 * 
 * @example
 * isContainerComponent('tabContainer') // returns true
 * isContainerComponent('input') // returns false
 */
export const isContainerComponent = (componentType) => {
  if (!componentType || typeof componentType !== 'string') {
    return false;
  }
  
  return CONTAINER_COMPONENT_TYPES.includes(componentType);
};

/**
 * Gets component information for display purposes
 * 
 * Extracts and formats component information including type and display label.
 * Handles fallback values for missing or invalid component data.
 * 
 * @param {Object} component - Component data object
 * @returns {Object} Object containing type and label information
 * 
 * @example
 * getComponentInfo({ type: 'input', label: 'Email' })
 * // returns { type: 'input', label: 'Email' }
 * 
 * getComponentInfo({ type: 'button' })
 * // returns { type: 'button', label: 'button' }
 */
export const getComponentInfo = (component) => {
  if (!component || typeof component !== 'object') {
    return { type: 'component', label: 'component' };
  }
  
  const type = component.type || 'component';
  const label = component.label || component.content || type;
  
  return { type, label };
};

/**
 * Nested property access utilities
 */

/**
 * Gets a nested property value from an object using dot notation
 * 
 * Safely accesses nested properties without throwing errors if
 * intermediate properties don't exist. Supports array access.
 * 
 * @param {Object} obj - Object to access property from
 * @param {string} propertyPath - Dot-separated property path (e.g., 'cardProps.title', 'options.0.label')
 * @returns {*} Property value or undefined if not found
 * 
 * @example
 * getNestedPropertyValue({ cardProps: { title: 'Card Title' } }, 'cardProps.title')
 * // returns 'Card Title'
 * 
 * getNestedPropertyValue({ options: [{ label: 'Option 1' }] }, 'options.0.label')
 * // returns 'Option 1'
 */
export const getNestedPropertyValue = (obj, propertyPath) => {
  if (!obj || typeof obj !== 'object' || !propertyPath) {
    return undefined;
  }
  
  if (!propertyPath.includes('.')) {
    return obj[propertyPath];
  }
  
  const parts = propertyPath.split('.');
  let value = obj;
  
  for (const part of parts) {
    if (value && typeof value === 'object') {
      value = value[part];
    } else {
      return undefined;
    }
  }
  
  return value;
};

/**
 * Builds a nested update object for component property updates
 * 
 * Creates the proper nested structure for updating component properties
 * using dot notation paths. Handles both object and array updates.
 * 
 * @param {Array} pathParts - Array of property path parts
 * @param {*} value - Value to set
 * @param {Object} currentValue - Current value at the parent level
 * @returns {Object} Nested update object
 * 
 * @example
 * buildNestedUpdate(['cardProps', 'title'], 'New Title', { cardProps: { title: 'Old' } })
 * // returns { cardProps: { title: 'New Title' } }
 */
export const buildNestedUpdate = (pathParts, value, currentValue) => {
  if (!Array.isArray(pathParts) || pathParts.length === 0) {
    return {};
  }
  
  if (pathParts.length === 1) {
    return { [pathParts[0]]: value };
  }

  const [first, ...rest] = pathParts;
  const currentNestedValue = currentValue?.[first];

  if (Array.isArray(currentNestedValue)) {
    // Handle array updates (like options.0.label)
    const index = parseInt(rest[0]);
    if (isNaN(index)) {
      return {};
    }
    
    const newArray = [...currentNestedValue];
    if (rest.length === 2) {
      // Direct property update in array item
      newArray[index] = {
        ...newArray[index],
        [rest[1]]: value,
      };
    } else {
      // Nested property update in array item
      newArray[index] = {
        ...newArray[index],
        ...buildNestedUpdate(rest.slice(1), value, newArray[index]),
      };
    }
    return { [first]: newArray };
  } else {
    // Handle object updates (like cardProps.title)
    return {
      [first]: {
        ...(currentNestedValue || {}),
        ...buildNestedUpdate(rest, value, currentNestedValue),
      },
    };
  }
};

/**
 * Component validation utilities
 */

/**
 * Validates component data structure
 * 
 * Checks if component data has required properties and valid structure.
 * Used for error handling and debugging.
 * 
 * @param {Object} componentData - Component data to validate
 * @returns {Object} Validation result with isValid flag and errors array
 * 
 * @example
 * validateComponentData({ id: '123', type: 'input' })
 * // returns { isValid: true, errors: [] }
 */
export const validateComponentData = (componentData) => {
  const errors = [];
  
  if (!componentData) {
    errors.push('Component data is null or undefined');
    return { isValid: false, errors };
  }
  
  if (!componentData.id) {
    errors.push('Component data missing required id property');
  }
  
  if (!componentData.type) {
    errors.push('Component data missing required type property');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Gets form item props for canvas display
 * 
 * Generates appropriate Form.Item props for displaying components
 * in the form builder canvas with proper labels and styling.
 * 
 * @param {Object} component - Component data
 * @param {Function} renderEditableText - Function to render editable text
 * @returns {Object} Form.Item props object
 */
export const getCanvasFormItemProps = (component, renderEditableText) => {
  if (!component) {
    return { style: { marginBottom: '8px' } };
  }
  
  return {
    label: renderEditableText
      ? renderEditableText(
          component.label || component.content,
          'label',
          `${component.type} Label`,
        )
      : component.label || component.content || `${component.type} Label`,
    style: { marginBottom: '8px' },
  };
};
