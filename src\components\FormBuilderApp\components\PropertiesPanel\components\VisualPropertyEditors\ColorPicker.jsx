/**
 * @fileoverview Advanced Color Picker component
 *
 * This component provides a comprehensive color picker with presets,
 * gradients, opacity control, and real-time preview capabilities.
 *
 * @module ColorPicker
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Button, Input, Slider, Space, Tooltip, Popover, Row, Col } from 'antd';
import {
  BgColorsOutlined,
  EyeDropperOutlined,
  ClearOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { COLOR_PRESETS } from './constants/editorConstants';

// Note: react-color would need to be installed for full functionality
// For now, we'll create a simplified color picker

/**
 * Styled components
 */
const ColorPickerContainer = styled.div`
  .color-preview {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: 2px solid #e8e8e8;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #1890ff;
    }

    .color-value {
      width: 100%;
      height: 100%;
      background: ${(props) => props.color || '#ffffff'};
    }

    .transparency-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: linear-gradient(45deg, #ccc 25%, transparent 25%),
        linear-gradient(-45deg, #ccc 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #ccc 75%),
        linear-gradient(-45deg, transparent 75%, #ccc 75%);
      background-size: 8px 8px;
      background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
      z-index: 0;
    }
  }
`;

const PresetGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  margin: 12px 0;

  .preset-color {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
      border-color: #1890ff;
    }
  }
`;

const GradientPreview = styled.div`
  width: 100%;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  background: ${(props) => props.gradient};
  cursor: pointer;
  margin-bottom: 8px;

  &:hover {
    border-color: #1890ff;
  }
`;

/**
 * Advanced Color Picker component
 *
 * @param {Object} props - Component props
 * @param {string} props.value - Current color value
 * @param {Function} props.onChange - Color change callback
 * @param {boolean} props.showAlpha - Whether to show alpha/opacity control
 * @param {boolean} props.showPresets - Whether to show color presets
 * @param {boolean} props.showGradients - Whether to show gradient presets
 * @param {string} props.label - Field label
 * @param {string} props.placeholder - Input placeholder
 * @returns {React.ReactNode} Color picker JSX
 */
const ColorPicker = ({
  value = '#ffffff',
  onChange,
  showAlpha = true,
  showPresets = true,
  showGradients = false,
  label,
  placeholder = 'Enter color value',
}) => {
  const [isPickerVisible, setIsPickerVisible] = useState(false);
  const [inputValue, setInputValue] = useState(value);

  /**
   * Parse color value to get RGB and alpha
   */
  const colorInfo = useMemo(() => {
    if (!value) return { r: 255, g: 255, b: 255, a: 1 };

    // Handle hex colors
    if (value.startsWith('#')) {
      const hex = value.slice(1);
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      const a = hex.length === 8 ? parseInt(hex.slice(6, 8), 16) / 255 : 1;
      return { r, g, b, a };
    }

    // Handle rgba colors
    const rgbaMatch = value.match(
      /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/,
    );
    if (rgbaMatch) {
      return {
        r: parseInt(rgbaMatch[1]),
        g: parseInt(rgbaMatch[2]),
        b: parseInt(rgbaMatch[3]),
        a: rgbaMatch[4] ? parseFloat(rgbaMatch[4]) : 1,
      };
    }

    return { r: 255, g: 255, b: 255, a: 1 };
  }, [value]);

  /**
   * Handle color change from picker
   */
  const handleColorChange = useCallback(
    (color) => {
      const { r, g, b, a } = color.rgb;
      const newValue =
        showAlpha && a < 1
          ? `rgba(${r}, ${g}, ${b}, ${a})`
          : `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;

      setInputValue(newValue);
      if (onChange) {
        onChange(newValue);
      }
    },
    [onChange, showAlpha],
  );

  /**
   * Handle input change
   */
  const handleInputChange = useCallback(
    (e) => {
      const newValue = e.target.value;
      setInputValue(newValue);

      // Validate and apply color
      if (
        newValue.match(/^#[0-9A-Fa-f]{6}$/) ||
        newValue.match(/^rgba?\(\d+,\s*\d+,\s*\d+(?:,\s*[\d.]+)?\)$/)
      ) {
        if (onChange) {
          onChange(newValue);
        }
      }
    },
    [onChange],
  );

  /**
   * Handle preset color selection
   */
  const handlePresetSelect = useCallback(
    (color) => {
      setInputValue(color);
      if (onChange) {
        onChange(color);
      }
      setIsPickerVisible(false);
    },
    [onChange],
  );

  /**
   * Copy color to clipboard
   */
  const handleCopyColor = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(value);
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy color:', err);
    }
  }, [value]);

  /**
   * Clear color
   */
  const handleClearColor = useCallback(() => {
    setInputValue('');
    if (onChange) {
      onChange('');
    }
  }, [onChange]);

  /**
   * Render color picker popover content
   */
  const renderPickerContent = useCallback(
    () => (
      <div style={{ width: '240px' }}>
        {/* Simplified color picker - would use SketchPicker in full implementation */}
        <div
          style={{
            padding: '12px',
            background: '#f5f5f5',
            borderRadius: '4px',
            marginBottom: '12px',
          }}
        >
          <Input
            type='color'
            value={value.startsWith('#') ? value : '#ffffff'}
            onChange={(e) => {
              const newColor = e.target.value;
              setInputValue(newColor);
              if (onChange) onChange(newColor);
            }}
            style={{ width: '100%', height: '32px' }}
          />
        </div>

        {showPresets && (
          <div style={{ marginTop: '12px' }}>
            <div
              style={{ fontSize: '12px', fontWeight: 500, marginBottom: '8px' }}
            >
              Color Presets
            </div>

            {/* Primary Colors */}
            <div style={{ marginBottom: '8px' }}>
              <div
                style={{ fontSize: '11px', color: '#666', marginBottom: '4px' }}
              >
                Primary
              </div>
              <PresetGrid>
                {COLOR_PRESETS.PRIMARY.map((color, index) => (
                  <Tooltip key={index} title={color}>
                    <div
                      className='preset-color'
                      style={{ background: color }}
                      onClick={() => handlePresetSelect(color)}
                    />
                  </Tooltip>
                ))}
              </PresetGrid>
            </div>

            {/* Semantic Colors */}
            <div style={{ marginBottom: '8px' }}>
              <div
                style={{ fontSize: '11px', color: '#666', marginBottom: '4px' }}
              >
                Semantic
              </div>
              <PresetGrid>
                {COLOR_PRESETS.SEMANTIC.map((color, index) => (
                  <Tooltip key={index} title={color}>
                    <div
                      className='preset-color'
                      style={{ background: color }}
                      onClick={() => handlePresetSelect(color)}
                    />
                  </Tooltip>
                ))}
              </PresetGrid>
            </div>

            {/* Neutral Colors */}
            <div>
              <div
                style={{ fontSize: '11px', color: '#666', marginBottom: '4px' }}
              >
                Neutral
              </div>
              <PresetGrid>
                {COLOR_PRESETS.NEUTRAL.slice(0, 10).map((color, index) => (
                  <Tooltip key={index} title={color}>
                    <div
                      className='preset-color'
                      style={{ background: color }}
                      onClick={() => handlePresetSelect(color)}
                    />
                  </Tooltip>
                ))}
              </PresetGrid>
            </div>
          </div>
        )}

        {showGradients && (
          <div style={{ marginTop: '12px' }}>
            <div
              style={{ fontSize: '12px', fontWeight: 500, marginBottom: '8px' }}
            >
              Gradient Presets
            </div>
            {COLOR_PRESETS.GRADIENTS.map((gradient, index) => (
              <GradientPreview
                key={index}
                gradient={gradient}
                onClick={() => handlePresetSelect(gradient)}
              />
            ))}
          </div>
        )}
      </div>
    ),
    [
      colorInfo,
      handleColorChange,
      showAlpha,
      showPresets,
      showGradients,
      handlePresetSelect,
    ],
  );

  return (
    <ColorPickerContainer color={value}>
      <div style={{ marginBottom: '8px' }}>
        {label && (
          <div
            style={{ fontSize: '12px', fontWeight: 500, marginBottom: '4px' }}
          >
            {label}
          </div>
        )}

        <Row gutter={8} align='middle'>
          <Col flex='32px'>
            <Popover
              content={renderPickerContent()}
              trigger='click'
              open={isPickerVisible}
              onOpenChange={setIsPickerVisible}
              placement='bottomLeft'
            >
              <div className='color-preview'>
                <div className='transparency-bg' />
                <div className='color-value' />
              </div>
            </Popover>
          </Col>

          <Col flex='auto'>
            <Input
              value={inputValue}
              onChange={handleInputChange}
              placeholder={placeholder}
              size='small'
            />
          </Col>

          <Col flex='auto'>
            <Space size='small'>
              <Tooltip title='Copy color'>
                <Button
                  type='text'
                  size='small'
                  icon={<CopyOutlined />}
                  onClick={handleCopyColor}
                />
              </Tooltip>

              <Tooltip title='Clear color'>
                <Button
                  type='text'
                  size='small'
                  icon={<ClearOutlined />}
                  onClick={handleClearColor}
                />
              </Tooltip>
            </Space>
          </Col>
        </Row>
      </div>
    </ColorPickerContainer>
  );
};

export default ColorPicker;
