/**
 * Enhanced Container Test Schema
 * Comprehensive test for all enhanced container components with advanced nested drag-and-drop functionality
 */

export const enhancedContainerTestSchema = {
  metadata: {
    version: '3.0.0',
    title: 'Enhanced Container Components Test',
    description:
      'Comprehensive test for all enhanced container components with advanced nested drag-and-drop functionality',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: 'Form Builder Enhancement System',
    category: 'container-testing',
    tags: ['enhanced', 'containers', 'nested', 'drag-drop', 'testing'],
    formId: 'ENHANCED_CONTAINER_TEST_001',
    maxNestingDepth: 10,
    supportedContainers: [
      'tabContainer',
      'cardContainer',
      'formSection',
      'accordionContainer',
      'stepsContainer',
    ],
  },

  layout: [
    {
      id: 'test_row_1',
      type: 'row',
      children: [
        {
          id: 'test_col_1',
          type: 'column',
          children: [
            {
              id: 'enhanced_steps_container',
              type: 'stepsContainer',
              steps: [
                {
                  id: 'step_1',
                  key: 'step_1',
                  title: 'Personal Information',
                  description: 'Enter your personal details',
                  children: [],
                },
                {
                  id: 'step_2',
                  key: 'step_2',
                  title: 'Contact Details',
                  description: 'Provide contact information',
                  children: [],
                },
                {
                  id: 'step_3',
                  key: 'step_3',
                  title: 'Review & Submit',
                  description: 'Review your information',
                  children: [],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 'test_row_2',
      type: 'row',
      children: [
        {
          id: 'test_col_2',
          type: 'column',
          children: [
            {
              id: 'enhanced_tab_container',
              type: 'tabContainer',
              tabs: [
                {
                  id: 'tab_1',
                  key: 'tab_1',
                  label: 'Basic Info',
                  children: [],
                },
                {
                  id: 'tab_2',
                  key: 'tab_2',
                  label: 'Advanced Settings',
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 'test_col_3',
          type: 'column',
          children: [
            {
              id: 'enhanced_card_container',
              type: 'cardContainer',
              cardProps: {
                title: 'Enhanced Card Container',
                bordered: true,
              },
              children: [],
            },
          ],
        },
      ],
    },
    {
      id: 'test_row_3',
      type: 'row',
      children: [
        {
          id: 'test_col_4',
          type: 'column',
          children: [
            {
              id: 'enhanced_form_section',
              type: 'formSection',
              sectionProps: {
                title: 'Enhanced Form Section',
                description: 'Test section with enhanced drop zones',
              },
              styling: {
                bordered: true,
                collapsible: false,
              },
              children: [],
            },
          ],
        },
        {
          id: 'test_col_5',
          type: 'column',
          children: [
            {
              id: 'enhanced_accordion_container',
              type: 'accordionContainer',
              panels: [
                {
                  id: 'panel_1',
                  key: 'panel_1',
                  header: 'Panel 1 - Enhanced',
                  children: [],
                },
                {
                  id: 'panel_2',
                  key: 'panel_2',
                  header: 'Panel 2 - Enhanced',
                  children: [],
                },
              ],
            },
          ],
        },
      ],
    },
  ],

  components: {
    // Enhanced Steps Container
    enhanced_steps_container: {
      id: 'enhanced_steps_container',
      type: 'stepsContainer',
      label: 'Enhanced Multi-Step Form',
      steps: [
        {
          id: 'step_1',
          key: 'step_1',
          title: 'Personal Information',
          description: 'Enter your personal details',
          children: [],
        },
        {
          id: 'step_2',
          key: 'step_2',
          title: 'Contact Details',
          description: 'Provide contact information',
          children: [],
        },
        {
          id: 'step_3',
          key: 'step_3',
          title: 'Review & Submit',
          description: 'Review your information',
          children: [],
        },
      ],
      stepsProps: {
        current: 0,
        direction: 'horizontal',
        size: 'default',
      },
    },

    // Enhanced Tab Container
    enhanced_tab_container: {
      id: 'enhanced_tab_container',
      type: 'tabContainer',
      label: 'Enhanced Tab Container',
      tabs: [
        {
          id: 'tab_1',
          key: 'tab_1',
          label: 'Basic Info',
          children: [],
        },
        {
          id: 'tab_2',
          key: 'tab_2',
          label: 'Advanced Settings',
          children: [],
        },
      ],
      styling: {
        type: 'line',
        size: 'default',
        tabPosition: 'top',
      },
    },

    // Enhanced Card Container
    enhanced_card_container: {
      id: 'enhanced_card_container',
      type: 'cardContainer',
      label: 'Enhanced Card Container',
      cardProps: {
        title: 'Enhanced Card Container',
        bordered: true,
        extra: null,
      },
      styling: {
        size: 'default',
        bordered: true,
        hoverable: false,
      },
    },

    // Enhanced Form Section
    enhanced_form_section: {
      id: 'enhanced_form_section',
      type: 'formSection',
      label: 'Enhanced Form Section',
      sectionProps: {
        title: 'Enhanced Form Section',
        description: 'Test section with enhanced drop zones',
      },
      styling: {
        bordered: true,
        collapsible: false,
        defaultCollapsed: false,
      },
      conditionalLogic: {
        enabled: false,
        conditions: [],
      },
    },

    // Enhanced Accordion Container
    enhanced_accordion_container: {
      id: 'enhanced_accordion_container',
      type: 'accordionContainer',
      label: 'Enhanced Accordion Container',
      panels: [
        {
          id: 'panel_1',
          key: 'panel_1',
          header: 'Panel 1 - Enhanced',
          children: [],
        },
        {
          id: 'panel_2',
          key: 'panel_2',
          header: 'Panel 2 - Enhanced',
          children: [],
        },
      ],
      accordionProps: {
        defaultActiveKey: ['panel_1'],
        ghost: false,
        bordered: true,
      },
    },
  },
};

export default enhancedContainerTestSchema;
