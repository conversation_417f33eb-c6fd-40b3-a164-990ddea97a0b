/**
 * Test Schema for Steps Container
 * Simple test to verify Steps Container renders properly in form builder
 */

export const stepsContainerTestSchema = {
  layout: [
    {
      id: 'test_row_1',
      type: 'row',
      children: [
        {
          id: 'test_col_1',
          type: 'column',
          children: [
            {
              id: 'test_steps_container',
              type: 'stepsContainer',
              steps: [
                {
                  id: 'step_1',
                  key: 'step_1',
                  title: 'Personal Info',
                  description: 'Enter your personal details',
                  children: [],
                },
                {
                  id: 'step_2',
                  key: 'step_2',
                  title: 'Contact Info',
                  description: 'Enter your contact details',
                  children: [],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  components: {
    test_steps_container: {
      id: 'test_steps_container',
      type: 'stepsContainer',
      label: 'Test Steps Container',
      steps: [
        {
          id: 'step_1',
          key: 'step_1',
          title: 'Personal Info',
          description: 'Enter your personal details',
          children: [],
        },
        {
          id: 'step_2',
          key: 'step_2',
          title: 'Contact Info',
          description: 'Enter your contact details',
          children: [],
        },
      ],
      stepsProps: {
        current: 0,
        direction: 'horizontal',
        size: 'default',
      },
    },
  },
};
