/**
 * Validation Messages
 * 
 * Default validation messages and message templates for form validation.
 * Provides consistent error messaging across the form system.
 */

/**
 * Default validation messages
 * 
 * Standard validation messages for common validation scenarios.
 */
export const DEFAULT_VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  EMAIL: 'Please enter a valid email address',
  URL: 'Please enter a valid URL',
  NUMBER: 'Please enter a valid number',
  INTEGER: 'Please enter a valid integer',
  PHONE: 'Please enter a valid phone number',
  PASSWORD: 'Password must meet the requirements',
  CONFIRM_PASSWORD: 'Passwords do not match',
  MIN_LENGTH: 'Input is too short',
  MAX_LENGTH: 'Input is too long',
  PATTERN: 'Input format is invalid',
  RANGE: 'Value is out of range',
  DATE: 'Please enter a valid date',
  TIME: 'Please enter a valid time',
  FILE_SIZE: 'File size is too large',
  FILE_TYPE: 'File type is not supported',
};

/**
 * Validation message templates
 * 
 * Template functions for generating dynamic validation messages.
 */
export const VALIDATION_MESSAGE_TEMPLATES = {
  /**
   * Required field message template
   * @param {string} fieldName - Name of the field
   * @returns {string} Formatted required message
   */
  required: (fieldName) => `${fieldName || 'Field'} is required`,

  /**
   * Minimum length message template
   * @param {number} minLength - Minimum required length
   * @param {string} fieldName - Name of the field
   * @returns {string} Formatted minimum length message
   */
  minLength: (minLength, fieldName) => 
    `${fieldName || 'Field'} must be at least ${minLength} characters`,

  /**
   * Maximum length message template
   * @param {number} maxLength - Maximum allowed length
   * @param {string} fieldName - Name of the field
   * @returns {string} Formatted maximum length message
   */
  maxLength: (maxLength, fieldName) => 
    `${fieldName || 'Field'} must not exceed ${maxLength} characters`,

  /**
   * Range validation message template
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @param {string} fieldName - Name of the field
   * @returns {string} Formatted range message
   */
  range: (min, max, fieldName) => 
    `${fieldName || 'Value'} must be between ${min} and ${max}`,

  /**
   * Pattern validation message template
   * @param {string} pattern - Pattern description
   * @param {string} fieldName - Name of the field
   * @returns {string} Formatted pattern message
   */
  pattern: (pattern, fieldName) => 
    `${fieldName || 'Field'} must match the pattern: ${pattern}`,

  /**
   * File size validation message template
   * @param {string} maxSize - Maximum file size
   * @returns {string} Formatted file size message
   */
  fileSize: (maxSize) => `File size must not exceed ${maxSize}`,

  /**
   * File type validation message template
   * @param {Array} allowedTypes - Array of allowed file types
   * @returns {string} Formatted file type message
   */
  fileType: (allowedTypes) => 
    `Only ${allowedTypes.join(', ')} files are allowed`,
};

/**
 * Form submission messages
 * 
 * Messages for form submission states and results.
 */
export const SUBMISSION_MESSAGES = {
  SUCCESS: 'Form submitted successfully!',
  ERROR: 'Failed to submit form. Please try again.',
  VALIDATION_ERROR: 'Please check the form and fix any errors',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  LOADING: 'Submitting form...',
};

/**
 * Form state messages
 * 
 * Messages for various form states and actions.
 */
export const FORM_STATE_MESSAGES = {
  RESET_SUCCESS: 'Form has been reset',
  SAVE_DRAFT: 'Draft saved successfully',
  AUTO_SAVE: 'Changes saved automatically',
  UNSAVED_CHANGES: 'You have unsaved changes',
  LOADING: 'Loading form...',
  ERROR_LOADING: 'Failed to load form',
};

/**
 * Field-specific validation messages
 * 
 * Validation messages for specific field types.
 */
export const FIELD_VALIDATION_MESSAGES = {
  email: {
    required: 'Email address is required',
    invalid: 'Please enter a valid email address',
  },
  password: {
    required: 'Password is required',
    minLength: 'Password must be at least 8 characters',
    pattern: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  },
  confirmPassword: {
    required: 'Please confirm your password',
    mismatch: 'Passwords do not match',
  },
  phone: {
    required: 'Phone number is required',
    invalid: 'Please enter a valid phone number',
  },
  url: {
    required: 'URL is required',
    invalid: 'Please enter a valid URL',
  },
  date: {
    required: 'Date is required',
    invalid: 'Please enter a valid date',
    future: 'Date cannot be in the future',
    past: 'Date cannot be in the past',
  },
};
