/**
 * Component Styling Utilities
 * 
 * Styled components and styling utilities for individual form components.
 * Provides consistent styling patterns across different component types.
 */

import styled from 'styled-components';

/**
 * Base component wrapper with common styling
 * 
 * Provides consistent base styling for all form components including
 * spacing, transitions, and responsive behavior.
 */
export const ComponentWrapper = styled.div`
  width: 100%;
  position: relative;
  
  /* Smooth transitions for all state changes */
  transition: all 0.3s ease;
  
  /* Error state styling */
  ${props => props.hasError && `
    .ant-input,
    .ant-select-selector,
    .ant-picker,
    .ant-input-number {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
    }
  `}
  
  /* Loading state styling */
  ${props => props.loading && `
    opacity: 0.7;
    pointer-events: none;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  `}
  
  /* Disabled state styling */
  ${props => props.disabled && `
    opacity: 0.6;
    cursor: not-allowed;
    
    * {
      cursor: not-allowed !important;
    }
  `}
  
  /* Focus state styling */
  ${props => props.focused && `
    .ant-input:focus,
    .ant-select-focused .ant-select-selector,
    .ant-picker:focus {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }
  `}
`;

/**
 * Input component wrapper with specific styling
 * 
 * Provides styling specific to input-type components.
 */
export const InputWrapper = styled(ComponentWrapper)`
  .ant-input,
  .ant-input-password {
    border-radius: 6px;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  /* Input with prefix/suffix styling */
  .ant-input-affix-wrapper {
    border-radius: 6px;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
`;

/**
 * Select component wrapper with specific styling
 * 
 * Provides styling specific to select-type components.
 */
export const SelectWrapper = styled(ComponentWrapper)`
  .ant-select {
    width: 100%;
  }
  
  .ant-select-selector {
    border-radius: 6px !important;
    
    &:hover {
      border-color: #40a9ff !important;
    }
  }
  
  .ant-select-focused .ant-select-selector {
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  }
  
  /* Multiple select styling */
  .ant-select-multiple .ant-select-selection-item {
    background: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
`;

/**
 * Button component wrapper with specific styling
 * 
 * Provides styling specific to button components.
 */
export const ButtonWrapper = styled(ComponentWrapper)`
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  /* Primary button styling */
  .ant-btn-primary {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
    }
  }
`;

/**
 * Upload component wrapper with specific styling
 * 
 * Provides styling specific to upload components.
 */
export const UploadWrapper = styled(ComponentWrapper)`
  .ant-upload {
    width: 100%;
  }
  
  .ant-upload-btn {
    width: 100%;
    border-radius: 6px;
    border: 2px dashed #d9d9d9;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
  }
  
  .ant-upload-list {
    margin-top: 8px;
  }
`;

/**
 * Date picker component wrapper with specific styling
 * 
 * Provides styling specific to date picker components.
 */
export const DatePickerWrapper = styled(ComponentWrapper)`
  .ant-picker {
    width: 100%;
    border-radius: 6px;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  /* Range picker styling */
  .ant-picker-range {
    .ant-picker-input {
      text-align: center;
    }
  }
`;

/**
 * Slider component wrapper with specific styling
 * 
 * Provides styling specific to slider components.
 */
export const SliderWrapper = styled(ComponentWrapper)`
  padding: 16px 8px;
  
  .ant-slider {
    margin: 0;
  }
  
  .ant-slider-rail {
    background-color: #f5f5f5;
    border-radius: 2px;
  }
  
  .ant-slider-track {
    background: linear-gradient(90deg, #1890ff 0%, #096dd9 100%);
    border-radius: 2px;
  }
  
  .ant-slider-handle {
    border: 2px solid #1890ff;
    background-color: #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    
    &:hover,
    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.2);
    }
  }
`;

/**
 * Rate component wrapper with specific styling
 * 
 * Provides styling specific to rate components.
 */
export const RateWrapper = styled(ComponentWrapper)`
  .ant-rate {
    font-size: 20px;
    
    .ant-rate-star {
      margin-right: 4px;
      
      &:hover {
        transform: scale(1.1);
        transition: transform 0.2s ease;
      }
    }
  }
`;

/**
 * Switch component wrapper with specific styling
 * 
 * Provides styling specific to switch components.
 */
export const SwitchWrapper = styled(ComponentWrapper)`
  .ant-switch {
    &:hover {
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
`;

/**
 * Checkbox/Radio wrapper with specific styling
 * 
 * Provides styling specific to checkbox and radio components.
 */
export const CheckboxRadioWrapper = styled(ComponentWrapper)`
  .ant-checkbox-group,
  .ant-radio-group {
    width: 100%;
  }
  
  .ant-checkbox-wrapper,
  .ant-radio-wrapper {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  /* Horizontal layout */
  ${props => props.direction === 'horizontal' && `
    .ant-checkbox-wrapper,
    .ant-radio-wrapper {
      margin-right: 16px;
      margin-bottom: 0;
      
      &:last-child {
        margin-right: 0;
      }
    }
  `}
`;
