/**
 * Feedback Component Renderers
 * 
 * Renders feedback components like Alert, Progress, Skeleton, Spin, etc.
 * These components provide user feedback and loading states.
 */

import React from 'react';
import {
  Alert,
  Progress,
  Skeleton,
  Spin,
} from 'antd';

/**
 * Renders an Alert component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Alert component
 */
export const renderAlert = (component) => {
  return (
    <Alert
      message={component.message || 'Alert Message'}
      description={component.description || ''}
      type={component.styling?.type || 'info'}
      showIcon={component.styling?.showIcon !== false}
      closable={component.styling?.closable}
      banner={component.styling?.banner}
      style={{ marginBottom: '16px' }}
    />
  );
};

/**
 * Renders a Progress component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Progress component
 */
export const renderProgress = (component) => {
  return (
    <Progress
      percent={component.percent || 50}
      type={component.styling?.type || 'line'}
      size={component.styling?.size || 'default'}
      showInfo={component.styling?.showInfo !== false}
      status={component.styling?.status || 'normal'}
      strokeColor={component.styling?.strokeColor}
      format={component.styling?.format}
    />
  );
};

/**
 * Renders a Skeleton component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Skeleton component
 */
export const renderSkeleton = (component) => {
  return (
    <Skeleton
      active={component.styling?.active}
      avatar={component.styling?.avatar}
      paragraph={component.styling?.paragraph}
      title={component.styling?.title}
      loading={component.loading}
    />
  );
};

/**
 * Renders a Spin component
 * 
 * @param {Object} component - Component configuration
 * @returns {JSX.Element} Rendered Spin component
 */
export const renderSpin = (component) => {
  return (
    <Spin
      size={component.styling?.size}
      spinning={component.styling?.spinning}
      tip={component.tip}
    >
      <div style={{ padding: '20px', background: '#f0f0f0' }}>
        Content being loaded...
      </div>
    </Spin>
  );
};
