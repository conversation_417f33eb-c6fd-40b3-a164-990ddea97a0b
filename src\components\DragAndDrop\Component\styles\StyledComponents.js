/**
 * Component Styled Components
 *
 * Styled components for the Component module using styled-components.
 * Provides enterprise-grade styling with modern design patterns,
 * accessibility compliance, and responsive design.
 *
 * Features:
 * - Modern glass morphism and backdrop blur effects
 * - Accessibility-compliant focus states and high contrast support
 * - Responsive design with mobile-first approach
 * - Premium visual hierarchy with subtle shadows and borders
 * - Smooth transitions and hover states
 *
 * @module ComponentStyledComponents
 */

import styled from 'styled-components';
import { motion } from 'framer-motion';
import { colors } from '../../../../styles/theme';

/**
 * Advanced Component Wrapper
 *
 * Main container for draggable components with sophisticated styling.
 * Includes hover states, selection states, and container-specific styling.
 *
 * Features:
 * - Glass morphism with backdrop blur
 * - Accessibility-compliant focus states
 * - Container component differentiation
 * - Smooth transitions and animations
 */
export const AdvancedComponentWrapper = styled(motion.div)`
  position: relative;
  margin: 6px 0;
  border-radius: 8px;
  background: ${colors.surface};
  border: 1px solid ${colors.borderInactive};
  overflow: hidden;
  min-height: 80px;
  padding: 16px;

  /* Clean default state - establishes clear boundaries without visual noise */
  backdrop-filter: blur(5px);

  /* Accessibility: Ensure keyboard focus is visible */
  &:focus-visible {
    outline: 2px solid ${colors.borderFocus};
    outline-offset: 2px;
  }

  /* Hover styles handled by Framer Motion variants */

  /* Focus state for accessibility */
  &:focus-within {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04), 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &.selected {
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2),
      0 2px 8px rgba(59, 130, 246, 0.08), 0 1px 3px rgba(0, 0, 0, 0.04);
    background: rgba(59, 130, 246, 0.02);

    .component-label {
      opacity: 1;
      background: rgba(59, 130, 246, 0.9);
      color: white;
      transform: translateX(0) scale(1);
    }

    .drag-handle {
      opacity: 0.8;
      background: rgba(59, 130, 246, 0.6);
    }
  }

  &.dragging {
    opacity: 0.8;
    transform: rotate(2deg) scale(1.02);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16), 0 4px 16px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    border-color: ${colors.primary};
  }

  &.container-component {
    border-style: dashed;
    border-color: ${colors.aiPrimary}60;
    background: linear-gradient(
      135deg,
      ${colors.aiPrimary}05 0%,
      ${colors.primary}03 100%
    );

    &:hover {
      border-color: ${colors.aiPrimary};
      background: linear-gradient(
        135deg,
        ${colors.aiPrimary}10 0%,
        ${colors.primary}05 100%
      );
    }
  }
`;

/**
 * Component Overlay
 *
 * Transparent overlay for advanced visual effects and interactions.
 * Provides glass morphism effect and smooth transitions.
 */
export const ComponentOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  pointer-events: none;
  border-radius: 8px;

  /* Sophisticated glass morphism effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.02) 0%,
      rgba(255, 255, 255, 0.01) 100%
    );
    border-radius: 8px;
    backdrop-filter: blur(1px);
  }
`;

/**
 * Component Label
 *
 * Floating label that appears on hover to identify component types.
 * Features premium typography and glass morphism styling.
 */
export const ComponentLabel = styled(motion.div)`
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(15, 23, 42, 0.9);
  color: rgba(248, 250, 252, 0.95);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  z-index: 4;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);

  /* Premium typography */
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    sans-serif;
  line-height: 1;

  /* Animation handled by Framer Motion variants */

  /* Contextual positioning for different screen sizes */
  @media (max-width: 768px) {
    font-size: 8px;
    padding: 1px 4px;
    top: 6px;
    right: 6px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
`;

/**
 * Component Actions
 *
 * Action buttons container that appears on hover.
 * Provides edit, copy, and delete functionality with smooth animations.
 */
export const ComponentActions = styled.div`
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transform: translateY(-4px);
  transition: all 0.2s ease;
  z-index: 4;

  .action-btn {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid ${colors.borderLight};
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .anticon {
      font-size: 12px;
      color: ${colors.textSecondary};
    }

    &:hover {
      background: ${colors.primary};
      border-color: ${colors.primary};
      transform: scale(1.1);

      .anticon {
        color: white;
      }
    }

    &.danger:hover {
      background: ${colors.error};
      border-color: ${colors.error};
    }

    &.warning:hover {
      background: ${colors.warning};
      border-color: ${colors.warning};
    }
  }
`;

/**
 * Drag Handle
 *
 * Visual indicator for draggable areas with sophisticated dot pattern.
 * Provides clear affordance for drag interactions.
 */
export const DragHandle = styled(motion.div)`
  position: absolute;
  top: 8px;
  left: 8px;
  width: 16px;
  height: 16px;
  background: rgba(100, 116, 139, 0.4);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  z-index: 3;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  /* Sophisticated drag dots pattern */
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    background-image: radial-gradient(
        circle at 2px 2px,
        rgba(255, 255, 255, 0.8) 0.5px,
        transparent 0.5px
      ),
      radial-gradient(
        circle at 6px 2px,
        rgba(255, 255, 255, 0.8) 0.5px,
        transparent 0.5px
      ),
      radial-gradient(
        circle at 2px 6px,
        rgba(255, 255, 255, 0.8) 0.5px,
        transparent 0.5px
      ),
      radial-gradient(
        circle at 6px 6px,
        rgba(255, 255, 255, 0.8) 0.5px,
        transparent 0.5px
      );
    background-size: 8px 8px;
    background-repeat: no-repeat;
  }

  /* Hover and active states handled by Framer Motion */
  &:active {
    cursor: grabbing;
  }
`;

/**
 * Component Content
 *
 * Main content area for component rendering.
 * Provides proper spacing and z-index layering.
 */
export const ComponentContent = styled.div`
  position: relative;
  padding: 8px;
  z-index: 1;
`;

/**
 * Component Type Indicator
 *
 * Small indicator showing component type at bottom-left.
 * Appears on hover for additional context.
 */
export const ComponentTypeIndicator = styled.div`
  position: absolute;
  bottom: 4px;
  left: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 3;

  .component-wrapper:hover & {
    opacity: 1;
  }
`;

/**
 * Inline Editable Text Container
 *
 * Container for inline text editing functionality.
 * Provides smooth transitions between display and edit modes.
 */
export const InlineEditableText = styled.div`
  position: relative;
  display: inline-block;
  min-width: 60px;
  min-height: 20px;

  .editable-text {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    display: inline-block;
    min-width: 60px;

    &:hover {
      background: rgba(59, 130, 246, 0.1);
      border-color: rgba(59, 130, 246, 0.3);
    }
  }

  .editing-input {
    border: 2px solid #1890ff;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: inherit;
    font-family: inherit;
    background: white;
    outline: none;
    min-width: 60px;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .placeholder-text {
    color: #999;
    font-style: italic;
  }
`;
