/**
 * PropertiesPanel Module Index
 *
 * Barrel export file for the PropertiesPanel module.
 * Provides clean imports for all components, hooks, utilities, and constants.
 *
 * Usage:
 * import PropertiesPanel, { usePropertiesPanel, PropertiesTab } from './PropertiesPanel';
 *
 * Architecture Overview:
 * - Main component: PropertiesPanel (default export)
 * - Custom hooks: usePropertiesPanel, usePositioning, useComponentProperties
 * - UI components: PropertiesTab, AdvancedSettingsTab, RolesTab
 * - Styled components: All styling components from styles
 * - Utilities: Positioning, validation, and helper functions
 * - Constants: Configuration, UI text, styling constants
 *
 * @module PropertiesPanel
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

// Main component (default export)
export { default } from './PropertiesPanel';

// Custom hooks
export { usePropertiesPanel } from './hooks/usePropertiesPanel';
export { usePositioning } from './hooks/usePositioning';
export { useComponentProperties } from './hooks/useComponentProperties';
export { useDoubleClickHandler } from './hooks/useDoubleClickHandler';
export { useSwipeGesture } from './hooks/useSwipeGesture';
export { useEventHandling } from './hooks/useEventHandling';
export { useScrollManagement } from './hooks/useScrollManagement';

// Context and providers
export {
  PropertiesPanelProvider,
  usePropertiesPanelContext,
  withPropertiesPanel,
} from './context/PropertiesPanelContext';

// UI Components
export { default as PropertiesTab } from './components/PropertiesTab';
export { default as AdvancedSettingsTab } from './components/AdvancedSettingsTab';
export { default as RolesTab } from './components/RolesTab';

// Styled Components
export * from './styles/PropertiesPanel.styles';

// Utility functions
export {
  calculateOptimalPosition,
  detectCollisions,
  getComponentPropertySchema,
  validatePropertyValue,
  formatPropertyValue,
} from './utils/propertiesPanelUtils';

// Constants
export {
  PANEL_TABS,
  POSITIONING_PRIORITY,
  RESPONSIVE_BREAKPOINTS,
  ANIMATION_VARIANTS,
  Z_INDEX_LEVELS,
} from './constants/propertiesPanelConstants';
