import Groq from 'groq-sdk';

// Initialize Groq client
const groq = new Groq({
  apiKey: process.env.REACT_APP_GROQ_API_KEY || process.env.GROQ_API_KEY,
  dangerouslyAllowBrowser: true, // Required for client-side usage
});

// Available Groq models - using current supported models (2024)
const GROQ_MODELS = {
  LLAMA_70B: 'llama-3.3-70b-versatile', // Most powerful production model
  LLAMA_8B: 'llama-3.1-8b-instant', // Faster production model
  DEEPSEEK_70B: 'deepseek-r1-distill-llama-70b', // Reasoning model (preview)
  QWEN_32B: 'qwen-qwq-32b', // Alternative reasoning model (preview)
  MISTRAL_24B: 'mistral-saba-24b', // Alternative model (preview)
};

// Default model configuration - using the most powerful production model
const DEFAULT_MODEL = GROQ_MODELS.LLAMA_70B;
const MAX_TOKENS = 8000; // Increased for complex forms
const TEMPERATURE = 0.05; // Very low temperature for consistent JSON generation

// Removed unused FORM_GENERATION_PROMPT - using inline prompt in generateFormSchema function

/**
 * Fix common JSON syntax issues in AI responses
 * @param {string} jsonString - The JSON string to fix
 * @returns {string} Fixed JSON string
 */
const fixCommonJSONIssues = (jsonString) => {
  let fixed = jsonString;

  // Fix trailing commas in objects and arrays
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

  // Fix missing commas between object properties
  fixed = fixed.replace(/}(\s*)"/g, '},$1"');
  fixed = fixed.replace(/](\s*)"/g, '],$1"');

  // Fix unescaped quotes in strings
  fixed = fixed.replace(/"([^"]*)"([^"]*)"([^"]*)":/g, '"$1\\"$2\\"$3":');

  // Fix incomplete arrays or objects at the end
  const openBraces = (fixed.match(/\{/g) || []).length;
  const closeBraces = (fixed.match(/\}/g) || []).length;
  const openBrackets = (fixed.match(/\[/g) || []).length;
  const closeBrackets = (fixed.match(/\]/g) || []).length;

  // Add missing closing braces
  for (let i = 0; i < openBraces - closeBraces; i++) {
    fixed += '}';
  }

  // Add missing closing brackets
  for (let i = 0; i < openBrackets - closeBrackets; i++) {
    fixed += ']';
  }

  return fixed;
};

/**
 * Generate form schema using Groq AI
 * @param {string} userPrompt - User's natural language description
 * @param {Object} options - Generation options
 * @returns {Promise<Object>} Generated form schema
 */
export const generateFormSchema = async (userPrompt, options = {}) => {
  try {
    const {
      model = DEFAULT_MODEL,
      maxTokens = MAX_TOKENS,
      temperature = TEMPERATURE,
    } = options;

    // Create a focused prompt for better JSON generation
    const enhancedPrompt = `You are a JSON form schema generator. Generate ONLY valid JSON - no explanations, no markdown, no code blocks.

USER REQUEST: "${userPrompt}"

Generate a complete JSON schema with this EXACT structure:

{
  "metadata": {
    "version": "2.0.0",
    "title": "Form Title",
    "description": "Form Description",
    "createdAt": "${new Date().toISOString()}",
    "updatedAt": "${new Date().toISOString()}",
    "author": "AI Form Generator"
  },
  "layout": [
    {
      "id": "row-1",
      "type": "row",
      "children": [
        {
          "id": "col-1",
          "type": "column",
          "props": {"span": 24},
          "children": [
            {
              "type": "component",
              "id": "component-id-1"
            }
          ]
        }
      ]
    }
  ],
  "components": {
    "component-id": {
      "id": "component-id",
      "type": "input",
      "label": "Field Label",
      "name": "fieldName",
      "validation": {"required": true},
      "props": {"placeholder": "Enter value"}
    }
  }
}

CRITICAL RULES:
- Use ONLY these component types: input, textarea, password, inputnumber, select, radio, checkbox, switch, rate, slider, datepicker, rangepicker, timepicker, upload, alert, progress, statistic, button
- For multi-step forms, use stepsContainer type with proper structure
- Ensure all JSON is valid with proper commas and brackets
- Keep component count under 15 for reliability
- Return ONLY the JSON object`;

    console.log(
      'Sending prompt to AI:',
      enhancedPrompt.substring(0, 200) + '...',
    );

    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: 'system',
          content:
            'You are a professional form builder AI that generates perfect JSON schemas. Always return valid JSON only.',
        },
        {
          role: 'user',
          content: enhancedPrompt,
        },
      ],
      model: model,
      max_tokens: maxTokens,
      temperature: temperature,
      top_p: 0.9,
      stream: false,
    });

    const response = completion.choices[0]?.message?.content;

    if (!response) {
      throw new Error('No response received from AI');
    }

    // Clean and parse JSON response with enhanced error handling
    const cleanedResponse = response.trim();
    let jsonResponse;

    try {
      // Try to parse the response as JSON
      jsonResponse = JSON.parse(cleanedResponse);
    } catch (parseError) {
      console.log(
        'Initial JSON parse failed, attempting to fix...',
        parseError.message,
      );

      // Try to extract JSON from markdown or other formatting
      let jsonString = cleanedResponse;

      // Remove markdown code blocks
      jsonString = jsonString.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Try to find JSON object boundaries
      const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonString = jsonMatch[0];
      }

      // Try to fix common JSON issues
      jsonString = fixCommonJSONIssues(jsonString);

      try {
        jsonResponse = JSON.parse(jsonString);
      } catch (secondParseError) {
        console.error(
          'JSON parsing failed after cleanup:',
          secondParseError.message,
        );
        console.error(
          'Problematic JSON:',
          jsonString.substring(0, 500) + '...',
        );

        // Generate a simplified fallback schema
        throw new Error(
          `Invalid JSON response from AI: ${secondParseError.message}. The AI response may be too complex or truncated.`,
        );
      }
    }

    // Validate and enhance the generated schema
    console.log('🔍 [aiService] Raw AI response:', jsonResponse);
    const validatedSchema = validateAndEnhanceSchema(jsonResponse);
    console.log('🔍 [aiService] Validated schema:', validatedSchema);

    return {
      success: true,
      schema: validatedSchema,
      metadata: {
        model: model,
        tokensUsed: completion.usage?.total_tokens || 0,
        generatedAt: new Date().toISOString(),
        userPrompt: userPrompt,
      },
    };
  } catch (error) {
    console.error('AI Form Generation Error:', error);

    return {
      success: false,
      error: error.message,
      fallbackSchema: generateFallbackSchema(userPrompt),
      metadata: {
        generatedAt: new Date().toISOString(),
        userPrompt: userPrompt,
        errorType: error.name,
      },
    };
  }
};

/**
 * Validate and enhance generated schema
 * @param {Object} schema - Generated schema
 * @returns {Object} Validated and enhanced schema
 */
const validateAndEnhanceSchema = (schema) => {
  // Ensure required metadata exists
  if (!schema.metadata) {
    schema.metadata = {};
  }

  // Add/update timestamps
  const now = new Date().toISOString();
  schema.metadata.updatedAt = now;
  if (!schema.metadata.createdAt) {
    schema.metadata.createdAt = now;
  }

  // Ensure version
  if (!schema.metadata.version) {
    schema.metadata.version = '2.0.0';
  }

  // Ensure layout exists
  if (!schema.layout || !Array.isArray(schema.layout)) {
    schema.layout = [];
  }

  // Ensure components exist
  if (!schema.components || typeof schema.components !== 'object') {
    schema.components = {};
  }

  // Add unique IDs if missing
  const generateId = () =>
    `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  // Validate layout structure
  schema.layout.forEach((row) => {
    if (!row.id) row.id = generateId();
    if (row.children && Array.isArray(row.children)) {
      row.children.forEach((col) => {
        if (!col.id) col.id = generateId();
      });
    }
  });

  // Validate components
  Object.keys(schema.components).forEach((key) => {
    const component = schema.components[key];
    if (!component.id) component.id = key;
    if (!component.name && component.label) {
      component.name = component.label.toLowerCase().replace(/\s+/g, '_');
    }
  });

  return schema;
};

/**
 * Generate fallback schema when AI fails
 * @param {string} userPrompt - Original user prompt
 * @returns {Object} Basic fallback schema
 */
const generateFallbackSchema = (userPrompt) => {
  const now = new Date().toISOString();
  const id = `fallback-${Date.now()}`;

  return {
    metadata: {
      version: '2.0.0',
      title: 'Basic Form',
      description: `Generated from: ${userPrompt}`,
      createdAt: now,
      updatedAt: now,
      author: 'AI Form Generator (Fallback)',
      category: 'basic',
      tags: ['fallback', 'basic'],
      formId: id,
    },
    layout: [
      {
        id: `row-${id}`,
        type: 'row',
        children: [
          {
            id: `col-${id}`,
            type: 'column',
            props: { span: 24 },
            children: [`input-${id}`, `submit-${id}`],
          },
        ],
      },
    ],
    components: {
      [`input-${id}`]: {
        id: `input-${id}`,
        type: 'input',
        content: 'Text Input',
        label: 'Please describe your form requirements',
        name: 'user_input',
        validation: { required: true },
        styling: { size: 'large' },
        props: { placeholder: 'Enter your requirements here...' },
      },
      [`submit-${id}`]: {
        id: `submit-${id}`,
        type: 'button',
        content: 'Submit',
        label: 'Submit',
        styling: { type: 'primary', size: 'large' },
        props: { htmlType: 'submit' },
      },
    },
  };
};

/**
 * Get available AI models
 * @returns {Object} Available models with descriptions
 */
export const getAvailableModels = () => {
  return {
    [GROQ_MODELS.LLAMA_70B]: {
      name: 'Llama 3.3 70B Versatile',
      description: 'Most powerful production model - best for complex forms',
      maxTokens: 32768,
      recommended: true,
      type: 'production',
    },
    [GROQ_MODELS.LLAMA_8B]: {
      name: 'Llama 3.1 8B Instant',
      description: 'Faster production model - good for simple forms',
      maxTokens: 131072,
      recommended: false,
      type: 'production',
    },
    [GROQ_MODELS.DEEPSEEK_70B]: {
      name: 'DeepSeek R1 Distill Llama 70B',
      description: 'Advanced reasoning model - excellent for complex logic',
      maxTokens: 131072,
      recommended: false,
      type: 'preview',
    },
    [GROQ_MODELS.QWEN_32B]: {
      name: 'Qwen QwQ 32B',
      description: 'Reasoning model - good for analytical forms',
      maxTokens: 131072,
      recommended: false,
      type: 'preview',
    },
    [GROQ_MODELS.MISTRAL_24B]: {
      name: 'Mistral Saba 24B',
      description: 'Multilingual model - good for international forms',
      maxTokens: 32768,
      recommended: false,
      type: 'preview',
    },
  };
};

/**
 * Test AI connection
 * @returns {Promise<Object>} Connection test result
 */
export const testAIConnection = async () => {
  try {
    const response = await groq.chat.completions.create({
      messages: [
        {
          role: 'user',
          content:
            'Respond with exactly: {"status": "connected", "timestamp": "' +
            new Date().toISOString() +
            '"}',
        },
      ],
      model: GROQ_MODELS.LLAMA_8B, // Use faster model for testing
      max_tokens: 100,
      temperature: 0,
    });

    const result = JSON.parse(response.choices[0]?.message?.content || '{}');

    return {
      success: true,
      connected: true,
      model: GROQ_MODELS.LLAMA_8B,
      response: result,
    };
  } catch (error) {
    return {
      success: false,
      connected: false,
      error: error.message,
    };
  }
};

export default {
  generateFormSchema,
  getAvailableModels,
  testAIConnection,
  GROQ_MODELS,
};
