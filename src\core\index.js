import ReactDOM from 'react-dom/client';
import React from 'react';
import Form<PERSON><PERSON>erApp from '../FormBuilderApp';
import FormRendererDemo from '../FormRendererDemo';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { GlobalStyle } from '../styles';
import { Button } from 'antd';
import 'antd/dist/reset.css';

function App() {
  const [currentView, setCurrentView] = React.useState('builder');

  const renderView = () => {
    switch (currentView) {
      case 'standalone':
        return <FormRendererDemo />;
      case 'builder':
      default:
        return (
          <DndProvider backend={HTML5Backend}>
            <FormBuilderApp />
          </DndProvider>
        );
    }
  };

  return (
    <div className='App'>
      <GlobalStyle />

      {/* Navigation */}
      <div
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          zIndex: 1000,
          display: 'flex',
          gap: '8px',
        }}
      >
        {/* <Button
          type={currentView === 'builder' ? 'primary' : 'default'}
          onClick={() => setCurrentView('builder')}
          size='small'
        >
          Form Builder
        </Button> */}
        {/* <Button
          type={currentView === 'standalone' ? 'primary' : 'default'}
          onClick={() => setCurrentView('standalone')}
          size='small'
        >
          Standalone Demo
        </Button> */}
      </div>

      {renderView()}
    </div>
  );
}

const rootElement = document.getElementById('root');
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(<App />);
}
