import { useMemo } from 'react';

/**
 * Custom hook for managing component props generation and memoization
 * 
 * Generates and memoizes common props, form item props, and component-specific props
 * to optimize rendering performance and prevent unnecessary re-renders.
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Memoized component props
 * @returns {Object} returns.commonProps - Common props for all components
 * @returns {Object} returns.formItemProps - Props for Ant Design Form.Item
 * @returns {Object} returns.validationRules - Validation rules array
 */
export const useComponentProps = (component) => {
  /**
   * Memoized common component props
   * 
   * These props are shared across most form components and include
   * basic styling, behavior, and interaction properties.
   */
  const commonProps = useMemo(() => {
    if (!component) return {};

    return {
      placeholder: component.placeholder,
      disabled: component.styling?.disabled || false,
      size: component.styling?.size || 'middle',
      style: {
        width: '100%',
        ...component.styling?.style,
      },
      ...component.componentProps,
    };
  }, [
    component?.placeholder,
    component?.styling?.disabled,
    component?.styling?.size,
    component?.styling?.style,
    component?.componentProps,
  ]);

  /**
   * Memoized validation rules
   * 
   * Generates validation rules based on component configuration.
   */
  const validationRules = useMemo(() => {
    if (!component?.validation) return [];

    const rules = [];
    const validation = component.validation;

    // Required field validation
    if (validation.required) {
      rules.push({
        required: true,
        message: validation.message || `${component.label || 'Field'} is required`,
      });
    }

    // Pattern validation (regex)
    if (validation.pattern) {
      rules.push({
        pattern: new RegExp(validation.pattern),
        message: validation.patternMessage || 'Invalid format',
      });
    }

    // Minimum length validation
    if (validation.min !== undefined) {
      rules.push({
        min: validation.min,
        message: `Minimum length is ${validation.min}`,
      });
    }

    // Maximum length validation
    if (validation.max !== undefined) {
      rules.push({
        max: validation.max,
        message: `Maximum length is ${validation.max}`,
      });
    }

    return rules;
  }, [
    component?.validation,
    component?.label,
  ]);

  /**
   * Memoized form item props
   * 
   * Props specifically for Ant Design Form.Item wrapper component.
   */
  const formItemProps = useMemo(() => {
    if (!component) return {};

    return {
      label: component.label,
      name: component.name || component.id,
      rules: validationRules,
      tooltip: component.tooltip,
      extra: component.extra,
      hasFeedback: component.styling?.hasFeedback,
      validateStatus: component.styling?.validateStatus,
      help: component.styling?.help,
      ...component.formItemProps,
    };
  }, [
    component?.label,
    component?.name,
    component?.id,
    component?.tooltip,
    component?.extra,
    component?.styling?.hasFeedback,
    component?.styling?.validateStatus,
    component?.styling?.help,
    component?.formItemProps,
    validationRules,
  ]);

  return {
    commonProps,
    formItemProps,
    validationRules,
  };
};
