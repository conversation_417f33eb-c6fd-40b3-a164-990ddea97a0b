/**
 * Display Component Styling
 * 
 * Styled components for display components like Avatar, Badge, Tag, etc.
 * Provides consistent styling for components that display information.
 */

import styled from 'styled-components';

/**
 * Avatar component styling
 * 
 * Provides styling for avatar display components.
 */
export const AvatarWrapper = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  .ant-avatar {
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    /* Custom size styling */
    &.avatar-large {
      width: 64px;
      height: 64px;
      font-size: 24px;
    }
    
    &.avatar-small {
      width: 24px;
      height: 24px;
      font-size: 12px;
    }
  }
  
  /* Avatar group styling */
  .ant-avatar-group {
    .ant-avatar {
      border: 2px solid #fff;
      margin-left: -8px;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }
`;

/**
 * Badge component styling
 * 
 * Provides styling for badge display components.
 */
export const BadgeWrapper = styled.div`
  display: inline-block;
  
  .ant-badge {
    .ant-badge-count {
      background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
      border: none;
      box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
      font-weight: 600;
    }
    
    .ant-badge-dot {
      background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
      box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
    }
    
    /* Status badge styling */
    &.ant-badge-status {
      .ant-badge-status-dot {
        &.ant-badge-status-success {
          background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        }
        
        &.ant-badge-status-processing {
          background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        }
        
        &.ant-badge-status-warning {
          background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
        }
        
        &.ant-badge-status-error {
          background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
        }
      }
    }
  }
`;

/**
 * Tag component styling
 * 
 * Provides styling for tag display components.
 */
export const TagWrapper = styled.div`
  display: inline-block;
  
  .ant-tag {
    border-radius: 16px;
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Closable tag styling */
    &.ant-tag-closable {
      padding-right: 8px;
      
      .ant-tag-close-icon {
        margin-left: 6px;
        color: rgba(0, 0, 0, 0.45);
        transition: color 0.3s ease;
        
        &:hover {
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
    
    /* Checkable tag styling */
    &.ant-tag-checkable {
      cursor: pointer;
      
      &:hover {
        background-color: #1890ff;
        color: #fff;
      }
      
      &.ant-tag-checkable-checked {
        background-color: #1890ff;
        color: #fff;
      }
    }
  }
  
  /* Tag group styling */
  .tag-group {
    .ant-tag {
      margin-bottom: 8px;
      margin-right: 8px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
`;

/**
 * Image component styling
 * 
 * Provides styling for image display components.
 */
export const ImageWrapper = styled.div`
  display: inline-block;
  
  .ant-image {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.02);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
    
    .ant-image-img {
      transition: all 0.3s ease;
    }
    
    .ant-image-mask {
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(4px);
      
      .ant-image-mask-info {
        .anticon {
          font-size: 24px;
          color: #fff;
        }
      }
    }
  }
  
  /* Image preview styling */
  .ant-image-preview {
    .ant-image-preview-img {
      max-width: 90vw;
      max-height: 90vh;
    }
  }
`;

/**
 * Typography component styling
 * 
 * Provides styling for typography display components.
 */
export const TypographyWrapper = styled.div`
  .ant-typography {
    /* Title styling */
    &.ant-typography-title {
      margin-bottom: 16px;
      
      &.ant-typography-title-1 {
        font-size: 32px;
        font-weight: 700;
        line-height: 1.2;
      }
      
      &.ant-typography-title-2 {
        font-size: 24px;
        font-weight: 600;
        line-height: 1.3;
      }
      
      &.ant-typography-title-3 {
        font-size: 20px;
        font-weight: 600;
        line-height: 1.4;
      }
      
      &.ant-typography-title-4 {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.4;
      }
      
      &.ant-typography-title-5 {
        font-size: 14px;
        font-weight: 600;
        line-height: 1.5;
      }
    }
    
    /* Text styling */
    &.ant-typography-text {
      &.ant-typography-text-secondary {
        color: #8c8c8c;
      }
      
      &.ant-typography-text-success {
        color: #52c41a;
      }
      
      &.ant-typography-text-warning {
        color: #faad14;
      }
      
      &.ant-typography-text-danger {
        color: #ff4d4f;
      }
    }
    
    /* Paragraph styling */
    &.ant-typography-paragraph {
      margin-bottom: 16px;
      line-height: 1.6;
    }
    
    /* Code styling */
    code.ant-typography-code {
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 2px 6px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    }
    
    /* Keyboard styling */
    kbd.ant-typography-keyboard {
      background-color: #fafafa;
      border: 1px solid #d9d9d9;
      border-bottom: 2px solid #b7b7b7;
      border-radius: 4px;
      padding: 2px 6px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    }
    
    /* Mark styling */
    mark.ant-typography-mark {
      background-color: #ffe58f;
      padding: 2px 4px;
      border-radius: 2px;
    }
  }
`;

/**
 * Statistic component styling
 * 
 * Provides styling for statistic display components.
 */
export const StatisticWrapper = styled.div`
  text-align: center;
  padding: 16px;
  
  .statistic-value {
    font-size: 32px;
    font-weight: 700;
    color: #1890ff;
    margin-bottom: 8px;
    line-height: 1.2;
    
    .statistic-prefix,
    .statistic-suffix {
      font-size: 24px;
      color: #8c8c8c;
      font-weight: 500;
    }
    
    .statistic-prefix {
      margin-right: 4px;
    }
    
    .statistic-suffix {
      margin-left: 4px;
    }
  }
  
  .statistic-title {
    font-size: 14px;
    color: #8c8c8c;
    font-weight: 500;
    margin: 0;
  }
  
  /* Colored statistic variants */
  &.statistic-success {
    .statistic-value {
      color: #52c41a;
    }
  }
  
  &.statistic-warning {
    .statistic-value {
      color: #faad14;
    }
  }
  
  &.statistic-error {
    .statistic-value {
      color: #ff4d4f;
    }
  }
  
  /* Card-style statistic */
  &.statistic-card {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
  }
`;
