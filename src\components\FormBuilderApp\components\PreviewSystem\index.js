/**
 * @fileoverview Professional Preview System barrel exports
 *
 * This module provides a comprehensive preview system for the form builder
 * with multiple device previews, interactive testing, validation preview,
 * and performance metrics display.
 *
 * @module PreviewSystem
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

// Main preview components
export { default as EnhancedPreviewTab } from './components/EnhancedPreviewTab';
export { default as DevicePreview } from './components/DevicePreview';
export { default as InteractiveTestMode } from './components/InteractiveTestMode';
export { default as ValidationPreview } from './components/ValidationPreview';
export { default as PerformanceMetrics } from './components/PerformanceMetrics';

// Preview controls
export { default as PreviewToolbar } from './components/PreviewToolbar';
export { default as DeviceSelector } from './components/DeviceSelector';
export { default as ZoomControls } from './components/ZoomControls';
export { default as PreviewActions } from './components/PreviewActions';

// Testing components
export { default as FormTester } from './components/FormTester';
export { default as ValidationTester } from './components/ValidationTester';
export { default as ResponsiveTester } from './components/ResponsiveTester';

// Hooks
export { useDevicePreview } from './hooks/useDevicePreview';
export { useFormTesting } from './hooks/useFormTesting';
export { useValidationTesting } from './hooks/useValidationTesting';
export { usePerformanceMonitoring } from './hooks/usePerformanceMonitoring';

// Constants
export * from './constants/previewConstants';

// Utilities
export * from './utils/previewUtils';
