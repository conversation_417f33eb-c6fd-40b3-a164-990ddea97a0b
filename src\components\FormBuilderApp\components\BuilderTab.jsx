/**
 * @fileoverview Builder Tab component for form builder interface
 *
 * This component renders the main form builder tab with AI chat section,
 * categorized sidebar, and drag-and-drop canvas with splitter layout.
 *
 * @module BuilderTab
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { Suspense, useCallback, useState } from 'react';
import {
  Spin,
  Splitter,
  FloatButton,
  Button,
  Switch,
  Space,
  Tooltip,
} from 'antd';
import {
  AppstoreOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import {
  DropZone,
  TrashDropZone,
  Row,
  AIChatSection,
} from '../../../components';
import CategorizedSidebar from '../../../components/CategorizedSidebar';
import PropertiesPanel, {
  PropertiesPanelProvider,
  usePropertiesPanelContext,
} from './PropertiesPanel';
import ComponentManagerPanel from './ComponentManagement/components/ComponentManagerPanel';
import { useAIChatToggle } from '../hooks/useAIChatToggle';
import * as S from '../../../styles';

// Microsoft Office-style Canvas Toolbar with enterprise-grade design
const CanvasToolbar = ({
  activeBreakpoint,
  zoom,
  onBreakpointChange,
  onZoomChange,
  isAIChatEnabled,
  onAIToggle,
}) => {
  const handleZoomIn = () => onZoomChange?.(Math.min(zoom + 0.25, 2));
  const handleZoomOut = () => onZoomChange?.(Math.max(zoom - 0.25, 0.25));
  const handleZoomReset = () => onZoomChange?.(1);

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '12px 20px',
        background: 'linear-gradient(180deg, #ffffff 0%, #fafafa 100%)',
        borderBottom: '1px solid #e1e1e1',
        minHeight: '56px',
        backdropFilter: 'blur(10px)',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        position: 'relative',
        zIndex: 10,
      }}
    >
      {/* Left Section - Breakpoint & Status Info */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '6px 12px',
            background: '#f0f2f5',
            borderRadius: '6px',
            border: '1px solid #d9d9d9',
          }}
        >
          <div
            style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: '#52c41a',
              boxShadow: '0 0 4px rgba(82, 196, 26, 0.4)',
            }}
          />
          <span
            style={{
              fontSize: '12px',
              color: '#595959',
              fontWeight: 600,
              letterSpacing: '0.3px',
            }}
          >
            {activeBreakpoint?.toUpperCase()} • READY
          </span>
        </div>
      </div>

      {/* Center Section - AI Assistant Toggle with Microsoft styling */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '8px 16px',
          background: isAIChatEnabled
            ? 'rgba(24, 144, 255, 0.08)'
            : 'rgba(0, 0, 0, 0.04)',
          borderRadius: '8px',
          border: `1px solid ${
            isAIChatEnabled ? 'rgba(24, 144, 255, 0.2)' : 'rgba(0, 0, 0, 0.06)'
          }`,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          cursor: 'pointer',
        }}
        onClick={onAIToggle}
      >
        <RobotOutlined
          style={{
            color: isAIChatEnabled ? '#1890ff' : '#8c8c8c',
            fontSize: '16px',
            transition: 'all 0.3s ease',
          }}
        />
        <Switch
          size='small'
          checked={isAIChatEnabled}
          onChange={onAIToggle}
          style={{
            pointerEvents: 'none',
          }}
        />
        <span
          style={{
            fontSize: '13px',
            color: isAIChatEnabled ? '#1890ff' : '#595959',
            fontWeight: 600,
            transition: 'all 0.3s ease',
          }}
        >
          AI Assistant
        </span>
        <div
          style={{
            fontSize: '10px',
            color: isAIChatEnabled ? '#1890ff' : '#8c8c8c',
            fontWeight: 500,
            opacity: 0.8,
          }}
        >
          {isAIChatEnabled ? 'ON' : 'OFF'}
        </div>
      </div>

      {/* Right Section - Professional Zoom Controls with Microsoft Office styling */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '2px',
          background: '#ffffff',
          borderRadius: '8px',
          border: '1px solid #e1e1e1',
          padding: '4px',
          boxShadow: '0 1px 4px rgba(0, 0, 0, 0.08)',
        }}
      >
        <Space size={2}>
          <Tooltip title='Zoom Out (Ctrl + -)' placement='bottom'>
            <Button
              type='text'
              size='small'
              icon={<ZoomOutOutlined />}
              onClick={handleZoomOut}
              disabled={zoom <= 0.25}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                border: 'none',
                background: zoom <= 0.25 ? '#f5f5f5' : 'transparent',
                color: zoom <= 0.25 ? '#bfbfbf' : '#595959',
              }}
            />
          </Tooltip>

          <div
            style={{
              minWidth: '70px',
              textAlign: 'center',
              fontSize: '13px',
              fontWeight: 600,
              color: '#262626',
              padding: '6px 12px',
              background: '#fafafa',
              borderRadius: '6px',
              border: '1px solid #e8e8e8',
              fontFamily: 'SF Mono, Monaco, Consolas, monospace',
              letterSpacing: '0.5px',
            }}
          >
            {Math.round(zoom * 100)}%
          </div>

          <Tooltip title='Zoom In (Ctrl + +)' placement='bottom'>
            <Button
              type='text'
              size='small'
              icon={<ZoomInOutlined />}
              onClick={handleZoomIn}
              disabled={zoom >= 2}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                border: 'none',
                background: zoom >= 2 ? '#f5f5f5' : 'transparent',
                color: zoom >= 2 ? '#bfbfbf' : '#595959',
              }}
            />
          </Tooltip>

          <div
            style={{
              width: '1px',
              height: '24px',
              background: '#e8e8e8',
              margin: '0 4px',
            }}
          />

          <Tooltip title='Fit to Window (Ctrl + 0)' placement='bottom'>
            <Button
              type='text'
              size='small'
              icon={<FullscreenOutlined />}
              onClick={handleZoomReset}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                border: 'none',
                color: '#595959',
              }}
            />
          </Tooltip>
        </Space>
      </div>
    </div>
  );
};

/**
 * Properties Panel Wrapper component
 *
 * Renders the properties panel using the context data
 */
const PropertiesPanelWrapper = () => {
  const {
    isVisible,
    targetElement,
    componentData,
    componentId,
    formSchema,
    closePanel,
    handlePropertyUpdate,
  } = usePropertiesPanelContext();

  return (
    <PropertiesPanel
      visible={isVisible}
      targetElement={targetElement}
      componentData={componentData}
      componentId={componentId}
      onClose={closePanel}
      onUpdateComponent={handlePropertyUpdate}
      formSchema={formSchema}
    />
  );
};

/**
 * Builder Tab component
 *
 * Renders the complete form builder interface including AI chat section,
 * component sidebar, and drag-and-drop canvas with optimized performance.
 *
 * @param {Object} props - Component props
 * @param {Array} props.layout - Current form layout structure
 * @param {Object} props.components - Component registry object
 * @param {number} props.forceRenderKey - Key for forcing re-renders
 * @param {Function} props.handleDrop - Drop handler function
 * @param {Function} props.handleDropToTrashBin - Trash drop handler function
 * @param {Function} props.handleAIFormGenerated - AI form generation handler
 * @param {Function} props.handleSchemaUpdate - Schema update handler
 * @param {Function} props.handleComponentUpdate - Component update handler
 * @param {Object} props.currentFormSchema - Current form schema object
 *
 * @returns {React.ReactNode} Builder tab JSX
 *
 * @example
 * ```jsx
 * <BuilderTab
 *   layout={layout}
 *   components={components}
 *   forceRenderKey={forceRenderKey}
 *   handleDrop={handleDrop}
 *   handleDropToTrashBin={handleDropToTrashBin}
 *   handleAIFormGenerated={handleAIFormGenerated}
 *   handleSchemaUpdate={handleSchemaUpdate}
 *   handleComponentUpdate={handleComponentUpdate}
 *   currentFormSchema={currentFormSchema}
 * />
 * ```
 */
const BuilderTab = ({
  layout,
  components,
  forceRenderKey,
  handleDrop,
  handleDropToTrashBin,
  handleAIFormGenerated,
  handleSchemaUpdate,
  handleComponentUpdate,
  currentFormSchema,
}) => {
  // AI Chat Toggle Hook
  const { isAIChatEnabled, toggleAIChat } = useAIChatToggle();

  // Component Manager Panel state
  const [isComponentManagerVisible, setIsComponentManagerVisible] =
    useState(false);
  const [selectedComponentId, setSelectedComponentId] = useState(null);

  // Essential Canvas state (removed align, distribute, grid options)
  const [activeBreakpoint, setActiveBreakpoint] = useState('lg');
  const [canvasZoom, setCanvasZoom] = useState(1);

  /**
   * Handle component manager toggle
   */
  const handleComponentManagerToggle = useCallback(() => {
    setIsComponentManagerVisible(!isComponentManagerVisible);
  }, [isComponentManagerVisible]);

  /**
   * Handle component selection from manager
   */
  const handleComponentSelect = useCallback((componentId) => {
    setSelectedComponentId(componentId);
    // Additional logic to highlight component in canvas
  }, []);

  /**
   * Handle component deletion from manager
   */
  const handleComponentDelete = useCallback((componentId) => {
    // Implement component deletion logic
    console.log('Delete component:', componentId);
  }, []);

  /**
   * Handle component duplication from manager
   */
  const handleComponentDuplicate = useCallback((componentId) => {
    // Implement component duplication logic
    console.log('Duplicate component:', componentId);
  }, []);

  /**
   * Memoized row renderer for better performance
   *
   * Renders individual rows in the form builder canvas with
   * optimized re-rendering and proper prop passing.
   *
   * @param {Object} row - Row data object
   * @param {string} currentPath - Current path for the row
   * @returns {React.ReactNode} Rendered row component
   */
  const renderRow = useCallback(
    (row, currentPath) => {
      return (
        <Row
          key={row.id}
          data={row}
          handleDrop={handleDrop}
          components={components}
          path={currentPath}
          onUpdateComponent={handleComponentUpdate}
        />
      );
    },
    [handleDrop, components, handleComponentUpdate],
  );

  return (
    <PropertiesPanelProvider
      onUpdateComponent={handleComponentUpdate}
      formSchema={currentFormSchema}
    >
      <S.BuilderContainer>
        <Splitter
          style={{
            height: '100%',
            background: 'transparent',
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          split='vertical'
          resizerStyle={{
            background: '#f0f0f0',
            border: '1px solid #d9d9d9',
            borderRadius: '2px',
            width: '8px',
            cursor: 'col-resize',
            transition: 'all 0.2s ease',
            '&:hover': {
              background: '#1890ff',
              borderColor: '#1890ff',
            },
          }}
        >
          {/* Conditional AI Chat Section Panel - Optimized for workspace efficiency */}
          {isAIChatEnabled && (
            <Splitter.Panel
              defaultSize='20%'
              min='280px'
              max='400px'
              style={{
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                borderRight: '1px solid #e8e8e8',
                background: '#fafafa',
              }}
            >
              <S.AIChatSectionContainer
                style={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  overflow: 'hidden',
                }}
              >
                <Suspense
                  fallback={
                    <div
                      style={{
                        padding: '24px',
                        textAlign: 'center',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '200px',
                      }}
                    >
                      <Spin size='large' />
                      <div
                        style={{
                          marginTop: 16,
                          fontSize: 14,
                          color: '#666',
                          fontWeight: 500,
                        }}
                      >
                        Loading AI Assistant...
                      </div>
                    </div>
                  }
                >
                  <AIChatSection
                    onFormGenerated={handleAIFormGenerated}
                    currentSchema={currentFormSchema}
                    onSchemaUpdate={handleSchemaUpdate}
                  />
                </Suspense>
              </S.AIChatSectionContainer>
            </Splitter.Panel>
          )}

          {/* Component Sidebar Panel - Compact design for maximum canvas space */}
          <Splitter.Panel
            defaultSize='15%'
            min='250px'
            max='350px'
            style={{
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              borderRight: '1px solid #e8e8e8',
              background: '#ffffff',
              boxShadow: isAIChatEnabled
                ? 'none'
                : '2px 0 8px rgba(0, 0, 0, 0.06)',
            }}
          >
            <S.ComponentsSectionContainer
              style={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
              }}
            >
              <CategorizedSidebar />
            </S.ComponentsSectionContainer>
          </Splitter.Panel>

          {/* Form Builder Canvas Panel - Maximum workspace for optimal productivity */}
          <Splitter.Panel
            defaultSize={isAIChatEnabled ? '65%' : '85%'}
            min='450px'
            style={{
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              background: '#ffffff',
              position: 'relative',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              {/* Enhanced Canvas Toolbar with Microsoft-style design */}
              <CanvasToolbar
                activeBreakpoint={activeBreakpoint}
                zoom={canvasZoom}
                onBreakpointChange={setActiveBreakpoint}
                onZoomChange={setCanvasZoom}
                isAIChatEnabled={isAIChatEnabled}
                onAIToggle={toggleAIChat}
              />

              <S.FormBuilderCanvasContainer
                key={`builder-${forceRenderKey}`}
                style={{
                  flex: 1,
                  transform: `scale(${canvasZoom})`,
                  transformOrigin: 'top left',
                  transition: 'transform 0.3s ease',
                }}
              >
                <S.PageContainer>
                  <S.Page>
                    {layout.map((row, index) => {
                      const currentPath = `${index}`;

                      return (
                        <React.Fragment key={`${row.id}-${forceRenderKey}`}>
                          <DropZone
                            data={{
                              path: currentPath,
                              childrenCount: layout.length,
                            }}
                            onDrop={handleDrop}
                            path={currentPath}
                          />
                          {renderRow(row, currentPath)}
                        </React.Fragment>
                      );
                    })}
                    <DropZone
                      data={{
                        path: `${layout.length}`,
                        childrenCount: layout.length,
                      }}
                      onDrop={handleDrop}
                      isLast
                    />
                  </S.Page>

                  <TrashDropZone
                    data={{
                      layout,
                    }}
                    onDrop={handleDropToTrashBin}
                  />
                </S.PageContainer>
              </S.FormBuilderCanvasContainer>
            </div>
          </Splitter.Panel>
        </Splitter>

        {/* Properties Panel */}
        <PropertiesPanelWrapper />

        {/* Component Manager Panel */}
        <ComponentManagerPanel
          visible={isComponentManagerVisible}
          components={layout}
          selectedComponentId={selectedComponentId}
          onComponentSelect={handleComponentSelect}
          onComponentUpdate={handleComponentUpdate}
          onComponentDelete={handleComponentDelete}
          onComponentDuplicate={handleComponentDuplicate}
          onClose={() => setIsComponentManagerVisible(false)}
          width={380}
          collapsible={true}
        />

        {/* Floating Action Button for Component Manager */}
        <FloatButton
          icon={<AppstoreOutlined />}
          tooltip='Component Manager'
          onClick={handleComponentManagerToggle}
          style={{
            right: 24,
            bottom: 80,
            width: 48,
            height: 48,
          }}
          type={isComponentManagerVisible ? 'primary' : 'default'}
        />
      </S.BuilderContainer>
    </PropertiesPanelProvider>
  );
};

export default BuilderTab;
