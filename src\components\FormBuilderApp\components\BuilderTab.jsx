/**
 * @fileoverview Builder Tab component for form builder interface
 *
 * This component renders the main form builder tab with AI chat section,
 * categorized sidebar, and drag-and-drop canvas with splitter layout.
 *
 * @module BuilderTab
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { Suspense, useCallback, useState } from 'react';
import { Spin, Splitter, FloatButton } from 'antd';
import { AppstoreOutlined } from '@ant-design/icons';
import {
  DropZone,
  TrashDropZone,
  Row,
  AIChatSection,
} from '../../../components';
import CategorizedSidebar from '../../../components/CategorizedSidebar';
import PropertiesPanel, {
  PropertiesPanelProvider,
  usePropertiesPanelContext,
} from './PropertiesPanel';
import ComponentManagerPanel from './ComponentManagement/components/ComponentManagerPanel';
import * as S from '../../../styles';

// Simple Canvas Toolbar component (inline)
const CanvasToolbar = ({
  activeBreakpoint,
  zoom,
  onBreakpointChange,
  onZoomChange,
}) => {
  const handleZoomIn = () => onZoomChange?.(Math.min(zoom + 0.25, 2));
  const handleZoomOut = () => onZoomChange?.(Math.max(zoom - 0.25, 0.25));
  const handleZoomReset = () => onZoomChange?.(1);

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '8px 16px',
        background: 'rgba(255, 255, 255, 0.95)',
        borderBottom: '1px solid #e8e8e8',
        minHeight: '48px',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <span style={{ fontSize: '12px', color: '#666', fontWeight: 500 }}>
          Breakpoint: {activeBreakpoint?.toUpperCase()}
        </span>
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <button onClick={handleZoomOut} disabled={zoom <= 0.25}>
          -
        </button>
        <span
          style={{ minWidth: '60px', textAlign: 'center', fontSize: '12px' }}
        >
          {Math.round(zoom * 100)}%
        </span>
        <button onClick={handleZoomIn} disabled={zoom >= 2}>
          +
        </button>
        <button onClick={handleZoomReset}>Fit</button>
      </div>
    </div>
  );
};

/**
 * Properties Panel Wrapper component
 *
 * Renders the properties panel using the context data
 */
const PropertiesPanelWrapper = () => {
  const {
    isVisible,
    targetElement,
    componentData,
    componentId,
    formSchema,
    closePanel,
    handlePropertyUpdate,
  } = usePropertiesPanelContext();

  return (
    <PropertiesPanel
      visible={isVisible}
      targetElement={targetElement}
      componentData={componentData}
      componentId={componentId}
      onClose={closePanel}
      onUpdateComponent={handlePropertyUpdate}
      formSchema={formSchema}
    />
  );
};

/**
 * Builder Tab component
 *
 * Renders the complete form builder interface including AI chat section,
 * component sidebar, and drag-and-drop canvas with optimized performance.
 *
 * @param {Object} props - Component props
 * @param {Array} props.layout - Current form layout structure
 * @param {Object} props.components - Component registry object
 * @param {number} props.forceRenderKey - Key for forcing re-renders
 * @param {Function} props.handleDrop - Drop handler function
 * @param {Function} props.handleDropToTrashBin - Trash drop handler function
 * @param {Function} props.handleAIFormGenerated - AI form generation handler
 * @param {Function} props.handleSchemaUpdate - Schema update handler
 * @param {Function} props.handleComponentUpdate - Component update handler
 * @param {Object} props.currentFormSchema - Current form schema object
 *
 * @returns {React.ReactNode} Builder tab JSX
 *
 * @example
 * ```jsx
 * <BuilderTab
 *   layout={layout}
 *   components={components}
 *   forceRenderKey={forceRenderKey}
 *   handleDrop={handleDrop}
 *   handleDropToTrashBin={handleDropToTrashBin}
 *   handleAIFormGenerated={handleAIFormGenerated}
 *   handleSchemaUpdate={handleSchemaUpdate}
 *   handleComponentUpdate={handleComponentUpdate}
 *   currentFormSchema={currentFormSchema}
 * />
 * ```
 */
const BuilderTab = ({
  layout,
  components,
  forceRenderKey,
  handleDrop,
  handleDropToTrashBin,
  handleAIFormGenerated,
  handleSchemaUpdate,
  handleComponentUpdate,
  currentFormSchema,
}) => {
  // Component Manager Panel state
  const [isComponentManagerVisible, setIsComponentManagerVisible] =
    useState(false);
  const [selectedComponentId, setSelectedComponentId] = useState(null);

  // Essential Canvas state (removed align, distribute, grid options)
  const [activeBreakpoint, setActiveBreakpoint] = useState('lg');
  const [canvasZoom, setCanvasZoom] = useState(1);

  /**
   * Handle component manager toggle
   */
  const handleComponentManagerToggle = useCallback(() => {
    setIsComponentManagerVisible(!isComponentManagerVisible);
  }, [isComponentManagerVisible]);

  /**
   * Handle component selection from manager
   */
  const handleComponentSelect = useCallback((componentId) => {
    setSelectedComponentId(componentId);
    // Additional logic to highlight component in canvas
  }, []);

  /**
   * Handle component deletion from manager
   */
  const handleComponentDelete = useCallback((componentId) => {
    // Implement component deletion logic
    console.log('Delete component:', componentId);
  }, []);

  /**
   * Handle component duplication from manager
   */
  const handleComponentDuplicate = useCallback((componentId) => {
    // Implement component duplication logic
    console.log('Duplicate component:', componentId);
  }, []);

  /**
   * Memoized row renderer for better performance
   *
   * Renders individual rows in the form builder canvas with
   * optimized re-rendering and proper prop passing.
   *
   * @param {Object} row - Row data object
   * @param {string} currentPath - Current path for the row
   * @returns {React.ReactNode} Rendered row component
   */
  const renderRow = useCallback(
    (row, currentPath) => {
      return (
        <Row
          key={row.id}
          data={row}
          handleDrop={handleDrop}
          components={components}
          path={currentPath}
          onUpdateComponent={handleComponentUpdate}
        />
      );
    },
    [handleDrop, components, handleComponentUpdate],
  );

  return (
    <PropertiesPanelProvider
      onUpdateComponent={handleComponentUpdate}
      formSchema={currentFormSchema}
    >
      <S.BuilderContainer>
        <Splitter style={{ height: '100%', background: 'transparent' }}>
          {/* AI Chat Section Panel */}
          <Splitter.Panel defaultSize='25%' min='280px' max='400px'>
            <S.AIChatSectionContainer>
              <Suspense
                fallback={
                  <div style={{ padding: '16px', textAlign: 'center' }}>
                    <Spin size='small' />
                    <div style={{ marginTop: 8, fontSize: 12 }}>
                      Loading AI Chat...
                    </div>
                  </div>
                }
              >
                <AIChatSection
                  onFormGenerated={handleAIFormGenerated}
                  currentSchema={currentFormSchema}
                  onSchemaUpdate={handleSchemaUpdate}
                />
              </Suspense>
            </S.AIChatSectionContainer>
          </Splitter.Panel>

          {/* Component Sidebar Panel */}
          <Splitter.Panel defaultSize='25%' min='250px' max='350px'>
            <S.ComponentsSectionContainer>
              <CategorizedSidebar />
            </S.ComponentsSectionContainer>
          </Splitter.Panel>

          {/* Form Builder Canvas Panel */}
          <Splitter.Panel defaultSize='50%' min='400px'>
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
              }}
            >
              {/* Essential Canvas Toolbar (no align/distribute/grid) */}
              <CanvasToolbar
                activeBreakpoint={activeBreakpoint}
                zoom={canvasZoom}
                onBreakpointChange={setActiveBreakpoint}
                onZoomChange={setCanvasZoom}
              />

              <S.FormBuilderCanvasContainer
                key={`builder-${forceRenderKey}`}
                style={{
                  flex: 1,
                  transform: `scale(${canvasZoom})`,
                  transformOrigin: 'top left',
                  transition: 'transform 0.3s ease',
                }}
              >
                <S.PageContainer>
                  <S.Page>
                    {layout.map((row, index) => {
                      const currentPath = `${index}`;

                      return (
                        <React.Fragment key={`${row.id}-${forceRenderKey}`}>
                          <DropZone
                            data={{
                              path: currentPath,
                              childrenCount: layout.length,
                            }}
                            onDrop={handleDrop}
                            path={currentPath}
                          />
                          {renderRow(row, currentPath)}
                        </React.Fragment>
                      );
                    })}
                    <DropZone
                      data={{
                        path: `${layout.length}`,
                        childrenCount: layout.length,
                      }}
                      onDrop={handleDrop}
                      isLast
                    />
                  </S.Page>

                  <TrashDropZone
                    data={{
                      layout,
                    }}
                    onDrop={handleDropToTrashBin}
                  />
                </S.PageContainer>
              </S.FormBuilderCanvasContainer>
            </div>
          </Splitter.Panel>
        </Splitter>

        {/* Properties Panel */}
        <PropertiesPanelWrapper />

        {/* Component Manager Panel */}
        <ComponentManagerPanel
          visible={isComponentManagerVisible}
          components={layout}
          selectedComponentId={selectedComponentId}
          onComponentSelect={handleComponentSelect}
          onComponentUpdate={handleComponentUpdate}
          onComponentDelete={handleComponentDelete}
          onComponentDuplicate={handleComponentDuplicate}
          onClose={() => setIsComponentManagerVisible(false)}
          width={380}
          collapsible={true}
        />

        {/* Floating Action Button for Component Manager */}
        <FloatButton
          icon={<AppstoreOutlined />}
          tooltip='Component Manager'
          onClick={handleComponentManagerToggle}
          style={{
            right: 24,
            bottom: 80,
            width: 48,
            height: 48,
          }}
          type={isComponentManagerVisible ? 'primary' : 'default'}
        />
      </S.BuilderContainer>
    </PropertiesPanelProvider>
  );
};

export default BuilderTab;
