/**
 * SidebarHeader Component
 * 
 * Header section of the CategorizedSidebar containing the title, component count,
 * description, and search functionality. Provides the main interface for users
 * to understand and interact with the component library.
 * 
 * Features:
 * - Component library branding with icon and title
 * - Real-time component count display with badge
 * - Descriptive text for user guidance
 * - Integrated search functionality with enhanced UX
 * - Responsive design with consistent spacing
 * - Accessibility-compliant design patterns
 * 
 * Props:
 * - totalComponents: Total number of available components
 * - searchTerm: Current search input value
 * - onSearchChange: Callback function for search input changes
 * - isSearchActive: Boolean indicating if search is currently active
 * 
 * @param {Object} props - Component props
 * @returns {JSX.Element} Rendered sidebar header component
 */

import React, { memo } from 'react';
import { Input, Badge, Typography, Space } from 'antd';
import { SearchOutlined, AppstoreOutlined } from '@ant-design/icons';
import { SidebarHeader as StyledSidebarHeader, SearchContainer } from '../styles/StyledComponents';
import { colors } from '../../../styles/theme';

const { Title, Text } = Typography;

/**
 * SidebarHeader component for the categorized sidebar
 * 
 * This component provides the main header interface for the sidebar,
 * including branding, search functionality, and component statistics.
 * 
 * Design Principles:
 * - Clear visual hierarchy with title and description
 * - Prominent search functionality for quick component discovery
 * - Real-time feedback with component counts
 * - Consistent spacing and typography
 * 
 * @param {Object} props - Component props
 * @param {number} props.totalComponents - Total number of components available
 * @param {string} props.searchTerm - Current search input value
 * @param {Function} props.onSearchChange - Handler for search input changes
 * @param {boolean} props.isSearchActive - Whether search is currently active
 * @returns {JSX.Element} Rendered header component
 */
const SidebarHeader = memo(({ 
  totalComponents = 0, 
  searchTerm = '', 
  onSearchChange, 
  isSearchActive = false 
}) => {
  /**
   * Handles search input changes
   * 
   * Processes search input events and calls the parent callback
   * with the new search value. Includes basic validation and
   * event handling.
   * 
   * @param {Event} e - Input change event
   */
  const handleSearchChange = (e) => {
    const value = e.target.value;
    if (onSearchChange) {
      onSearchChange(value);
    }
  };

  /**
   * Gets the appropriate placeholder text for the search input
   * 
   * Provides contextual placeholder text based on the current
   * state and available components.
   * 
   * @returns {string} Placeholder text for search input
   */
  const getSearchPlaceholder = () => {
    if (totalComponents === 0) {
      return '🔍 No components available...';
    }
    return '🔍 Search components, categories...';
  };

  /**
   * Gets the badge color based on component count
   * 
   * Provides visual feedback about the number of available
   * components using color coding.
   * 
   * @returns {string} Color for the component count badge
   */
  const getBadgeColor = () => {
    if (totalComponents === 0) return colors.textTertiary;
    if (totalComponents < 10) return colors.warning;
    if (totalComponents < 25) return colors.info;
    return colors.primary;
  };

  return (
    <StyledSidebarHeader>
      {/* Main header content with title and component count */}
      <Space direction='vertical' size='small' style={{ width: '100%' }}>
        {/* Title section with icon, text, and badge */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '8px',
          marginBottom: '4px' 
        }}>
          {/* Component library icon */}
          <AppstoreOutlined
            style={{ 
              fontSize: '18px', 
              color: colors.primary,
              flexShrink: 0 
            }}
          />
          
          {/* Main title */}
          <Title 
            level={4} 
            style={{ 
              margin: 0, 
              color: colors.textPrimary,
              fontSize: '16px',
              fontWeight: 600,
              lineHeight: 1.2
            }}
          >
            Components
          </Title>
          
          {/* Component count badge */}
          <Badge
            count={totalComponents}
            style={{
              backgroundColor: getBadgeColor(),
              fontSize: '10px',
              height: '18px',
              minWidth: '18px',
              lineHeight: '18px',
              fontWeight: 500,
            }}
            showZero
          />
        </div>
        
        {/* Descriptive text for user guidance */}
        <Text 
          type='secondary' 
          style={{ 
            fontSize: '12px',
            lineHeight: 1.3,
            color: colors.textSecondary
          }}
        >
          {totalComponents > 0 
            ? 'Drag components to build your form'
            : 'No components available'
          }
        </Text>
      </Space>

      {/* Search functionality */}
      <SearchContainer>
        <Input
          placeholder={getSearchPlaceholder()}
          prefix={
            <SearchOutlined 
              style={{ 
                color: isSearchActive ? colors.primary : colors.textSecondary,
                transition: 'color 0.2s ease'
              }} 
            />
          }
          value={searchTerm}
          onChange={handleSearchChange}
          allowClear
          size='middle'
          disabled={totalComponents === 0}
          style={{
            borderColor: isSearchActive ? colors.primary : undefined,
          }}
        />
      </SearchContainer>
    </StyledSidebarHeader>
  );
});

// Set display name for debugging
SidebarHeader.displayName = 'SidebarHeader';

export default SidebarHeader;
