import React, { memo } from 'react';
import { Form, Button, Space } from 'antd';
import LayoutRenderer from './LayoutRenderer';
import {
  useFormSubmission,
  useFormValidation,
  useFormProps,
  useFormState,
} from './hooks';
// Note: FormWrapper, FormActions, and DEFAULT_FORM_RENDERER_PROPS are available but not currently used
// Keeping imports commented for future use
// import { FormWrapper, FormActions } from './styles';
// import { DEFAULT_FORM_RENDERER_PROPS } from './constants';

// Memoized FormRenderer for better performance
const FormRenderer = memo(
  ({
    layout,
    components,
    onSubmit,
    onValuesChange,
    initialValues = {},
    formProps = {},
    showSubmitButton = true,
    submitButtonText = 'Submit',
    showResetButton = true,
    resetButtonText = 'Reset',
    buttonProps = {},
    className,
    style,
  }) => {
    // Use extracted hooks for form logic
    const { loading, handleSubmit, handleFinishFailed } =
      useFormSubmission(onSubmit);
    const { handleValuesChange: handleFormValuesChange } =
      useFormValidation(onValuesChange);
    const {
      form,
      defaultFormProps,
      formItemStyle,
      submitButtonProps,
      resetButtonProps,
    } = useFormProps({
      initialValues,
      formProps,
      handleSubmit,
      handleFinishFailed,
      handleValuesChange: handleFormValuesChange,
      buttonProps,
      loading,
    });
    const { handleReset } = useFormState(form);

    return (
      <div className={className} style={style}>
        <Form {...defaultFormProps}>
          <LayoutRenderer
            layout={layout}
            components={components}
            formInstance={form}
            onValuesChange={handleFormValuesChange}
          />

          {(showSubmitButton || showResetButton) && (
            <Form.Item style={formItemStyle}>
              <Space>
                {showSubmitButton && (
                  <Button {...submitButtonProps}>{submitButtonText}</Button>
                )}
                {showResetButton && (
                  <Button {...resetButtonProps} onClick={handleReset}>
                    {resetButtonText}
                  </Button>
                )}
              </Space>
            </Form.Item>
          )}
        </Form>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison to ensure re-render when layout or components change - avoid JSON.stringify
    if (
      prevProps.onSubmit !== nextProps.onSubmit ||
      prevProps.onValuesChange !== nextProps.onValuesChange ||
      prevProps.showSubmitButton !== nextProps.showSubmitButton ||
      prevProps.showResetButton !== nextProps.showResetButton ||
      prevProps.submitButtonText !== nextProps.submitButtonText ||
      prevProps.resetButtonText !== nextProps.resetButtonText ||
      prevProps.className !== nextProps.className ||
      prevProps.style !== nextProps.style
    ) {
      return false;
    }

    // Compare layout array length and structure (shallow comparison)
    const prevLayout = prevProps.layout || [];
    const nextLayout = nextProps.layout || [];

    if (prevLayout.length !== nextLayout.length) {
      return false;
    }

    // Compare layout items by ID and type only
    for (let i = 0; i < prevLayout.length; i++) {
      if (
        prevLayout[i]?.id !== nextLayout[i]?.id ||
        prevLayout[i]?.type !== nextLayout[i]?.type ||
        prevLayout[i]?.children?.length !== nextLayout[i]?.children?.length
      ) {
        return false;
      }
    }

    // Compare components object keys (shallow comparison)
    const prevComponentKeys = Object.keys(prevProps.components || {});
    const nextComponentKeys = Object.keys(nextProps.components || {});

    if (prevComponentKeys.length !== nextComponentKeys.length) {
      return false;
    }

    // Check if component keys are the same
    for (const key of prevComponentKeys) {
      if (
        !nextProps.components[key] ||
        prevProps.components[key] !== nextProps.components[key]
      ) {
        return false;
      }
    }

    // Compare initialValues and formProps (shallow comparison of keys)
    const prevInitialKeys = Object.keys(prevProps.initialValues || {});
    const nextInitialKeys = Object.keys(nextProps.initialValues || {});
    const prevFormPropKeys = Object.keys(prevProps.formProps || {});
    const nextFormPropKeys = Object.keys(nextProps.formProps || {});

    if (
      prevInitialKeys.length !== nextInitialKeys.length ||
      prevFormPropKeys.length !== nextFormPropKeys.length
    ) {
      return false;
    }

    return true;
  },
);

// Set display name for debugging
FormRenderer.displayName = 'FormRenderer';

export default FormRenderer;
