/**
 * @fileoverview Styled components for Properties Panel
 *
 * This module provides styled components for the properties panel
 * with responsive design, animations, and accessibility features.
 *
 * @module PropertiesPanel.styles
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import {
  PANEL_DIMENSIONS,
  Z_INDEX_LEVELS,
  RESPONSIVE_BREAKPOINTS,
  TOUCH_TARGETS,
} from '../constants/propertiesPanelConstants';

/**
 * Main panel container with positioning and responsive behavior
 */
export const PanelContainer = styled(motion.div)`
  position: fixed;
  width: ${PANEL_DIMENSIONS.WIDTH.DESKTOP}px;
  max-height: ${PANEL_DIMENSIONS.HEIGHT.MAX_DESKTOP}px;
  background: #ffffff;
  border-radius: ${PANEL_DIMENSIONS.BORDER_RADIUS}px;
  box-shadow: ${PANEL_DIMENSIONS.SHADOW};
  border: 1px solid #e8e8e8;
  z-index: ${Z_INDEX_LEVELS.PANEL};

  /* Allow caret to be visible outside panel bounds */
  overflow: visible;

  /* Ensure content area handles its own overflow */
  display: flex;
  flex-direction: column;

  /* Responsive design */
  @media (max-width: ${RESPONSIVE_BREAKPOINTS.TABLET}px) {
    width: ${PANEL_DIMENSIONS.WIDTH.TABLET}px;
    max-height: ${PANEL_DIMENSIONS.HEIGHT.MAX_TABLET}px;
  }

  @media (max-width: ${RESPONSIVE_BREAKPOINTS.MOBILE}px) {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: ${PANEL_DIMENSIONS.HEIGHT.MOBILE};
    max-height: ${PANEL_DIMENSIONS.HEIGHT.MOBILE};
    border-radius: ${PANEL_DIMENSIONS.BORDER_RADIUS}px
      ${PANEL_DIMENSIONS.BORDER_RADIUS}px 0 0;
    box-shadow: 0 -4px 24px rgba(0, 0, 0, 0.15);

    /* Ensure proper stacking on mobile */
    z-index: ${Z_INDEX_LEVELS.PANEL + 1};

    /* Add safe area padding for devices with notches */
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Tablet-specific optimizations */
  @media (min-width: ${RESPONSIVE_BREAKPOINTS.MOBILE +
    1}px) and (max-width: ${RESPONSIVE_BREAKPOINTS.TABLET}px) {
    /* Slightly smaller on tablet for better fit */
    width: ${PANEL_DIMENSIONS.WIDTH.TABLET}px;
    max-height: ${PANEL_DIMENSIONS.HEIGHT.MAX_TABLET}px;

    /* Better positioning for tablet landscape */
    @media (orientation: landscape) {
      max-height: 80vh;
    }
  }
`;

/**
 * Panel backdrop for mobile overlay
 */
export const PanelBackdrop = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: ${Z_INDEX_LEVELS.BACKDROP};

  /* Only show on mobile */
  display: none;

  @media (max-width: ${RESPONSIVE_BREAKPOINTS.MOBILE}px) {
    display: block;
  }
`;

/**
 * Visual caret/arrow pointer
 */
export const PanelCaret = styled.div`
  position: absolute;
  width: ${PANEL_DIMENSIONS.CARET_SIZE}px;
  height: ${PANEL_DIMENSIONS.CARET_SIZE}px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  transform: rotate(45deg);
  z-index: ${Z_INDEX_LEVELS.CARET};

  /* Subtle enterprise-grade shadow */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);

  /* Ensure caret is always visible but subtle */
  pointer-events: none;
  display: block !important;
  visibility: visible !important;
  opacity: 0.92 !important;

  /* Prevent any potential clipping */
  overflow: visible;

  /* Debug outline - remove in production */
  /* outline: 2px solid red; */

  ${({ position, offset }) => {
    const caretOffset = offset || 0;
    const halfCaret = PANEL_DIMENSIONS.CARET_SIZE / 2;

    console.log('🎯 [PanelCaret] Rendering caret:', {
      position,
      offset: caretOffset,
    });

    switch (position) {
      case 'bottom': // Panel is above target, caret points down
        return css`
          bottom: -${halfCaret + 1}px; /* Extra pixel for border */
          left: ${caretOffset ? `${caretOffset}px` : '50%'};
          ${!caretOffset && `transform: translateX(-50%) rotate(45deg);`}
          ${caretOffset && `transform: rotate(45deg);`}
          border-top: none;
          border-left: none;
          box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(255, 255, 255, 0.95);
        `;
      case 'left': // Panel is to the right of target, caret points left
        return css`
          left: -${halfCaret + 1}px; /* Extra pixel for border */
          top: ${caretOffset ? `${caretOffset}px` : '50%'};
          ${!caretOffset && `transform: translateY(-50%) rotate(45deg);`}
          ${caretOffset && `transform: rotate(45deg);`}
          border-top: none;
          border-right: none;
          box-shadow: -1px 1px 4px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(255, 255, 255, 0.95);
        `;
      case 'top': // Panel is below target, caret points up
        return css`
          top: -${halfCaret + 1}px; /* Extra pixel for border */
          left: ${caretOffset ? `${caretOffset}px` : '50%'};
          ${!caretOffset && `transform: translateX(-50%) rotate(45deg);`}
          ${caretOffset && `transform: rotate(45deg);`}
          border-bottom: none;
          border-right: none;
          box-shadow: -1px -1px 4px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(255, 255, 255, 0.95);
        `;
      case 'right': // Panel is to the left of target, caret points right
        return css`
          right: -${halfCaret + 1}px; /* Extra pixel for border */
          top: ${caretOffset ? `${caretOffset}px` : '50%'};
          ${!caretOffset && `transform: translateY(-50%) rotate(45deg);`}
          ${caretOffset && `transform: rotate(45deg);`}
          border-bottom: none;
          border-left: none;
          box-shadow: 1px -1px 4px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(255, 255, 255, 0.95);
        `;
      default:
        console.warn('🎯 [PanelCaret] Unknown position:', position);
        return css`
          display: block !important;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(45deg);
          border: 2px solid red; /* Debug fallback */
        `;
    }
  }}

  /* Hide caret on mobile */
  @media (max-width: ${RESPONSIVE_BREAKPOINTS.MOBILE}px) {
    display: none;
  }

  /* Ensure caret is visible on different backgrounds */
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: inherit;
    border: inherit;
    border-radius: inherit;
    z-index: -1;
  }
`;

/**
 * Enhanced panel header with modern design
 */
export const PanelHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  position: relative;

  /* Subtle top border accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
    border-radius: ${PANEL_DIMENSIONS.BORDER_RADIUS}px
      ${PANEL_DIMENSIONS.BORDER_RADIUS}px 0 0;
  }

  @media (max-width: ${RESPONSIVE_BREAKPOINTS.MOBILE}px) {
    padding: 12px 16px;
    position: relative;

    /* Add drag handle for mobile */
    &::before {
      content: '';
      position: absolute;
      top: 8px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 4px;
      background: #d9d9d9;
      border-radius: 2px;
      transition: background-color 0.2s ease;
    }

    /* Interactive drag handle */
    &:active::before {
      background: #bfbfbf;
    }

    padding-top: 24px;

    /* Better touch targets */
    min-height: ${TOUCH_TARGETS.RECOMMENDED_SIZE}px;

    /* Swipe gesture area */
    touch-action: pan-y;
  }

  /* Tablet header optimizations */
  @media (min-width: ${RESPONSIVE_BREAKPOINTS.MOBILE +
    1}px) and (max-width: ${RESPONSIVE_BREAKPOINTS.TABLET}px) {
    padding: 14px 18px;
  }
`;

/**
 * Enhanced panel title with modern typography
 */
export const PanelTitle = styled.div`
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;

  .title-main {
    font-size: 18px;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: -0.02em;
    display: flex;
    align-items: center;
    gap: 10px;

    .anticon {
      color: #1890ff;
      font-size: 20px;
    }
  }

  .title-subtitle {
    font-size: 13px;
    font-weight: 500;
    color: #8c8c8c;
    margin-left: 30px; /* Align with icon */
  }
`;

/**
 * Modern close button with enhanced interactions
 */
export const CloseButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #e8e8e8;
  background: #ffffff;
  border-radius: 8px;
  cursor: pointer;
  color: #8c8c8c;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  /* Subtle shadow */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:hover {
    background: #f8f9fa;
    border-color: #d9d9d9;
    color: #595959;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  &:active {
    background: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Focus state for accessibility */
  &:focus-visible {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }

  /* Ensure proper touch target on mobile */
  @media (max-width: ${RESPONSIVE_BREAKPOINTS.MOBILE}px) {
    width: ${TOUCH_TARGETS.RECOMMENDED_SIZE}px;
    height: ${TOUCH_TARGETS.RECOMMENDED_SIZE}px;
  }
`;

/**
 * Panel content area with tabs
 */
export const PanelContent = styled.div`
  height: calc(100% - 73px); /* Account for header height */
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

/**
 * Enhanced tab navigation with modern design
 */
export const TabNavigation = styled.div`
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  padding: 0 4px;
  position: relative;

  /* Subtle inner shadow */
  box-shadow: inset 0 -1px 0 #f0f0f0;
`;

/**
 * Modern tab button with enhanced interactions
 */
export const TabButton = styled.button`
  flex: 1;
  padding: 14px 20px;
  border: none;
  background: transparent;
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-height: ${TOUCH_TARGETS.MIN_SIZE}px;
  border-radius: 8px 8px 0 0;
  margin: 4px 2px 0 2px;

  /* Modern typography */
  letter-spacing: 0.02em;
  text-transform: uppercase;
  font-size: 12px;

  &:hover {
    color: #595959;
    background: rgba(24, 144, 255, 0.04);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  ${({ active }) =>
    active &&
    css`
      color: #1890ff;
      background: #ffffff;
      font-weight: 700;
      box-shadow: 0 -2px 8px rgba(24, 144, 255, 0.1), 0 1px 0 #ffffff;

      /* Modern active indicator */
      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 32px;
        height: 3px;
        background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
        border-radius: 2px 2px 0 0;
      }

      /* Subtle glow effect */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(24, 144, 255, 0.02) 0%,
          rgba(64, 169, 255, 0.02) 100%
        );
        border-radius: inherit;
        pointer-events: none;
      }
    `}

  /* Ensure proper touch targets on mobile */
  @media (max-width: ${RESPONSIVE_BREAKPOINTS.MOBILE}px) {
    min-height: ${TOUCH_TARGETS.RECOMMENDED_SIZE}px;
    padding: 16px 18px;
    font-size: 13px;
  }
`;

/**
 * Tab content area with enhanced scrolling
 */
export const TabContent = styled(motion.div)`
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 24px; /* Optimized padding for larger panel */
  position: relative;

  /* Ensure proper height calculation */
  height: 0; /* This allows flex: 1 to work properly */
  min-height: 0; /* Prevents flex item from growing beyond container */

  @media (max-width: ${RESPONSIVE_BREAKPOINTS.MOBILE}px) {
    padding: 16px 20px; /* Optimized mobile padding */
    /* Better touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Smooth scrolling */
  scroll-behavior: smooth;

  /* Scroll padding for better UX */
  scroll-padding-top: 20px;
  scroll-padding-bottom: 20px;

  /* Optimized custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px; /* Thinner scrollbar */
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 3px;
    margin: 2px 0;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.12);
    border-radius: 3px;
    transition: all 0.2s ease;

    /* Minimum height for better usability */
    min-height: 30px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
  }

  &::-webkit-scrollbar-thumb:active {
    background: rgba(0, 0, 0, 0.3);
  }

  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.12) rgba(0, 0, 0, 0.03);

  /* Scroll fade indicators */
  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 8px; /* Account for scrollbar */
    height: 20px;
    pointer-events: none;
    z-index: 1;
    transition: opacity 0.3s ease;
  }

  /* Top fade indicator */
  &::before {
    top: 0;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 0.8) 50%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  /* Bottom fade indicator */
  &::after {
    bottom: 0;
    background: linear-gradient(
      to top,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 0.8) 50%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  /* Hide fade indicators when not scrollable */
  &.no-scroll {
    &::before,
    &::after {
      opacity: 0;
    }
  }

  /* Optimized form element spacing */
  > * {
    margin-bottom: 12px; /* Reduced spacing for more compact layout */

    &:last-child {
      margin-bottom: 0;
    }
  }

  /* Specific spacing for form items */
  .ant-form-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  /* Compact dividers */
  .ant-divider {
    margin: 16px 0 12px 0;
  }

  /* Compact typography */
  h5 {
    margin-bottom: 8px !important;
  }
`;

/**
 * Loading overlay for async operations
 */
export const LoadingOverlay = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${Z_INDEX_LEVELS.LOADING};
  backdrop-filter: blur(4px);
  border-radius: ${PANEL_DIMENSIONS.BORDER_RADIUS}px;

  /* Subtle gradient overlay */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    border-radius: inherit;
  }

  /* Loading content */
  > * {
    position: relative;
    z-index: 1;
  }
`;
