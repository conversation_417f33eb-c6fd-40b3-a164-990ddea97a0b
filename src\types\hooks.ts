/**
 * @fileoverview Hook-specific TypeScript type definitions
 *
 * This module contains type definitions for custom hooks used throughout
 * the Enterprise Form Builder application.
 *
 * @module HookTypes
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { 
  FormSchema, 
  ComponentData, 
  Position, 
  PanelConfig,
  ValidationResult,
  ThemeConfig,
  ComponentType
} from './index';

// ============================================================================
// Form Builder Hook Types
// ============================================================================

/**
 * useFormBuilder hook return type
 */
export interface UseFormBuilderReturn {
  /** Current form schema */
  schema: FormSchema;
  /** Form layout data */
  layout: ComponentData[];
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Dirty state (unsaved changes) */
  isDirty: boolean;
  /** Update entire schema */
  updateSchema: (schema: FormSchema) => void;
  /** Add new component */
  addComponent: (component: ComponentData, path?: string) => void;
  /** Remove component */
  removeComponent: (id: string) => void;
  /** Update component */
  updateComponent: (id: string, updates: Partial<ComponentData>) => void;
  /** Move component */
  moveComponent: (fromPath: string, toPath: string) => void;
  /** Duplicate component */
  duplicateComponent: (id: string) => void;
  /** Undo last action */
  undo: () => void;
  /** Redo last action */
  redo: () => void;
  /** Check if can undo */
  canUndo: boolean;
  /** Check if can redo */
  canRedo: boolean;
  /** Save form */
  save: () => Promise<void>;
  /** Load form */
  load: (id: string) => Promise<void>;
  /** Reset form */
  reset: () => void;
  /** Validate form */
  validate: () => Promise<ValidationResult[]>;
}

/**
 * useFormBuilder hook options
 */
export interface UseFormBuilderOptions {
  /** Initial schema */
  initialSchema?: FormSchema;
  /** Auto-save enabled */
  autoSave?: boolean;
  /** Auto-save interval in milliseconds */
  autoSaveInterval?: number;
  /** Maximum undo history */
  maxHistory?: number;
  /** Validation on change */
  validateOnChange?: boolean;
  /** API endpoints */
  api?: {
    save?: string;
    load?: string;
    validate?: string;
  };
}

// ============================================================================
// Properties Panel Hook Types
// ============================================================================

/**
 * usePropertiesPanel hook return type
 */
export interface UsePropertiesPanelReturn {
  /** Panel visibility state */
  isVisible: boolean;
  /** Panel position */
  position: Position;
  /** Currently selected component */
  selectedComponent: ComponentData | null;
  /** Panel configuration */
  config: PanelConfig;
  /** Show panel */
  showPanel: (componentId: string, position?: Position) => void;
  /** Hide panel */
  hidePanel: () => void;
  /** Update panel position */
  updatePosition: (position: Position) => void;
  /** Update component property */
  updateProperty: (property: string, value: any) => void;
  /** Update multiple properties */
  updateProperties: (updates: Record<string, any>) => void;
  /** Reset component properties */
  resetProperties: () => void;
  /** Toggle panel visibility */
  togglePanel: () => void;
}

/**
 * usePropertiesPanel hook options
 */
export interface UsePropertiesPanelOptions {
  /** Default panel configuration */
  defaultConfig?: Partial<PanelConfig>;
  /** Collision detection enabled */
  collisionDetection?: boolean;
  /** Auto-hide on outside click */
  autoHide?: boolean;
  /** Animation enabled */
  animated?: boolean;
  /** Debounce delay for property updates */
  debounceDelay?: number;
}

// ============================================================================
// Component Analytics Hook Types
// ============================================================================

/**
 * Analytics data structure
 */
export interface AnalyticsData {
  /** Total components */
  totalComponents: number;
  /** Active components */
  activeComponents: number;
  /** Most used components */
  mostUsedComponents: ComponentUsageData[];
  /** Category distribution */
  categoryDistribution: Record<string, number>;
  /** Usage trends */
  usageTrends: UsageTrendData[];
  /** Performance metrics */
  performanceMetrics: PerformanceMetrics;
  /** Error statistics */
  errorStats: ErrorStatistics;
}

/**
 * Component usage data
 */
export interface ComponentUsageData {
  /** Component ID */
  id: string;
  /** Component type */
  type: ComponentType;
  /** Component label */
  label: string;
  /** Usage count */
  usageCount: number;
  /** Usage percentage */
  usagePercentage: number;
  /** Last used timestamp */
  lastUsed: string;
  /** Average render time */
  averageRenderTime: number;
  /** Error rate */
  errorRate: number;
}

/**
 * Usage trend data
 */
export interface UsageTrendData {
  /** Date */
  date: string;
  /** Usage count */
  count: number;
  /** Component type */
  type?: ComponentType;
  /** Category */
  category?: string;
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  /** Average render time */
  averageRenderTime: number;
  /** Memory usage */
  memoryUsage: number;
  /** Bundle size */
  bundleSize: number;
  /** Load time */
  loadTime: number;
  /** Error rate */
  errorRate: number;
  /** User satisfaction score */
  satisfactionScore: number;
}

/**
 * Error statistics
 */
export interface ErrorStatistics {
  /** Total errors */
  totalErrors: number;
  /** Error rate */
  errorRate: number;
  /** Most common errors */
  commonErrors: ErrorData[];
  /** Error trends */
  errorTrends: ErrorTrendData[];
}

/**
 * Error data
 */
export interface ErrorData {
  /** Error type */
  type: string;
  /** Error message */
  message: string;
  /** Error count */
  count: number;
  /** Component ID */
  componentId?: string;
  /** Component type */
  componentType?: ComponentType;
  /** First occurrence */
  firstOccurrence: string;
  /** Last occurrence */
  lastOccurrence: string;
}

/**
 * Error trend data
 */
export interface ErrorTrendData {
  /** Date */
  date: string;
  /** Error count */
  count: number;
  /** Error type */
  type: string;
}

/**
 * useComponentAnalytics hook return type
 */
export interface UseComponentAnalyticsReturn {
  /** Analytics data */
  analytics: AnalyticsData;
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Refresh analytics */
  refresh: () => Promise<void>;
  /** Get component usage */
  getComponentUsage: (componentId: string) => ComponentUsageData | null;
  /** Get popular components */
  getPopularComponents: (limit?: number) => ComponentUsageData[];
  /** Get performance metrics */
  getPerformanceMetrics: () => PerformanceMetrics;
  /** Get error statistics */
  getErrorStatistics: () => ErrorStatistics;
  /** Track component usage */
  trackUsage: (componentId: string, action: string) => void;
  /** Track error */
  trackError: (error: ErrorData) => void;
}

/**
 * useComponentAnalytics hook options
 */
export interface UseComponentAnalyticsOptions {
  /** Analytics enabled */
  enabled?: boolean;
  /** Refresh interval in milliseconds */
  refreshInterval?: number;
  /** Time period for analytics */
  timePeriod?: 'day' | 'week' | 'month' | 'year';
  /** Include performance metrics */
  includePerformance?: boolean;
  /** Include error tracking */
  includeErrors?: boolean;
  /** API endpoint */
  apiEndpoint?: string;
}

// ============================================================================
// Device Preview Hook Types
// ============================================================================

/**
 * Device configuration
 */
export interface DeviceConfig {
  /** Device key */
  key: string;
  /** Device name */
  name: string;
  /** Device width */
  width: number;
  /** Device height */
  height: number;
  /** Device scale factor */
  scale: number;
  /** User agent string */
  userAgent: string;
  /** Device category */
  category: 'mobile' | 'tablet' | 'desktop';
  /** Device icon */
  icon?: string;
  /** Device color */
  color?: string;
}

/**
 * useDevicePreview hook return type
 */
export interface UseDevicePreviewReturn {
  /** Selected device */
  selectedDevice: string;
  /** Current zoom level */
  zoom: number;
  /** Device orientation */
  orientation: 'portrait' | 'landscape';
  /** Available devices */
  devices: DeviceConfig[];
  /** Set selected device */
  setSelectedDevice: (device: string) => void;
  /** Set zoom level */
  setZoom: (zoom: number) => void;
  /** Toggle orientation */
  toggleOrientation: () => void;
  /** Get device configuration */
  getDeviceConfig: (device: string) => DeviceConfig | null;
  /** Get current device configuration */
  currentDeviceConfig: DeviceConfig;
  /** Zoom in */
  zoomIn: () => void;
  /** Zoom out */
  zoomOut: () => void;
  /** Reset zoom */
  resetZoom: () => void;
  /** Fit to screen */
  fitToScreen: () => void;
}

/**
 * useDevicePreview hook options
 */
export interface UseDevicePreviewOptions {
  /** Default device */
  defaultDevice?: string;
  /** Default zoom level */
  defaultZoom?: number;
  /** Default orientation */
  defaultOrientation?: 'portrait' | 'landscape';
  /** Custom devices */
  customDevices?: DeviceConfig[];
  /** Zoom limits */
  zoomLimits?: {
    min: number;
    max: number;
    step: number;
  };
}

// ============================================================================
// Theme Hook Types
// ============================================================================

/**
 * useTheme hook return type
 */
export interface UseThemeReturn {
  /** Current theme */
  theme: ThemeConfig;
  /** Theme mode */
  mode: 'light' | 'dark' | 'auto';
  /** Set theme */
  setTheme: (theme: Partial<ThemeConfig>) => void;
  /** Set theme mode */
  setMode: (mode: 'light' | 'dark' | 'auto') => void;
  /** Toggle theme mode */
  toggleMode: () => void;
  /** Reset theme */
  resetTheme: () => void;
  /** Get CSS variables */
  getCSSVariables: () => Record<string, string>;
  /** Apply theme */
  applyTheme: () => void;
}

/**
 * useTheme hook options
 */
export interface UseThemeOptions {
  /** Default theme */
  defaultTheme?: ThemeConfig;
  /** Default mode */
  defaultMode?: 'light' | 'dark' | 'auto';
  /** Persist theme */
  persist?: boolean;
  /** Storage key */
  storageKey?: string;
  /** Auto-detect system theme */
  autoDetect?: boolean;
}

// ============================================================================
// Validation Hook Types
// ============================================================================

/**
 * useValidation hook return type
 */
export interface UseValidationReturn {
  /** Validation errors */
  errors: Record<string, string[]>;
  /** Validation state */
  isValid: boolean;
  /** Validating state */
  isValidating: boolean;
  /** Validate field */
  validateField: (field: string, value: any) => Promise<string[]>;
  /** Validate all fields */
  validateAll: () => Promise<boolean>;
  /** Clear errors */
  clearErrors: (field?: string) => void;
  /** Set error */
  setError: (field: string, errors: string[]) => void;
  /** Get field errors */
  getFieldErrors: (field: string) => string[];
  /** Check if field has errors */
  hasFieldErrors: (field: string) => boolean;
}

/**
 * useValidation hook options
 */
export interface UseValidationOptions {
  /** Validation rules */
  rules?: Record<string, any>;
  /** Validate on change */
  validateOnChange?: boolean;
  /** Validate on blur */
  validateOnBlur?: boolean;
  /** Debounce delay */
  debounceDelay?: number;
  /** Custom validators */
  customValidators?: Record<string, (value: any) => boolean | string | Promise<boolean | string>>;
}
