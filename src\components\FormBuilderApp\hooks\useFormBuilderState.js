/**
 * @fileoverview Custom hook for managing form builder state
 * 
 * This hook encapsulates all core state management for the form builder,
 * including layout, components, rendering keys, and debug logging.
 * 
 * @module useFormBuilderState
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import { useState, useMemo, useEffect, useRef } from 'react';
import { complexFormSchema } from '../../../data';

/**
 * Custom hook for managing form builder state
 * 
 * Provides centralized state management for the form builder application,
 * including layout structure, component registry, rendering optimization,
 * and comprehensive debug logging.
 * 
 * @returns {Object} State management object
 * @returns {Array} returns.layout - Current form layout structure
 * @returns {Function} returns.setLayout - Layout state setter
 * @returns {Object} returns.components - Component registry object
 * @returns {Function} returns.setComponents - Components state setter
 * @returns {number} returns.forceRenderKey - Key for forcing re-renders
 * @returns {Function} returns.setForceRenderKey - Force render key setter
 * @returns {number} returns.componentUpdateCounter - Counter for component updates
 * @returns {Function} returns.setComponentUpdateCounter - Component update counter setter
 * @returns {Object} returns.aiGenerationAttemptRef - Ref for tracking AI generation attempts
 * @returns {Object} returns.currentFormSchema - Memoized current form schema
 * 
 * @example
 * ```jsx
 * const {
 *   layout,
 *   setLayout,
 *   components,
 *   setComponents,
 *   forceRenderKey,
 *   setForceRenderKey,
 *   currentFormSchema
 * } = useFormBuilderState();
 * 
 * // Update layout
 * setLayout(newLayout);
 * 
 * // Access current schema
 * console.log(currentFormSchema);
 * ```
 */
export const useFormBuilderState = () => {
  // Memoized initial data to prevent unnecessary re-computations
  const memoizedInitialData = useMemo(
    () => ({
      layout: complexFormSchema.layout,
      components: complexFormSchema.components,
    }),
    [],
  );

  // Core state variables
  const [layout, setLayout] = useState(memoizedInitialData.layout);
  const [components, setComponents] = useState(memoizedInitialData.components);

  // Rendering optimization state
  const [forceRenderKey, setForceRenderKey] = useState(0);
  const [componentUpdateCounter, setComponentUpdateCounter] = useState(0);

  // AI generation tracking
  const aiGenerationAttemptRef = useRef(0);

  // Debug state changes with detailed logging
  useEffect(() => {
    console.log('🔍 [State] Layout updated:', layout);
    console.log('🔍 [State] Layout length:', layout?.length);
    console.log(
      '🔍 [State] Layout structure:',
      JSON.stringify(layout, null, 2),
    );
  }, [layout]);

  useEffect(() => {
    console.log('🔍 [State] Components updated:', components);
    console.log('🔍 [State] Components keys:', Object.keys(components || {}));
    console.log(
      '🔍 [State] Components structure:',
      JSON.stringify(components, null, 2),
    );
  }, [components]);

  // Track when both layout and components change together
  useEffect(() => {
    console.log('🔍 [State] Combined state change detected:');
    console.log('🔍 [State] Layout items:', layout?.length || 0);
    console.log(
      '🔍 [State] Component count:',
      Object.keys(components || {}).length,
    );
    console.log('🔍 [State] This should trigger re-render in both tabs');
    console.log(
      '🔍 [State] Current AI generation attempt:',
      aiGenerationAttemptRef.current,
    );

    // Check if this is a significant change (not just initial load)
    if (aiGenerationAttemptRef.current > 0) {
      console.log('🔍 [State] This appears to be an AI-generated form update!');
    }
  }, [layout, components]);

  // Memoized form schema generation
  const currentFormSchema = useMemo(
    () => ({
      layout,
      components,
      metadata: {
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        title: 'Generated Form',
      },
    }),
    [layout, components],
  );

  return {
    // Core state
    layout,
    setLayout,
    components,
    setComponents,
    
    // Rendering optimization
    forceRenderKey,
    setForceRenderKey,
    componentUpdateCounter,
    setComponentUpdateCounter,
    
    // AI generation tracking
    aiGenerationAttemptRef,
    
    // Computed values
    currentFormSchema,
  };
};
