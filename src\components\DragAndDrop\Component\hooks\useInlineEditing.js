/**
 * useInlineEditing Hook
 * 
 * Custom hook for managing inline text editing functionality in components.
 * Handles editing state, input validation, and property updates with support
 * for nested properties and complex data structures.
 * 
 * Features:
 * - Inline text editing with click-to-edit functionality
 * - Support for nested property paths (e.g., 'cardProps.title', 'options.0.label')
 * - Keyboard shortcuts (Enter to save, Escape to cancel)
 * - Auto-save on blur with validation
 * - Undo functionality for cancelled edits
 * - Property change detection to prevent unnecessary updates
 * 
 * @module useInlineEditing
 */

import { useState, useCallback } from 'react';
import { getNestedPropertyValue, buildNestedUpdate } from '../utils/componentUtils';

/**
 * Custom hook for inline editing functionality
 * 
 * @param {Object} options - Configuration options
 * @param {Object} options.component - Component data object
 * @param {string} options.componentId - Component ID for updates
 * @param {Function} options.onUpdateComponent - Callback for component updates
 * @returns {Object} Inline editing state and handlers
 * 
 * @example
 * const {
 *   isEditing,
 *   renderEditableText,
 *   handleLabelClick,
 *   handleEditingSave,
 *   handleEditingCancel
 * } = useInlineEditing({
 *   component: componentData,
 *   componentId: 'comp-123',
 *   onUpdateComponent: handleUpdate
 * });
 */
export const useInlineEditing = ({ component, componentId, onUpdateComponent }) => {
  // Editing state management
  const [isEditing, setIsEditing] = useState(false);
  const [editingValue, setEditingValue] = useState('');
  const [originalValue, setOriginalValue] = useState('');
  const [editingProperty, setEditingProperty] = useState(null);

  /**
   * Initiates inline editing for a property
   * 
   * @param {Event} e - Click event
   * @param {string} currentValue - Current property value
   * @param {string} propertyName - Property path to edit (supports dot notation)
   */
  const handleLabelClick = useCallback(
    (e, currentValue, propertyName = 'label') => {
      e.stopPropagation();
      
      if (!isEditing) {
        const actualValue = currentValue !== undefined 
          ? currentValue 
          : getNestedPropertyValue(component, propertyName);
          
        setOriginalValue(actualValue || '');
        setEditingValue(actualValue || '');
        setEditingProperty(propertyName);
        setIsEditing(true);
      }
    },
    [isEditing, component],
  );

  /**
   * Saves the edited value and updates the component
   * 
   * @param {string} propertyName - Property path being edited
   */
  const handleEditingSave = useCallback(
    (propertyName = 'label') => {
      if (onUpdateComponent && editingValue !== originalValue) {
        // Handle nested properties like 'cardProps.title', 'options.0.label', etc.
        if (propertyName.includes('.')) {
          const parts = propertyName.split('.');
          const updateData = buildNestedUpdate(parts, editingValue, component);
          onUpdateComponent(componentId, updateData);
        } else {
          // Simple property update
          const updateData = {
            [propertyName]: editingValue,
          };
          onUpdateComponent(componentId, updateData);
        }
      }
      
      // Reset editing state
      setIsEditing(false);
      setEditingValue('');
      setOriginalValue('');
      setEditingProperty(null);
    },
    [onUpdateComponent, componentId, editingValue, originalValue, component],
  );

  /**
   * Cancels editing and reverts to original value
   */
  const handleEditingCancel = useCallback(() => {
    setIsEditing(false);
    setEditingValue(originalValue);
    setOriginalValue('');
    setEditingProperty(null);
  }, [originalValue]);

  /**
   * Handles keyboard shortcuts during editing
   * 
   * @param {KeyboardEvent} e - Keyboard event
   * @param {string} propertyName - Property being edited
   */
  const handleEditingKeyDown = useCallback(
    (e, propertyName = 'label') => {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleEditingSave(propertyName);
      } else if (e.key === 'Escape') {
        e.preventDefault();
        handleEditingCancel();
      }
    },
    [handleEditingSave, handleEditingCancel],
  );

  /**
   * Handles blur event to auto-save changes
   * 
   * @param {string} propertyName - Property being edited
   */
  const handleEditingBlur = useCallback(
    (propertyName = 'label') => {
      handleEditingSave(propertyName);
    },
    [handleEditingSave],
  );

  /**
   * Renders editable text with inline editing functionality
   * 
   * @param {string} text - Text to display/edit
   * @param {string} propertyName - Property path (supports dot notation)
   * @param {string} placeholder - Placeholder text when empty
   * @returns {JSX.Element} Editable text component
   */
  const renderEditableText = useCallback(
    (text, propertyName = 'label', placeholder = 'Click to edit') => {
      // If text is not provided, try to get it from the component using propertyName
      const actualText = text !== undefined 
        ? text 
        : getNestedPropertyValue(component, propertyName);
      const displayText = actualText || placeholder;
      const isPlaceholder = !actualText;
      const isCurrentlyEditing = isEditing && editingProperty === propertyName;

      if (isCurrentlyEditing) {
        return (
          <input
            className='editing-input'
            value={editingValue}
            onChange={(e) => setEditingValue(e.target.value)}
            onKeyDown={(e) => handleEditingKeyDown(e, propertyName)}
            onBlur={() => handleEditingBlur(propertyName)}
            autoFocus
            style={{
              fontSize: 'inherit',
              fontFamily: 'inherit',
              fontWeight: 'inherit',
              color: 'inherit',
            }}
          />
        );
      }

      return (
        <span
          className={`editable-text ${isPlaceholder ? 'placeholder-text' : ''}`}
          onClick={(e) => handleLabelClick(e, actualText, propertyName)}
          title='Click to edit'
        >
          {displayText}
        </span>
      );
    },
    [
      isEditing,
      editingProperty,
      editingValue,
      handleEditingKeyDown,
      handleEditingBlur,
      handleLabelClick,
      component,
    ],
  );

  /**
   * Checks if a specific property is currently being edited
   * 
   * @param {string} propertyName - Property path to check
   * @returns {boolean} True if property is being edited
   */
  const isEditingProperty = useCallback(
    (propertyName) => {
      return isEditing && editingProperty === propertyName;
    },
    [isEditing, editingProperty],
  );

  /**
   * Gets the current editing value for a property
   * 
   * @param {string} propertyName - Property path
   * @returns {string} Current editing value or empty string
   */
  const getEditingValue = useCallback(
    (propertyName) => {
      return isEditingProperty(propertyName) ? editingValue : '';
    },
    [isEditingProperty, editingValue],
  );

  /**
   * Programmatically starts editing a property
   * 
   * @param {string} propertyName - Property path to edit
   * @param {string} initialValue - Initial value for editing
   */
  const startEditing = useCallback(
    (propertyName, initialValue = '') => {
      if (!isEditing) {
        const actualValue = initialValue || getNestedPropertyValue(component, propertyName) || '';
        setOriginalValue(actualValue);
        setEditingValue(actualValue);
        setEditingProperty(propertyName);
        setIsEditing(true);
      }
    },
    [isEditing, component],
  );

  /**
   * Checks if any property is currently being edited
   * 
   * @returns {boolean} True if any editing is in progress
   */
  const hasActiveEditing = useCallback(() => {
    return isEditing;
  }, [isEditing]);

  return {
    // Editing state
    isEditing,
    editingValue,
    originalValue,
    editingProperty,
    
    // Core editing functions
    handleLabelClick,
    handleEditingSave,
    handleEditingCancel,
    handleEditingKeyDown,
    handleEditingBlur,
    
    // Rendering function
    renderEditableText,
    
    // Utility functions
    isEditingProperty,
    getEditingValue,
    startEditing,
    hasActiveEditing,
    
    // State setters for advanced use cases
    setEditingValue,
    setIsEditing,
  };
};
