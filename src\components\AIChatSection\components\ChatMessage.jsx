/**
 * ChatMessage Component
 *
 * Renders individual chat messages with proper styling and animations.
 * Handles both user and AI messages with different visual treatments.
 *
 * Features:
 * - Dynamic styling based on message sender (user vs AI)
 * - Smooth animations with Framer Motion
 * - Avatar display with appropriate icons
 * - Message metadata and action buttons for AI responses
 * - Accessibility-compliant design
 *
 * @param {Object} props - Component props
 * @param {Object} props.message - Message object to render
 * @param {number} props.index - Message index for animation delays
 * @param {Function} props.onModifyForm - Callback for modifying form
 */

import React, { memo } from 'react';
import {
  Avatar,
  Typography,
  Tag,
  Button,
  Tooltip,
  message as antMessage,
} from 'antd';
import {
  UserOutlined,
  RobotOutlined,
  CopyOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { MessageBubble, MessageContent } from '../styles/StyledComponents';
import { ActionButtons, ComplexityTag } from '../styles/StatusComponents';
import {
  messageVariants,
  messageContentVariants,
} from '../constants/animations';

const { Text } = Typography;

/**
 * ChatMessage component for rendering individual messages
 *
 * @param {Object} props - Component props
 * @returns {JSX.Element} Rendered message component
 */
const ChatMessage = memo(({ message, index, onModifyForm }) => {
  /**
   * Handles copying the JSON schema to clipboard
   */
  const handleCopySchema = () => {
    if (message.schema) {
      navigator.clipboard.writeText(JSON.stringify(message.schema, null, 2));
      antMessage.success('Schema copied to clipboard!');

      // Analytics tracking for schema copy action
      console.log('📋 [ChatMessage] Schema copied for message:', message.id);
    }
  };

  /**
   * Handles the modify form action
   */
  const handleModifyForm = () => {
    if (onModifyForm) {
      onModifyForm(`Modify the previous form: `);
      console.log(
        '✏️ [ChatMessage] Modify form requested for message:',
        message.id,
      );
    }
  };

  /**
   * Gets the appropriate avatar icon based on message sender
   */
  const getAvatarIcon = () => {
    return message.isUser ? <UserOutlined /> : <RobotOutlined />;
  };

  /**
   * Gets the avatar background color based on message sender
   */
  const getAvatarColor = () => {
    return message.isUser ? '#6b7280' : '#3b82f6';
  };

  /**
   * Gets the avatar box shadow based on message sender
   */
  const getAvatarShadow = () => {
    return message.isUser
      ? '0 2px 6px rgba(107, 114, 128, 0.15)'
      : '0 2px 6px rgba(59, 130, 246, 0.15)';
  };

  return (
    <MessageBubble
      key={message.id}
      isUser={message.isUser}
      variants={messageVariants}
      initial='hidden'
      animate='visible'
      exit='exit'
      layout
      layoutId={`message-${message.id}`}
      style={{ animationDelay: `${index * 0.1}s` }}
    >
      {/* Message Avatar */}
      <Avatar
        size={28}
        icon={getAvatarIcon()}
        style={{
          backgroundColor: getAvatarColor(),
          flexShrink: 0,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          boxShadow: getAvatarShadow(),
        }}
      />

      {/* Message Content Container */}
      <div style={{ flex: 1 }}>
        {/* Main Message Content */}
        <MessageContent
          isUser={message.isUser}
          variants={messageContentVariants}
          initial='initial'
          animate='animate'
        >
          {message.text}
        </MessageContent>

        {/* Additional Content for Form Generation Messages */}
        {message.type === 'form-generated' && (
          <div style={{ marginTop: 8 }}>
            {/* Complexity Tag */}
            <ComplexityTag>{message.complexity} Form</ComplexityTag>

            {/* Suggested Components Display */}
            {message.suggestedComponents &&
              message.suggestedComponents.length > 0 && (
                <div style={{ marginTop: 4 }}>
                  <Text type='secondary' style={{ fontSize: 11 }}>
                    Components: {message.suggestedComponents.join(', ')}
                  </Text>
                </div>
              )}

            {/* Action Buttons */}
            <ActionButtons>
              {/* Auto-Applied Status Tag */}
              {message.autoApplied && (
                <Tag color='green' size='small' style={{ fontSize: 10 }}>
                  ✅ Auto-Applied
                </Tag>
              )}

              {/* Copy JSON Schema Button */}
              <Tooltip title='Copy JSON Schema'>
                <Button
                  size='small'
                  icon={<CopyOutlined />}
                  onClick={handleCopySchema}
                >
                  Copy JSON
                </Button>
              </Tooltip>

              {/* Modify Form Button */}
              <Tooltip title='Regenerate with modifications'>
                <Button
                  size='small'
                  icon={<ReloadOutlined />}
                  onClick={handleModifyForm}
                >
                  Modify
                </Button>
              </Tooltip>
            </ActionButtons>
          </div>
        )}

        {/* Error Message Styling */}
        {message.type === 'error' && (
          <div style={{ marginTop: 4 }}>
            <Text type='danger' style={{ fontSize: 11 }}>
              Please try again or check your connection
            </Text>
          </div>
        )}

        {/* Welcome Message Styling */}
        {message.type === 'welcome' && (
          <div style={{ marginTop: 4 }}>
            <Text type='secondary' style={{ fontSize: 11 }}>
              Powered by Groq's Llama 3.3 70B
            </Text>
          </div>
        )}
      </div>
    </MessageBubble>
  );
});

// Set display name for debugging
ChatMessage.displayName = 'ChatMessage';

export default ChatMessage;
