/**
 * @fileoverview Properties Panel Context for global state management
 *
 * This context provides global state management for the properties panel,
 * allowing any component in the form builder to trigger the panel.
 *
 * @module PropertiesPanelContext
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useRef,
  useEffect,
} from 'react';

/**
 * Properties Panel Context
 */
const PropertiesPanelContext = createContext(null);

/**
 * Properties Panel Provider component
 *
 * Provides global state management for the properties panel including
 * visibility, target element, and component data management.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Function} props.onUpdateComponent - Component update callback
 * @param {Object} props.formSchema - Current form schema
 *
 * @returns {JSX.Element} Context provider
 */
export const PropertiesPanelProvider = ({
  children,
  onUpdateComponent,
  formSchema,
}) => {
  // Panel state
  const [isVisible, setIsVisible] = useState(false);
  const [targetElement, setTargetElement] = useState(null);
  const [componentData, setComponentData] = useState(null);
  const [componentId, setComponentId] = useState(null);

  // Ref to track the current target for cleanup
  const currentTargetRef = useRef(null);

  // Sync component data when external form schema changes
  useEffect(() => {
    if (componentId && formSchema?.components?.[componentId]) {
      const externalComponentData = formSchema.components[componentId];

      // Only update if the data has actually changed to avoid infinite loops
      if (
        JSON.stringify(componentData) !== JSON.stringify(externalComponentData)
      ) {
        console.log(
          '🔄 [PropertiesPanelContext] Syncing component data from external update:',
          {
            componentId,
            oldData: componentData,
            newData: externalComponentData,
          },
        );

        setComponentData(externalComponentData);
      }
    }
  }, [formSchema?.components, componentId, componentData]);

  /**
   * Validates a property update before applying it
   *
   * @param {string} propertyPath - Property path being updated
   * @param {*} value - New value
   * @param {Object} componentData - Component data
   * @returns {boolean} Whether the update is valid
   */
  const validatePropertyUpdate = useCallback(
    (propertyPath, value, componentData) => {
      try {
        // Basic validation rules
        const validationRules = {
          label: (val) => typeof val === 'string',
          name: (val) =>
            typeof val === 'string' && /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(val),
          placeholder: (val) => typeof val === 'string',
          required: (val) => typeof val === 'boolean',
          disabled: (val) => typeof val === 'boolean',
          size: (val) => ['small', 'middle', 'large'].includes(val),
          'validation.minLength': (val) => typeof val === 'number' && val >= 0,
          'validation.maxLength': (val) => typeof val === 'number' && val >= 0,
          rows: (val) => typeof val === 'number' && val >= 1 && val <= 20,
          maxLength: (val) => typeof val === 'number' && val >= 0,
        };

        // Check if there's a specific validation rule
        const validator = validationRules[propertyPath];
        if (validator) {
          return validator(value);
        }

        // Default validation - allow most values but reject null/undefined for required fields
        if (value === null || value === undefined) {
          const requiredFields = ['type', 'id'];
          return !requiredFields.includes(propertyPath);
        }

        return true;
      } catch (error) {
        console.warn('Validation error:', error);
        return false;
      }
    },
    [],
  );

  /**
   * Opens the properties panel for a specific component
   *
   * @param {HTMLElement} element - DOM element that triggered the panel
   * @param {Object} data - Component data
   * @param {string} id - Component ID
   */
  const openPanel = useCallback(
    (element, data, id) => {
      console.log('🔧 [PropertiesPanelContext] Opening properties panel:', {
        componentId: id,
        componentType: data?.type,
        element: element?.tagName,
      });

      // Close any existing panel first
      if (isVisible) {
        closePanel();
      }

      // Set new panel state
      setTargetElement(element);
      setComponentData(data);
      setComponentId(id);
      setIsVisible(true);

      // Track current target for cleanup
      currentTargetRef.current = element;
    },
    [isVisible],
  );

  /**
   * Closes the properties panel with proper cleanup
   */
  const closePanel = useCallback(() => {
    console.log('🔒 [PropertiesPanelContext] Closing properties panel');

    // Clear any pending timeouts or intervals
    if (window.propertiesPanelTimeouts) {
      window.propertiesPanelTimeouts.forEach(clearTimeout);
      window.propertiesPanelTimeouts = [];
    }

    setIsVisible(false);
    setTargetElement(null);
    setComponentData(null);
    setComponentId(null);

    // Clear target reference
    currentTargetRef.current = null;
  }, []);

  /**
   * Updates component data in the panel
   *
   * @param {Object} newData - Updated component data
   */
  const updateComponentData = useCallback((newData) => {
    setComponentData(newData);
  }, []);

  /**
   * Handles component property updates with optimistic updates and rollback
   *
   * @param {string} propertyPath - Property path to update
   * @param {*} value - New property value
   * @param {Object} options - Update options
   */
  const handlePropertyUpdate = useCallback(
    async (propertyPath, value, options = {}) => {
      if (!componentId || !componentData) {
        console.warn('Cannot update property: no component selected');
        return;
      }

      const { immediate = false, validate = true, optimistic = true } = options;
      const originalData = componentData;

      try {
        // Create updated component data
        const updatedComponent = { ...componentData };

        // Handle nested property paths
        const pathParts = propertyPath.split('.');
        let current = updatedComponent;

        for (let i = 0; i < pathParts.length - 1; i++) {
          const part = pathParts[i];
          if (!current[part]) {
            current[part] = {};
          }
          current = current[part];
        }

        // Set the final value
        const finalKey = pathParts[pathParts.length - 1];
        const oldValue = current[finalKey];
        current[finalKey] = value;

        // Optimistic update - update local state immediately
        if (optimistic) {
          setComponentData(updatedComponent);
        }

        // Validate the update if requested
        if (
          validate &&
          !validatePropertyUpdate(propertyPath, value, updatedComponent)
        ) {
          throw new Error(
            `Invalid value for property ${propertyPath}: ${value}`,
          );
        }

        // Call external update handler
        if (onUpdateComponent) {
          await onUpdateComponent(componentId, updatedComponent);
        }

        console.log('✅ [PropertiesPanelContext] Property updated:', {
          componentId,
          propertyPath,
          oldValue,
          newValue: value,
          immediate,
          optimistic,
        });
      } catch (error) {
        console.error(
          '❌ [PropertiesPanelContext] Property update failed:',
          error,
        );

        // Rollback optimistic update on error
        if (optimistic) {
          setComponentData(originalData);
        }

        throw error;
      }
    },
    [componentId, componentData, onUpdateComponent],
  );

  /**
   * Checks if a specific component is currently being edited
   *
   * @param {string} id - Component ID to check
   * @returns {boolean} Whether the component is being edited
   */
  const isComponentBeingEdited = useCallback(
    (id) => {
      return isVisible && componentId === id;
    },
    [isVisible, componentId],
  );

  // Context value
  const contextValue = {
    // State
    isVisible,
    targetElement,
    componentData,
    componentId,
    formSchema,

    // Actions
    openPanel,
    closePanel,
    updateComponentData,
    handlePropertyUpdate,

    // Utilities
    isComponentBeingEdited,
  };

  return (
    <PropertiesPanelContext.Provider value={contextValue}>
      {children}
    </PropertiesPanelContext.Provider>
  );
};

/**
 * Custom hook to use the Properties Panel context
 *
 * @returns {Object} Properties panel context value
 * @throws {Error} If used outside of PropertiesPanelProvider
 */
export const usePropertiesPanelContext = () => {
  const context = useContext(PropertiesPanelContext);

  if (!context) {
    throw new Error(
      'usePropertiesPanelContext must be used within a PropertiesPanelProvider',
    );
  }

  return context;
};

/**
 * HOC to provide properties panel context to a component
 *
 * @param {React.Component} Component - Component to wrap
 * @returns {React.Component} Wrapped component with context
 */
export const withPropertiesPanel = (Component) => {
  const WrappedComponent = (props) => {
    const propertiesPanelContext = usePropertiesPanelContext();

    return <Component {...props} propertiesPanel={propertiesPanelContext} />;
  };

  WrappedComponent.displayName = `withPropertiesPanel(${
    Component.displayName || Component.name
  })`;

  return WrappedComponent;
};
