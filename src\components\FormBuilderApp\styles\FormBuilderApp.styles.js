/**
 * @fileoverview Styled components for FormBuilderApp
 * 
 * This module re-exports styled components from the main styles file
 * and provides FormBuilderApp-specific styled components.
 * 
 * @module FormBuilderApp.styles
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

// Re-export all styled components from main styles
export * from '../../../styles';

/**
 * Note: The FormBuilderApp component uses styled components from the main
 * styles file (src/styles/index.js). This file serves as a reference point
 * for FormBuilderApp-specific styling and can be extended with additional
 * styled components as needed.
 * 
 * Main styled components used by FormBuilderApp:
 * - Body: Main container for the application
 * - BuilderContainer: Container for the builder tab
 * - AIChatSectionContainer: Container for AI chat section
 * - ComponentsSectionContainer: Container for components sidebar
 * - FormBuilderCanvasContainer: Container for the form builder canvas
 * - PageContainer: Container for the page layout
 * - Page: Main page content area
 * - PreviewContainer: Container for the preview tab
 * - PreviewHeader: Header section for preview tab
 * - PreviewContent: Content area for preview tab
 * 
 * These components are defined in src/styles/index.js and provide
 * consistent styling across the entire form builder application.
 * 
 * Usage:
 * ```jsx
 * import * as S from './styles/FormBuilderApp.styles';
 * 
 * return (
 *   <S.Body>
 *     <S.BuilderContainer>
 *       // Builder content
 *     </S.BuilderContainer>
 *   </S.Body>
 * );
 * ```
 */
