/**
 * @fileoverview Utility functions for Properties Panel
 *
 * This module provides utility functions for positioning calculations,
 * collision detection, property validation, and component schema management.
 *
 * @module propertiesPanelUtils
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import {
  POSITIONING_PRIORITY,
  PANEL_DIMENSIONS,
  PANEL_DEFAULTS,
  PROPERTY_CATEGORIES,
} from '../constants/propertiesPanelConstants';

/**
 * Calculates optimal position for the properties panel
 *
 * @param {HTMLElement} targetElement - Element that triggered the panel
 * @param {Object} panelDimensions - Panel width and height
 * @param {Object} viewport - Viewport dimensions (optional, will use window if not provided)
 * @returns {Object} Position calculation result
 */
export const calculateOptimalPosition = (
  targetElement,
  panelDimensions,
  viewport,
) => {
  if (!targetElement) {
    return { position: 'center', style: {} };
  }

  // Use current viewport if not provided
  const currentViewport = viewport || {
    width: window.innerWidth,
    height: window.innerHeight,
  };

  const targetRect = targetElement.getBoundingClientRect();
  const { width: panelWidth, height: panelHeight } = panelDimensions;
  const offset = PANEL_DIMENSIONS.OFFSET;
  const padding = PANEL_DEFAULTS.COLLISION_PADDING;

  // Calculate available space in each direction
  const spaces = {
    top: targetRect.top - padding,
    right: currentViewport.width - targetRect.right - padding,
    bottom: currentViewport.height - targetRect.bottom - padding,
    left: targetRect.left - padding,
  };

  // Calculate scores for each position (higher is better)
  const positionScores = {};

  for (const position of POSITIONING_PRIORITY) {
    const result = calculatePositionStyle(
      position,
      targetRect,
      panelDimensions,
      spaces,
      offset,
      currentViewport,
      padding,
    );
    if (result.canFit) {
      // Score based on available space and position preference
      const spaceScore = getSpaceScore(position, spaces, panelDimensions);
      const preferenceScore =
        POSITIONING_PRIORITY.indexOf(position) === 0 ? 10 : 5; // Prefer 'top'
      positionScores[position] = {
        ...result,
        score: spaceScore + preferenceScore,
      };
    }
  }

  // Choose the best position based on score
  const bestPosition = Object.keys(positionScores).reduce((best, current) => {
    return positionScores[current].score > positionScores[best]?.score
      ? current
      : best;
  }, null);

  if (bestPosition) {
    return {
      position: bestPosition,
      style: positionScores[bestPosition].style,
    };
  }

  // Fallback to center if no position works
  return {
    position: 'center',
    style: {
      top: Math.max(padding, (currentViewport.height - panelHeight) / 2),
      left: Math.max(padding, (currentViewport.width - panelWidth) / 2),
    },
  };
};

/**
 * Calculates position style for a specific position
 *
 * @param {string} position - Position to calculate (top/right/bottom/left)
 * @param {DOMRect} targetRect - Target element bounds
 * @param {Object} panelDimensions - Panel dimensions
 * @param {Object} spaces - Available spaces
 * @param {number} offset - Offset from target element
 * @param {Object} viewport - Viewport dimensions
 * @param {number} padding - Collision padding
 * @returns {Object} Position calculation result
 */
const calculatePositionStyle = (
  position,
  targetRect,
  panelDimensions,
  spaces,
  offset,
  viewport,
  padding,
) => {
  const { width: panelWidth, height: panelHeight } = panelDimensions;

  switch (position) {
    case 'top':
      if (spaces.top >= panelHeight) {
        return {
          canFit: true,
          style: {
            top: targetRect.top - panelHeight - offset,
            left: Math.max(
              padding,
              Math.min(
                targetRect.left + targetRect.width / 2 - panelWidth / 2,
                viewport.width - panelWidth - padding,
              ),
            ),
          },
        };
      }
      break;

    case 'right':
      if (spaces.right >= panelWidth) {
        return {
          canFit: true,
          style: {
            top: Math.max(
              padding,
              Math.min(
                targetRect.top + targetRect.height / 2 - panelHeight / 2,
                viewport.height - panelHeight - padding,
              ),
            ),
            left: targetRect.right + offset,
          },
        };
      }
      break;

    case 'bottom':
      if (spaces.bottom >= panelHeight) {
        return {
          canFit: true,
          style: {
            top: targetRect.bottom + offset,
            left: Math.max(
              padding,
              Math.min(
                targetRect.left + targetRect.width / 2 - panelWidth / 2,
                viewport.width - panelWidth - padding,
              ),
            ),
          },
        };
      }
      break;

    case 'left':
      if (spaces.left >= panelWidth) {
        return {
          canFit: true,
          style: {
            top: Math.max(
              padding,
              Math.min(
                targetRect.top + targetRect.height / 2 - panelHeight / 2,
                viewport.height - panelHeight - padding,
              ),
            ),
            left: targetRect.left - panelWidth - offset,
          },
        };
      }
      break;
  }

  return { canFit: false, style: {} };
};

/**
 * Calculates a score for a position based on available space
 *
 * @param {string} position - Position to score
 * @param {Object} spaces - Available spaces in each direction
 * @param {Object} panelDimensions - Panel dimensions
 * @returns {number} Space score (higher is better)
 */
const getSpaceScore = (position, spaces, panelDimensions) => {
  const { width: panelWidth, height: panelHeight } = panelDimensions;

  switch (position) {
    case 'top':
    case 'bottom':
      // For vertical positions, score based on vertical space and horizontal centering ability
      const verticalSpace = position === 'top' ? spaces.top : spaces.bottom;
      const horizontalSpace = Math.min(spaces.left, spaces.right);
      return (
        (verticalSpace / panelHeight) * 10 + (horizontalSpace / panelWidth) * 5
      );

    case 'left':
    case 'right':
      // For horizontal positions, score based on horizontal space and vertical centering ability
      const horizontalSpaceMain =
        position === 'left' ? spaces.left : spaces.right;
      const verticalSpaceMain = Math.min(spaces.top, spaces.bottom);
      return (
        (horizontalSpaceMain / panelWidth) * 10 +
        (verticalSpaceMain / panelHeight) * 5
      );

    default:
      return 0;
  }
};

/**
 * Detects collisions with viewport boundaries
 *
 * @param {Object} position - Panel position
 * @param {Object} panelDimensions - Panel dimensions
 * @param {Object} viewport - Viewport dimensions
 * @returns {Object} Collision detection result
 */
export const detectCollisions = (position, panelDimensions, viewport) => {
  const { top, left } = position;
  const { width, height } = panelDimensions;
  const padding = PANEL_DEFAULTS.COLLISION_PADDING;

  const collisions = {
    top: top < padding,
    right: left + width > viewport.width - padding,
    bottom: top + height > viewport.height - padding,
    left: left < padding,
  };

  const hasCollision = Object.values(collisions).some(Boolean);

  return {
    hasCollision,
    collisions,
    adjustedPosition: hasCollision
      ? adjustPositionForCollisions(
          position,
          collisions,
          panelDimensions,
          viewport,
          padding,
        )
      : position,
  };
};

/**
 * Adjusts position to avoid collisions
 *
 * @param {Object} position - Original position
 * @param {Object} collisions - Collision flags
 * @param {Object} panelDimensions - Panel dimensions
 * @param {Object} viewport - Viewport dimensions
 * @param {number} padding - Collision padding
 * @returns {Object} Adjusted position
 */
const adjustPositionForCollisions = (
  position,
  collisions,
  panelDimensions,
  viewport,
  padding,
) => {
  let { top, left } = position;
  const { width, height } = panelDimensions;

  if (collisions.left) {
    left = padding;
  } else if (collisions.right) {
    left = viewport.width - width - padding;
  }

  if (collisions.top) {
    top = padding;
  } else if (collisions.bottom) {
    top = viewport.height - height - padding;
  }

  return { top, left };
};

/**
 * Gets component property schema based on component type
 *
 * @param {string} componentType - Type of the component
 * @returns {Object} Property schema for the component
 */
export const getComponentPropertySchema = (componentType) => {
  const baseSchema = {
    [PROPERTY_CATEGORIES.BASIC]: {
      label: { type: 'string', required: false, default: '' },
      name: { type: 'string', required: false, default: '' },
    },
    [PROPERTY_CATEGORIES.STYLING]: {
      size: {
        type: 'select',
        options: ['small', 'middle', 'large'],
        default: 'middle',
      },
      disabled: { type: 'boolean', default: false },
    },
  };

  // Component-specific schemas
  const componentSchemas = {
    input: {
      ...baseSchema,
      [PROPERTY_CATEGORIES.BASIC]: {
        ...baseSchema[PROPERTY_CATEGORIES.BASIC],
        placeholder: { type: 'string', required: false, default: '' },
      },
      [PROPERTY_CATEGORIES.VALIDATION]: {
        required: { type: 'boolean', default: false },
        minLength: { type: 'number', min: 0, default: undefined },
        maxLength: { type: 'number', min: 0, default: undefined },
        pattern: { type: 'string', default: '' },
      },
    },
    select: {
      ...baseSchema,
      [PROPERTY_CATEGORIES.BASIC]: {
        ...baseSchema[PROPERTY_CATEGORIES.BASIC],
        placeholder: {
          type: 'string',
          required: false,
          default: 'Please select...',
        },
        options: { type: 'array', default: [] },
      },
      [PROPERTY_CATEGORIES.VALIDATION]: {
        required: { type: 'boolean', default: false },
      },
    },
    textarea: {
      ...baseSchema,
      [PROPERTY_CATEGORIES.BASIC]: {
        ...baseSchema[PROPERTY_CATEGORIES.BASIC],
        placeholder: { type: 'string', required: false, default: '' },
        rows: { type: 'number', min: 1, max: 20, default: 4 },
      },
      [PROPERTY_CATEGORIES.VALIDATION]: {
        required: { type: 'boolean', default: false },
        minLength: { type: 'number', min: 0, default: undefined },
        maxLength: { type: 'number', min: 0, default: undefined },
      },
    },
  };

  return componentSchemas[componentType] || baseSchema;
};

/**
 * Validates a property value against its schema
 *
 * @param {*} value - Value to validate
 * @param {Object} propertySchema - Property schema definition
 * @returns {Object} Validation result
 */
export const validatePropertyValue = (value, propertySchema) => {
  const { type, required, min, max, options } = propertySchema;

  const errors = [];

  // Required validation
  if (required && (value === undefined || value === null || value === '')) {
    errors.push('This field is required');
  }

  // Type validation
  if (value !== undefined && value !== null && value !== '') {
    switch (type) {
      case 'string':
        if (typeof value !== 'string') {
          errors.push('Value must be a string');
        }
        break;
      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          errors.push('Value must be a number');
        } else {
          if (min !== undefined && value < min) {
            errors.push(`Value must be at least ${min}`);
          }
          if (max !== undefined && value > max) {
            errors.push(`Value must be at most ${max}`);
          }
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          errors.push('Value must be true or false');
        }
        break;
      case 'select':
        if (options && !options.includes(value)) {
          errors.push(`Value must be one of: ${options.join(', ')}`);
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          errors.push('Value must be an array');
        }
        break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Formats a property value for display
 *
 * @param {*} value - Value to format
 * @param {Object} propertySchema - Property schema definition
 * @returns {string} Formatted value
 */
export const formatPropertyValue = (value, propertySchema) => {
  if (value === undefined || value === null) {
    return '';
  }

  const { type } = propertySchema;

  switch (type) {
    case 'boolean':
      return value ? 'Yes' : 'No';
    case 'array':
      return Array.isArray(value) ? value.join(', ') : String(value);
    case 'object':
      return typeof value === 'object'
        ? JSON.stringify(value, null, 2)
        : String(value);
    default:
      return String(value);
  }
};

/**
 * Deep clones an object to avoid mutation
 *
 * @param {*} obj - Object to clone
 * @returns {*} Cloned object
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }

  if (Array.isArray(obj)) {
    return obj.map(deepClone);
  }

  const cloned = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }

  return cloned;
};

/**
 * Sets a nested property value using dot notation
 *
 * @param {Object} obj - Target object
 * @param {string} path - Property path (e.g., 'validation.required')
 * @param {*} value - Value to set
 * @returns {Object} Updated object
 */
export const setNestedProperty = (obj, path, value) => {
  const cloned = deepClone(obj);
  const keys = path.split('.');
  let current = cloned;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }

  current[keys[keys.length - 1]] = value;
  return cloned;
};
