/**
 * StatusBar Component
 * 
 * Displays the AI status bar at the bottom of the chat interface.
 * Shows connection status, processing state, and other system information.
 * 
 * Features:
 * - Real-time connection status with visual indicators
 * - Processing state display with spinner
 * - Color-coded status indicators
 * - Professional enterprise design
 * - Smooth animations and transitions
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.aiConnected - AI connection status
 * @param {string} props.aiStatus - Current AI processing status
 * @param {boolean} props.isProcessing - Whether AI is currently processing
 */

import React, { memo } from 'react';
import { Typography, Tag, Spin } from 'antd';
import {
  ThunderboltOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { AIStatusBar, ConnectionIndicator } from '../styles/StatusComponents';
import { colors } from '../../../styles/theme';

const { Text } = Typography;

/**
 * StatusBar component for AI chat interface
 * 
 * @param {Object} props - Component props
 * @returns {JSX.Element} Rendered status bar component
 */
const StatusBar = memo(({ aiConnected, aiStatus, isProcessing }) => {
  /**
   * Gets the status text based on current AI status
   */
  const getStatusText = () => {
    switch (aiStatus) {
      case 'processing':
        return 'Generating...';
      case 'success':
        return 'Ready';
      case 'error':
        return 'Error';
      default:
        return 'Idle';
    }
  };

  /**
   * Gets the status color based on current AI status
   */
  const getStatusColor = () => {
    switch (aiStatus) {
      case 'processing':
        return colors.accent || '#3b82f6';
      case 'success':
        return colors.success || '#10b981';
      case 'error':
        return colors.error || '#ef4444';
      default:
        return colors.textSecondary || '#6b7280';
    }
  };

  /**
   * Gets the connection tag color
   */
  const getConnectionTagColor = () => {
    return aiConnected ? 'green' : 'red';
  };

  /**
   * Gets the connection icon
   */
  const getConnectionIcon = () => {
    return aiConnected ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />;
  };

  /**
   * Gets the connection text
   */
  const getConnectionText = () => {
    return aiConnected ? 'Online' : 'Offline';
  };

  return (
    <AIStatusBar connected={aiConnected}>
      {/* Left Side - Connection Status */}
      <ConnectionIndicator connected={aiConnected}>
        {/* Animated Status Dot */}
        <div className="status-dot" />

        {/* Thunder Icon */}
        <ThunderboltOutlined
          style={{
            color: aiConnected ? colors.primary : colors.gray400,
            transition: 'color 0.3s ease',
            fontSize: '12px',
          }}
        />

        {/* AI Assistance Label */}
        <Text style={{ fontSize: 11, fontWeight: 500, color: '#374151' }}>
          AI Assistance
        </Text>

        {/* Connection Status Tag */}
        <Tag
          color={getConnectionTagColor()}
          style={{
            fontSize: 9,
            margin: 0,
            fontWeight: 600,
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            border: 'none',
            borderRadius: '4px',
            padding: '2px 6px',
          }}
        >
          {getConnectionIcon()} {getConnectionText()}
        </Tag>
      </ConnectionIndicator>

      {/* Right Side - Processing Status */}
      <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
        {/* Processing Spinner */}
        {isProcessing && (
          <Spin 
            size="small" 
            style={{ 
              fontSize: 8,
              color: colors.accent || '#3b82f6',
            }} 
          />
        )}

        {/* Status Text */}
        <Text
          type="secondary"
          style={{
            fontSize: 10,
            fontWeight: aiStatus === 'processing' ? 600 : 400,
            color: getStatusColor(),
            transition: 'all 0.3s ease',
            textTransform: 'capitalize',
          }}
        >
          {getStatusText()}
        </Text>

        {/* Additional Status Indicator */}
        {aiStatus === 'success' && (
          <div
            style={{
              width: '4px',
              height: '4px',
              borderRadius: '50%',
              backgroundColor: colors.success || '#10b981',
              marginLeft: '4px',
            }}
          />
        )}

        {aiStatus === 'error' && (
          <div
            style={{
              width: '4px',
              height: '4px',
              borderRadius: '50%',
              backgroundColor: colors.error || '#ef4444',
              marginLeft: '4px',
            }}
          />
        )}
      </div>
    </AIStatusBar>
  );
});

// Set display name for debugging
StatusBar.displayName = 'StatusBar';

export default StatusBar;
