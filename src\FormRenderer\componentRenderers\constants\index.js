/**
 * ComponentRenderer Constants - Barrel Export
 *
 * Centralized export point for all ComponentRenderer constants and configurations.
 * Provides a clean import interface for consuming components.
 */

// Export specific constants to avoid conflicts
export {
  DATA_ENTRY_DEFAULTS,
  ADVANCED_DATA_ENTRY_DEFAULTS,
  DISPLAY_DEFAULTS,
  DATA_DISPLAY_DEFAULTS,
  NAVIGATION_DEFAULTS,
  LAYOUT_DEFAULTS,
  FEEDBACK_DEFAULTS,
  CONTAINER_DEFAULTS,
  getComponentDefaults,
  mergeWithDefaults,
} from './componentDefaults';

export {
  DATA_ENTRY_TYPES,
  ADVANCED_DATA_ENTRY_TYPES,
  DISPLAY_TYPES,
  DATA_DISPLAY_TYPES,
  NAVIGATION_TYPES,
  LAYOUT_TYPES,
  FEEDBACK_TYPES,
  CONTAINER_TYPES,
  COMPONENT_TYPES_BY_CATEGORY,
  ALL_COMPONENT_TYPES,
  TYPE_TO_CATEGORY_MAP,
  FORM_BINDABLE_TYPES,
  VALIDATABLE_TYPES,
  CONTAINER_COMPONENT_TYPES,
  READONLY_TYPES,
  COMPONENT_SIZES,
  VALIDATION_STATUS,
  BUTTON_TYPES as COMPONENT_BUTTON_TYPES,
  INPUT_TYPES,
  ALERT_TYPES,
  PROGRESS_TYPES,
  getComponentCategory as getConstantsComponentCategory,
  isValidComponentType as isValidComponentTypeConstants,
  requiresFormBinding as requiresFormBindingConstants,
  supportsValidation as supportsValidationConstants,
  canHaveChildren as canHaveChildrenConstants,
  isReadOnly,
  getTypesByCategory,
  TYPE_ALIASES,
  resolveComponentType,
} from './componentTypes';

export {
  RENDERING_PERFORMANCE,
  ERROR_HANDLING,
  VALIDATION_CONSTANTS,
  STYLING_CONSTANTS,
  LAYOUT_CONSTANTS,
  ANIMATION_CONSTANTS,
  ACCESSIBILITY_CONSTANTS,
  DATA_CONSTANTS,
  DEVELOPMENT_CONSTANTS,
  COMPONENT_STATE,
  EVENT_CONSTANTS,
} from './renderingConstants';
