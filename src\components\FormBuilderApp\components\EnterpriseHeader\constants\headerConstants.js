/**
 * @fileoverview Enterprise Header constants and configuration
 *
 * This module contains all constants, configuration objects, and default values
 * for the enterprise header/toolbar system.
 *
 * @module headerConstants
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

/**
 * Header action types for consistent event handling
 *
 * @constant {Object} HEADER_ACTIONS
 */
export const HEADER_ACTIONS = {
  // Project management
  SAVE_PROJECT: 'save_project',
  PUBLISH_PROJECT: 'publish_project',
  EXPORT_PROJECT: 'export_project',
  IMPORT_PROJECT: 'import_project',
  
  // Version control
  VIEW_HISTORY: 'view_history',
  CREATE_VERSION: 'create_version',
  RESTORE_VERSION: 'restore_version',
  
  // Collaboration
  SHARE_PROJECT: 'share_project',
  ADD_COMMENT: 'add_comment',
  INVITE_USER: 'invite_user',
  
  // Form operations
  PREVIEW_FORM: 'preview_form',
  TEST_FORM: 'test_form',
  VALIDATE_FORM: 'validate_form',
  
  // Analytics
  VIEW_ANALYTICS: 'view_analytics',
  EXPORT_DATA: 'export_data',
};

/**
 * Project status types
 *
 * @constant {Object} PROJECT_STATUS
 */
export const PROJECT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  ARCHIVED: 'archived',
  SHARED: 'shared',
};

/**
 * Form complexity levels for metrics
 *
 * @constant {Object} COMPLEXITY_LEVELS
 */
export const COMPLEXITY_LEVELS = {
  SIMPLE: { level: 'simple', threshold: 5, color: '#52c41a' },
  MODERATE: { level: 'moderate', threshold: 15, color: '#faad14' },
  COMPLEX: { level: 'complex', threshold: 30, color: '#ff7a45' },
  ENTERPRISE: { level: 'enterprise', threshold: Infinity, color: '#f5222d' },
};

/**
 * Default project metadata
 *
 * @constant {Object} DEFAULT_PROJECT
 */
export const DEFAULT_PROJECT = {
  id: null,
  name: 'Untitled Form',
  description: '',
  status: PROJECT_STATUS.DRAFT,
  createdAt: null,
  updatedAt: null,
  version: '1.0.0',
  author: 'Anonymous',
  tags: [],
  isPublic: false,
};

/**
 * Header UI text constants
 *
 * @constant {Object} HEADER_TEXT
 */
export const HEADER_TEXT = {
  PROJECT: {
    SAVE: 'Save',
    PUBLISH: 'Publish',
    EXPORT: 'Export',
    IMPORT: 'Import',
    UNTITLED: 'Untitled Form',
  },
  METRICS: {
    COMPONENTS: 'Components',
    CONTAINERS: 'Containers',
    COMPLEXITY: 'Complexity',
    PERFORMANCE: 'Performance',
  },
  COLLABORATION: {
    SHARE: 'Share',
    COMMENT: 'Comment',
    INVITE: 'Invite',
    USERS: 'Users',
  },
  ACTIONS: {
    PREVIEW: 'Preview',
    TEST: 'Test',
    VALIDATE: 'Validate',
    ANALYTICS: 'Analytics',
  },
};

/**
 * Animation configurations for header elements
 *
 * @constant {Object} HEADER_ANIMATIONS
 */
export const HEADER_ANIMATIONS = {
  SAVE_SUCCESS: {
    scale: [1, 1.1, 1],
    transition: { duration: 0.3 },
  },
  PUBLISH_SUCCESS: {
    backgroundColor: ['#1890ff', '#52c41a', '#1890ff'],
    transition: { duration: 0.6 },
  },
  METRIC_UPDATE: {
    y: [-5, 0],
    opacity: [0, 1],
    transition: { duration: 0.2 },
  },
};

/**
 * Responsive breakpoints for header layout
 *
 * @constant {Object} HEADER_BREAKPOINTS
 */
export const HEADER_BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1200,
  WIDE: 1600,
};

/**
 * Z-index levels for header elements
 *
 * @constant {Object} HEADER_Z_INDEX
 */
export const HEADER_Z_INDEX = {
  BASE: 100,
  DROPDOWN: 101,
  MODAL: 102,
  TOOLTIP: 103,
};
