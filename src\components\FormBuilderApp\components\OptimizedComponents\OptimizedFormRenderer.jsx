/**
 * @fileoverview Optimized Form Renderer component
 *
 * This component provides a performance-optimized version of the form renderer
 * with React.memo, useMemo, useCallback, and virtual scrolling for large forms.
 *
 * @module OptimizedFormRenderer
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { memo, useMemo, useCallback, useState, useRef } from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { usePerformanceOptimization } from '../../hooks/usePerformanceOptimization';
// Note: react-window would need to be installed for virtual scrolling
// For now, we'll implement a simplified version
// import { FixedSizeList as List } from 'react-window';
// import OptimizedComponentRenderer from './OptimizedComponentRenderer';

/**
 * Styled components
 */
const FormContainer = styled.div`
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
`;

const VirtualListContainer = styled.div`
  height: 100%;
  width: 100%;

  .virtual-item {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }
`;

const StandardContainer = styled.div`
  padding: 16px;
  max-height: 100%;
  overflow-y: auto;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
`;

/**
 * Virtual list item component
 */
const VirtualListItem = memo(
  ({ index, style, data }) => {
    const { components, onComponentUpdate, selectedComponentId } = data;
    const component = components[index];

    return (
      <div style={style} className='virtual-item'>
        <OptimizedComponentRenderer
          key={component.id}
          component={component}
          onUpdate={onComponentUpdate}
          isSelected={selectedComponentId === component.id}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    const prevComponent = prevProps.data.components[prevProps.index];
    const nextComponent = nextProps.data.components[nextProps.index];

    return (
      prevComponent?.id === nextComponent?.id &&
      prevComponent?.version === nextComponent?.version &&
      prevProps.data.selectedComponentId === nextProps.data.selectedComponentId
    );
  },
);

VirtualListItem.displayName = 'VirtualListItem';

/**
 * Optimized Form Renderer component
 *
 * @param {Object} props - Component props
 * @param {Array} props.components - Array of form components
 * @param {Function} props.onComponentUpdate - Component update callback
 * @param {string} props.selectedComponentId - Currently selected component ID
 * @param {boolean} props.enableVirtualization - Enable virtual scrolling for large forms
 * @param {number} props.virtualizationThreshold - Threshold for enabling virtualization
 * @param {number} props.itemHeight - Height of each virtual list item
 * @param {Object} props.formSettings - Form-level settings
 * @param {boolean} props.enablePerformanceMonitoring - Enable performance monitoring
 * @returns {React.ReactNode} Optimized form renderer JSX
 */
const OptimizedFormRenderer = memo(
  ({
    components = [],
    onComponentUpdate,
    selectedComponentId,
    enableVirtualization = true,
    virtualizationThreshold = 50,
    itemHeight = 120,
    formSettings = {},
    enablePerformanceMonitoring = false,
  }) => {
    const containerRef = useRef(null);
    const [containerHeight, setContainerHeight] = useState(600);

    // Performance optimization hook
    const {
      startMeasurement,
      endMeasurement,
      createMemoComparison,
      useStableObject,
    } = usePerformanceOptimization({
      enableProfiling: enablePerformanceMonitoring,
    });

    /**
     * Stable form settings object
     */
    const stableFormSettings = useStableObject(formSettings);

    /**
     * Determine if virtualization should be used
     */
    const shouldUseVirtualization = useMemo(() => {
      return (
        enableVirtualization && components.length > virtualizationThreshold
      );
    }, [enableVirtualization, components.length, virtualizationThreshold]);

    /**
     * Memoized component data for virtual list
     */
    const virtualListData = useMemo(
      () => ({
        components,
        onComponentUpdate,
        selectedComponentId,
      }),
      [components, onComponentUpdate, selectedComponentId],
    );

    /**
     * Optimized component update handler
     */
    const handleComponentUpdate = useCallback(
      (componentId, updates) => {
        startMeasurement('component-update');

        if (onComponentUpdate) {
          onComponentUpdate(componentId, updates);
        }

        endMeasurement('component-update');
      },
      [onComponentUpdate, startMeasurement, endMeasurement],
    );

    /**
     * Handle container resize
     */
    const handleResize = useCallback(() => {
      if (containerRef.current) {
        const { height } = containerRef.current.getBoundingClientRect();
        setContainerHeight(height);
      }
    }, []);

    /**
     * Resize observer effect
     */
    React.useEffect(() => {
      const resizeObserver = new ResizeObserver(handleResize);

      if (containerRef.current) {
        resizeObserver.observe(containerRef.current);
        handleResize(); // Initial measurement
      }

      return () => resizeObserver.disconnect();
    }, [handleResize]);

    /**
     * Render virtual list
     */
    const renderVirtualList = useCallback(
      () => (
        <VirtualListContainer>
          <List
            height={containerHeight}
            itemCount={components.length}
            itemSize={itemHeight}
            itemData={virtualListData}
            overscanCount={5}
            width='100%'
          >
            {VirtualListItem}
          </List>
        </VirtualListContainer>
      ),
      [containerHeight, components.length, itemHeight, virtualListData],
    );

    /**
     * Render standard list
     */
    const renderStandardList = useCallback(
      () => (
        <StandardContainer>
          {components.map((component) => (
            <motion.div
              key={component.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              style={{ marginBottom: '16px' }}
            >
              <OptimizedComponentRenderer
                component={component}
                onUpdate={handleComponentUpdate}
                isSelected={selectedComponentId === component.id}
                formSettings={stableFormSettings}
              />
            </motion.div>
          ))}
        </StandardContainer>
      ),
      [
        components,
        handleComponentUpdate,
        selectedComponentId,
        stableFormSettings,
      ],
    );

    /**
     * Performance measurement effect
     */
    React.useEffect(() => {
      startMeasurement('form-render');

      return () => {
        endMeasurement('form-render');
      };
    }, [components.length, startMeasurement, endMeasurement]);

    return (
      <FormContainer ref={containerRef}>
        {shouldUseVirtualization ? renderVirtualList() : renderStandardList()}
      </FormContainer>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for optimal re-rendering
    return (
      prevProps.components.length === nextProps.components.length &&
      prevProps.components.every(
        (comp, index) =>
          comp.id === nextProps.components[index]?.id &&
          comp.version === nextProps.components[index]?.version,
      ) &&
      prevProps.selectedComponentId === nextProps.selectedComponentId &&
      prevProps.enableVirtualization === nextProps.enableVirtualization &&
      prevProps.virtualizationThreshold === nextProps.virtualizationThreshold &&
      prevProps.itemHeight === nextProps.itemHeight
    );
  },
);

OptimizedFormRenderer.displayName = 'OptimizedFormRenderer';

export default OptimizedFormRenderer;
