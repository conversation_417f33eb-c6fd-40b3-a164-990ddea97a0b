/**
 * @fileoverview Unified Enterprise Header component
 *
 * Streamlined header component that consolidates project controls, metrics, and actions
 * into a clean, organized toolbar with intelligent grouping and contextual visibility.
 * Implements progressive disclosure and enterprise-grade UX patterns.
 *
 * @module EnterpriseHeader
 * @version 2.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Button, Space, Typography, Divider, Dropdown, Badge } from 'antd';
import {
  SaveOutlined,
  EyeOutlined,
  EditOutlined,
  CloudUploadOutlined,
  ExportOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  BarChartOutlined,
  SettingOutlined,
  ShareAltOutlined,
  MoreOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

/**
 * Clean Enterprise Header component
 */
const EnterpriseHeader = ({
  activeTab,
  setActiveTab,
  builderTabProps,
  previewTabProps,
  onUpdateFormSchema,
  componentUpdateCounter,
}) => {
  // Extract current form data with real-time updates
  const currentFormData = useMemo(() => {
    const props = activeTab === 'builder' ? builderTabProps : previewTabProps;
    console.log('🔄 [EnterpriseHeader] Updating currentFormData:', {
      activeTab,
      layout: props?.layout?.length || 0,
      components: Object.keys(props?.components || {}).length,
      forceRenderKey: props?.forceRenderKey,
      componentUpdateCounter,
    });
    return {
      layout: props?.layout || [],
      components: props?.components || {},
      currentFormSchema: props?.currentFormSchema || {},
    };
  }, [activeTab, builderTabProps, previewTabProps, componentUpdateCounter]);

  // Calculate real-time metrics
  const formMetrics = useMemo(() => {
    const componentCount = Object.keys(currentFormData.components).length;
    console.log('📊 [EnterpriseHeader] Calculating metrics:', {
      componentCount,
      layoutLength: currentFormData.layout.length,
      components: Object.keys(currentFormData.components),
    });

    // Count containers recursively
    const countContainers = (items) => {
      if (!Array.isArray(items)) return 0;

      let count = 0;
      items.forEach((item) => {
        if (
          [
            'tabContainer',
            'cardContainer',
            'accordionContainer',
            'stepsContainer',
            'formSection',
          ].includes(item.type)
        ) {
          count++;
        }
        // Recursively count in children
        if (item.children && Array.isArray(item.children)) {
          count += countContainers(item.children);
        }
        // Count in tabs for tab containers
        if (item.type === 'tabContainer' && item.tabs) {
          item.tabs.forEach((tab) => {
            if (tab.children) {
              count += countContainers(tab.children);
            }
          });
        }
      });
      return count;
    };

    const containerCount = countContainers(currentFormData.layout);
    const complexity =
      componentCount > 20 ? 'High' : componentCount > 10 ? 'Med' : 'Low';

    return {
      componentCount,
      containerCount,
      complexity,
    };
  }, [currentFormData.layout, currentFormData.components]);

  // Action handlers
  const handleSave = useCallback(() => {
    console.log('Saving project...');
    // TODO: Implement save logic
  }, []);

  const handlePublish = useCallback(() => {
    console.log('Publishing project...');
    // TODO: Implement publish logic
  }, []);

  const handleExport = useCallback(() => {
    console.log('Exporting project...');
    // TODO: Implement export logic
  }, []);

  const handlePreview = useCallback(() => {
    console.log('Opening preview...');
    // TODO: Implement preview logic
  }, []);

  const handleTest = useCallback(() => {
    console.log('Testing form...');
    // TODO: Implement test logic
  }, []);

  const handleValidate = useCallback(() => {
    console.log('Validating form...');
    // TODO: Implement validation logic
  }, []);

  const handleAnalytics = useCallback(() => {
    console.log('Opening analytics...');
    // TODO: Implement analytics logic
  }, []);

  const handleSettings = useCallback(() => {
    console.log('Opening settings...');
    // TODO: Implement settings logic
  }, []);

  const handleShare = useCallback(() => {
    console.log('Opening share...');
    // TODO: Implement share logic
  }, []);

  // Handle title change
  const handleTitleChange = useCallback(
    (newTitle) => {
      console.log('Updating form title:', newTitle);
      if (onUpdateFormSchema) {
        onUpdateFormSchema({
          ...currentFormData.currentFormSchema,
          title: newTitle,
        });
      }
    },
    [currentFormData.currentFormSchema, onUpdateFormSchema],
  );

  // Handle mode switching
  const handleModeSwitch = useCallback(
    (newMode) => {
      console.log('Switching to mode:', newMode);
      if (setActiveTab) {
        setActiveTab(newMode);
      }
    },
    [setActiveTab],
  );

  // Project actions dropdown
  const projectActionsMenu = {
    items: [
      {
        key: 'save',
        icon: <SaveOutlined />,
        label: 'Save',
        onClick: handleSave,
      },
      { type: 'divider' },
      {
        key: 'publish',
        icon: <CloudUploadOutlined />,
        label: 'Publish',
        onClick: handlePublish,
      },
      {
        key: 'export',
        icon: <ExportOutlined />,
        label: 'Export',
        onClick: handleExport,
      },
    ],
  };

  // Context actions dropdown
  const contextActionsMenu = {
    items: [
      ...(activeTab === 'builder'
        ? [
            {
              key: 'test',
              icon: <PlayCircleOutlined />,
              label: 'Test Form',
              onClick: handleTest,
            },
            {
              key: 'validate',
              icon: <CheckCircleOutlined />,
              label: 'Validate',
              onClick: handleValidate,
            },
          ]
        : [
            {
              key: 'test-submit',
              icon: <PlayCircleOutlined />,
              label: 'Test Submit',
              onClick: handleTest,
            },
          ]),
      { type: 'divider' },
      {
        key: 'analytics',
        icon: <BarChartOutlined />,
        label: 'Analytics',
        onClick: handleAnalytics,
      },
      {
        key: 'share',
        icon: <ShareAltOutlined />,
        label: 'Share',
        onClick: handleShare,
      },
      {
        key: 'settings',
        icon: <SettingOutlined />,
        label: 'Settings',
        onClick: handleSettings,
      },
    ],
  };

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '12px 24px',
        background: '#fff',
        borderBottom: '1px solid #e0e0e0',
        boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
        minHeight: '64px',
      }}
    >
      {/* Left Section - Project Info & Primary Actions */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
        {/* Project Info */}
        <div>
          <Title
            level={4}
            editable={{
              onChange: handleTitleChange,
              tooltip: 'Click to edit form title',
            }}
            style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}
          >
            {currentFormData.currentFormSchema?.title || 'Untitled Form'}
          </Title>
          <Text type='secondary' style={{ fontSize: '12px' }}>
            {formMetrics.componentCount} components • Last saved:{' '}
            {new Date().toLocaleTimeString()}
          </Text>
        </div>

        {/* Project Actions Menu - moved next to last saved */}
        <Dropdown menu={projectActionsMenu} placement='bottomLeft'>
          <Button
            icon={<MoreOutlined />}
            size='small'
            style={{ marginLeft: '8px' }}
          />
        </Dropdown>
      </div>

      {/* Center Section - Real-time Form Metrics */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
        <div style={{ textAlign: 'center' }}>
          <motion.div
            key={`components-${formMetrics.componentCount}`}
            initial={{ scale: 1.2, opacity: 0.7 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            style={{
              fontSize: '18px',
              fontWeight: 600,
              color: '#1890ff',
            }}
          >
            {formMetrics.componentCount}
          </motion.div>
          <div
            style={{
              fontSize: '11px',
              color: '#8c8c8c',
              textTransform: 'uppercase',
            }}
          >
            Components
          </div>
        </div>

        <Divider type='vertical' style={{ height: '32px' }} />

        <div style={{ textAlign: 'center' }}>
          <motion.div
            key={`containers-${formMetrics.containerCount}`}
            initial={{ scale: 1.2, opacity: 0.7 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            style={{
              fontSize: '18px',
              fontWeight: 600,
              color: '#52c41a',
            }}
          >
            {formMetrics.containerCount}
          </motion.div>
          <div
            style={{
              fontSize: '11px',
              color: '#8c8c8c',
              textTransform: 'uppercase',
            }}
          >
            Containers
          </div>
        </div>

        <Divider type='vertical' style={{ height: '32px' }} />

        <div style={{ textAlign: 'center' }}>
          <motion.div
            key={`complexity-${formMetrics.complexity}`}
            initial={{ scale: 1.2, opacity: 0.7 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            style={{
              fontSize: '18px',
              fontWeight: 600,
              color:
                formMetrics.complexity === 'High'
                  ? '#ff4d4f'
                  : formMetrics.complexity === 'Med'
                  ? '#fa8c16'
                  : '#52c41a',
            }}
          >
            {formMetrics.complexity}
          </motion.div>
          <div
            style={{
              fontSize: '11px',
              color: '#8c8c8c',
              textTransform: 'uppercase',
            }}
          >
            Complexity
          </div>
        </div>
      </div>

      {/* Right Section - Mode Switcher & Actions */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        {/* Mode Switcher - Replaces sub-header tabs */}
        <div
          style={{
            display: 'flex',
            background: '#f5f5f5',
            borderRadius: '8px',
            padding: '4px',
            border: '1px solid #e0e0e0',
          }}
        >
          <button
            onClick={() => handleModeSwitch('builder')}
            style={{
              padding: '8px 16px',
              border: 'none',
              borderRadius: '6px',
              background: activeTab === 'builder' ? '#1890ff' : 'transparent',
              color: activeTab === 'builder' ? '#fff' : '#666',
              fontSize: '13px',
              fontWeight: 500,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
            }}
          >
            🛠️ Builder
          </button>
          <button
            onClick={() => handleModeSwitch('preview')}
            style={{
              padding: '8px 16px',
              border: 'none',
              borderRadius: '6px',
              background: activeTab === 'preview' ? '#1890ff' : 'transparent',
              color: activeTab === 'preview' ? '#fff' : '#666',
              fontSize: '13px',
              fontWeight: 500,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
            }}
          >
            👁️ Preview
          </button>
        </div>

        {/* Quick Actions */}
        <Space size='small'>
          <Button icon={<PlayCircleOutlined />} onClick={handleTest}>
            Test
          </Button>

          <Dropdown menu={contextActionsMenu} placement='bottomRight'>
            <Button icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      </div>
    </div>
  );
};

export default EnterpriseHeader;
