/**
 * @fileoverview Component Manager Panel
 *
 * This component provides a comprehensive management interface for form components
 * including hierarchy tree, analytics, search, and templates.
 *
 * @module ComponentManagerPanel
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useState, useCallback } from 'react';
import { Layout, Tabs, Button, Space, Tooltip } from 'antd';
import {
  AppstoreOutlined,
  BarChartOutlined,
  SearchOutlined,
  FileTextOutlined,
  SettingOutlined,
  FullscreenOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import ComponentHierarchyTree from './ComponentHierarchyTree';
import ComponentAnalytics from './ComponentAnalytics';
import { MANAGEMENT_ANIMATIONS } from '../constants/managementConstants';

const { Sider } = Layout;

/**
 * Styled components
 */
const ManagerContainer = styled(Sider)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid #e8e8e8;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.06);
  
  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
`;

const ManagerHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  
  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .header-actions {
    display: flex;
    gap: 8px;
  }
`;

const ManagerContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .ant-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .ant-tabs-content-holder {
      flex: 1;
      overflow: hidden;
      
      .ant-tabs-content {
        height: 100%;
        
        .ant-tabs-tabpane {
          height: 100%;
          overflow: hidden;
        }
      }
    }
    
    .ant-tabs-tab {
      padding: 8px 12px;
      
      .ant-tabs-tab-btn {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
`;

const SearchPanel = styled.div`
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  text-align: center;
  
  .search-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }
  
  .search-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .search-description {
    font-size: 14px;
    color: #999;
  }
`;

const TemplatesPanel = styled.div`
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  text-align: center;
  
  .templates-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }
  
  .templates-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .templates-description {
    font-size: 14px;
    color: #999;
  }
`;

/**
 * Component Manager Panel component
 *
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the panel is visible
 * @param {Array} props.components - Array of form components
 * @param {string} props.selectedComponentId - Currently selected component ID
 * @param {Function} props.onComponentSelect - Component selection callback
 * @param {Function} props.onComponentUpdate - Component update callback
 * @param {Function} props.onComponentDelete - Component delete callback
 * @param {Function} props.onComponentDuplicate - Component duplicate callback
 * @param {Function} props.onClose - Panel close callback
 * @param {number} props.width - Panel width
 * @param {boolean} props.collapsible - Whether panel is collapsible
 * @returns {React.ReactNode} Component manager panel JSX
 */
const ComponentManagerPanel = ({
  visible = false,
  components = [],
  selectedComponentId,
  onComponentSelect,
  onComponentUpdate,
  onComponentDelete,
  onComponentDuplicate,
  onClose,
  width = 320,
  collapsible = true,
}) => {
  const [activeTab, setActiveTab] = useState('hierarchy');
  const [isFullscreen, setIsFullscreen] = useState(false);

  /**
   * Handle tab change
   */
  const handleTabChange = useCallback((key) => {
    setActiveTab(key);
  }, []);

  /**
   * Handle fullscreen toggle
   */
  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  /**
   * Tab items configuration
   */
  const tabItems = [
    {
      key: 'hierarchy',
      label: (
        <span>
          <AppstoreOutlined />
          Hierarchy
        </span>
      ),
      children: (
        <ComponentHierarchyTree
          components={components}
          selectedComponentId={selectedComponentId}
          onComponentSelect={onComponentSelect}
          onComponentUpdate={onComponentUpdate}
          onComponentDelete={onComponentDelete}
          onComponentDuplicate={onComponentDuplicate}
          showSearch={true}
        />
      ),
    },
    {
      key: 'analytics',
      label: (
        <span>
          <BarChartOutlined />
          Analytics
        </span>
      ),
      children: (
        <ComponentAnalytics
          components={components}
          analyticsData={{}}
          onPeriodChange={(period) => console.log('Period changed:', period)}
        />
      ),
    },
    {
      key: 'search',
      label: (
        <span>
          <SearchOutlined />
          Search
        </span>
      ),
      children: (
        <SearchPanel>
          <SearchOutlined className="search-icon" />
          <div className="search-title">Advanced Search</div>
          <div className="search-description">
            Search and filter components by type, category, usage, and more
          </div>
        </SearchPanel>
      ),
    },
    {
      key: 'templates',
      label: (
        <span>
          <FileTextOutlined />
          Templates
        </span>
      ),
      children: (
        <TemplatesPanel>
          <FileTextOutlined className="templates-icon" />
          <div className="templates-title">Component Templates</div>
          <div className="templates-description">
            Save and reuse component configurations as templates
          </div>
        </TemplatesPanel>
      ),
    },
  ];

  if (!visible) {
    return null;
  }

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          variants={MANAGEMENT_ANIMATIONS.TREE_EXPAND}
          initial="hidden"
          animate="visible"
          exit="exit"
          style={{ height: '100%' }}
        >
          <ManagerContainer
            width={isFullscreen ? '100vw' : width}
            collapsible={collapsible && !isFullscreen}
            collapsedWidth={0}
            style={{
              position: isFullscreen ? 'fixed' : 'relative',
              top: isFullscreen ? 0 : 'auto',
              left: isFullscreen ? 0 : 'auto',
              zIndex: isFullscreen ? 1000 : 'auto',
              height: isFullscreen ? '100vh' : '100%',
            }}
          >
            <ManagerHeader>
              <div className="header-title">
                <span>
                  <SettingOutlined style={{ marginRight: '8px' }} />
                  Component Manager
                </span>
                <div className="header-actions">
                  <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}>
                    <Button
                      type="text"
                      size="small"
                      icon={<FullscreenOutlined />}
                      onClick={handleFullscreenToggle}
                    />
                  </Tooltip>
                  <Tooltip title="Close Panel">
                    <Button
                      type="text"
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={onClose}
                    />
                  </Tooltip>
                </div>
              </div>
            </ManagerHeader>

            <ManagerContent>
              <Tabs
                activeKey={activeTab}
                onChange={handleTabChange}
                items={tabItems}
                size="small"
                tabPosition="top"
                style={{ height: '100%' }}
              />
            </ManagerContent>
          </ManagerContainer>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ComponentManagerPanel;
