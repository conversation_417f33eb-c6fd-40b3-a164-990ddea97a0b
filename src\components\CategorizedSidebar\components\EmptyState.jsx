/**
 * EmptyState Component
 * 
 * Displays appropriate empty state messages when no components are available
 * or when search/filtering returns no results. Provides helpful guidance
 * to users in different scenarios.
 * 
 * Features:
 * - Different empty state types (no data, no results, error)
 * - Contextual messaging based on the situation
 * - Visual icons and styling for better UX
 * - Actionable suggestions for users
 * - Responsive design with consistent spacing
 * 
 * Props:
 * - type: Type of empty state ('no-data', 'no-results', 'error')
 * - searchTerm: Current search term (for no-results state)
 * - onClearSearch: Callback to clear search
 * - message: Custom message override
 * 
 * @param {Object} props - Component props
 * @returns {JSX.Element} Rendered empty state component
 */

import React, { memo } from 'react';
import { Button, Typography } from 'antd';
import { 
  FilterOutlined, 
  InboxOutlined, 
  ExclamationCircleOutlined,
  ClearOutlined 
} from '@ant-design/icons';
import { colors } from '../../../styles/theme';

const { Text } = Typography;

/**
 * EmptyState component for various empty scenarios
 * 
 * This component handles different types of empty states that can occur
 * in the sidebar, providing appropriate messaging and actions for each.
 * 
 * Empty State Types:
 * - no-data: No components available in the system
 * - no-results: Search/filter returned no results
 * - error: Error occurred while loading components
 * 
 * @param {Object} props - Component props
 * @param {string} props.type - Type of empty state
 * @param {string} props.searchTerm - Current search term
 * @param {Function} props.onClearSearch - Function to clear search
 * @param {string} props.message - Custom message override
 * @returns {JSX.Element} Rendered empty state
 */
const EmptyState = memo(({ 
  type = 'no-results', 
  searchTerm = '', 
  onClearSearch, 
  message 
}) => {
  /**
   * Gets the appropriate icon for the empty state type
   * 
   * Returns the most suitable icon component based on the
   * type of empty state being displayed.
   * 
   * @returns {JSX.Element} Icon component
   */
  const getIcon = () => {
    const iconStyle = { 
      fontSize: '24px', 
      marginBottom: '8px',
      color: colors.textTertiary 
    };

    switch (type) {
      case 'no-data':
        return <InboxOutlined style={iconStyle} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ ...iconStyle, color: colors.error }} />;
      case 'no-results':
      default:
        return <FilterOutlined style={iconStyle} />;
    }
  };

  /**
   * Gets the primary message for the empty state
   * 
   * Returns appropriate messaging based on the empty state type
   * and current context (search term, etc.).
   * 
   * @returns {string} Primary message text
   */
  const getPrimaryMessage = () => {
    if (message) return message;

    switch (type) {
      case 'no-data':
        return 'No components available';
      case 'error':
        return 'Error loading components';
      case 'no-results':
        return searchTerm 
          ? `No components found for "${searchTerm}"`
          : 'No components found';
      default:
        return 'No components found';
    }
  };

  /**
   * Gets the secondary message with helpful suggestions
   * 
   * Provides additional context and suggestions for users
   * based on the empty state type.
   * 
   * @returns {string} Secondary message text
   */
  const getSecondaryMessage = () => {
    switch (type) {
      case 'no-data':
        return 'Components will appear here when available';
      case 'error':
        return 'Please try refreshing the page';
      case 'no-results':
        return searchTerm 
          ? 'Try different search terms or clear the search'
          : 'Try adjusting your search criteria';
      default:
        return 'Try different search terms';
    }
  };

  /**
   * Renders action buttons based on the empty state type
   * 
   * Provides appropriate action buttons that users can take
   * to resolve the empty state situation.
   * 
   * @returns {JSX.Element|null} Action buttons or null
   */
  const renderActions = () => {
    if (type === 'no-results' && searchTerm && onClearSearch) {
      return (
        <Button
          type="link"
          size="small"
          icon={<ClearOutlined />}
          onClick={onClearSearch}
          style={{
            color: colors.primary,
            fontSize: '12px',
            padding: '4px 8px',
            height: 'auto',
            marginTop: '8px',
          }}
        >
          Clear search
        </Button>
      );
    }

    if (type === 'error') {
      return (
        <Button
          type="link"
          size="small"
          onClick={() => window.location.reload()}
          style={{
            color: colors.primary,
            fontSize: '12px',
            padding: '4px 8px',
            height: 'auto',
            marginTop: '8px',
          }}
        >
          Refresh page
        </Button>
      );
    }

    return null;
  };

  /**
   * Gets container styling based on empty state type
   * 
   * Provides appropriate styling for different empty state types,
   * including error states that might need different visual treatment.
   * 
   * @returns {Object} Style object for container
   */
  const getContainerStyle = () => {
    const baseStyle = {
      textAlign: 'center',
      padding: '40px 20px',
      color: colors.textSecondary,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    };

    if (type === 'error') {
      return {
        ...baseStyle,
        backgroundColor: colors.error + '05',
        borderRadius: '8px',
        border: `1px solid ${colors.error}20`,
      };
    }

    return baseStyle;
  };

  return (
    <div style={getContainerStyle()}>
      {/* Icon */}
      {getIcon()}
      
      {/* Primary message */}
      <div style={{ 
        fontSize: '14px',
        fontWeight: 500,
        color: type === 'error' ? colors.error : colors.textSecondary,
        marginBottom: '4px'
      }}>
        {getPrimaryMessage()}
      </div>
      
      {/* Secondary message */}
      <Text 
        type='secondary' 
        style={{ 
          fontSize: '12px',
          lineHeight: 1.4,
          maxWidth: '200px'
        }}
      >
        {getSecondaryMessage()}
      </Text>

      {/* Action buttons */}
      {renderActions()}
    </div>
  );
});

// Set display name for debugging
EmptyState.displayName = 'EmptyState';

export default EmptyState;
