{"metadata": {"version": "1.0.0", "title": "Test Import Form", "description": "Simple test form for JSON import functionality", "createdAt": "2024-01-01T00:00:00.000Z"}, "layout": [{"id": "test-row-1", "type": "row", "children": [{"id": "test-col-1", "type": "column", "span": 24, "children": [{"id": "test-input-1", "type": "input", "componentType": "input"}]}]}, {"id": "test-row-2", "type": "row", "children": [{"id": "test-col-2", "type": "column", "span": 12, "children": [{"id": "test-select-1", "type": "select", "componentType": "select"}]}, {"id": "test-col-3", "type": "column", "span": 12, "children": [{"id": "test-button-1", "type": "button", "componentType": "button"}]}]}], "components": {"test-input-1": {"id": "test-input-1", "type": "input", "componentType": "input", "label": "Test Input Field", "placeholder": "Enter test value", "required": true, "name": "testInput"}, "test-select-1": {"id": "test-select-1", "type": "select", "componentType": "select", "label": "Test Select", "placeholder": "Choose option", "required": false, "name": "testSelect", "options": [{"label": "Option 1", "value": "option1"}, {"label": "Option 2", "value": "option2"}, {"label": "Option 3", "value": "option3"}]}, "test-button-1": {"id": "test-button-1", "type": "button", "componentType": "button", "label": "Test <PERSON>", "buttonType": "primary", "size": "large"}}}