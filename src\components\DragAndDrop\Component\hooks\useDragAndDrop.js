/**
 * useDragAndDrop Hook
 *
 * Custom hook for managing drag-and-drop functionality in components.
 * Integrates with react-dnd to provide smooth dragging experience with
 * proper state management and visual feedback.
 *
 * Features:
 * - React DnD integration for drag functionality
 * - Drag state management and monitoring
 * - Optimized drag item creation with memoization
 * - Ref management for drag targets
 * - Visual feedback during drag operations
 * - Performance optimizations for smooth 60fps dragging
 *
 * @module useDragAndDrop
 */

import { useRef, useMemo, useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { COMPONENT } from '../../../../constants';

/**
 * Custom hook for drag-and-drop functionality
 *
 * @param {Object} options - Configuration options
 * @param {Object} options.data - Component data for dragging
 * @param {string} options.path - Component path in the tree
 * @param {string} options.dragType - Type of drag item (defaults to COMPONENT)
 * @returns {Object} Drag state and utilities
 *
 * @example
 * const {
 *   dragRef,
 *   isDragging,
 *   dragItem,
 *   canDrag,
 *   dragPreview
 * } = useDragAndDrop({
 *   data: componentData,
 *   path: 'root.0.children.1',
 *   dragType: COMPONENT
 * });
 */
export const useDragAndDrop = ({ data, path, dragType = COMPONENT }) => {
  const dragRef = useRef(null);

  // Memoized drag item to prevent unnecessary re-creations
  // Uses optional chaining for safety and includes all necessary data
  const dragItem = useMemo(
    () => ({
      id: data?.id,
      type: data?.type || dragType,
      path,
      // Include additional data that might be needed during drop
      componentData: data,
      timestamp: Date.now(), // For debugging and tracking
    }),
    [data?.id, data?.type, dragType, path, data],
  );

  // React DnD drag configuration
  const [{ isDragging, canDrag }, drag, dragPreview] = useDrag({
    type: data?.type || dragType,
    item: () => {
      console.log('🎯 [useDragAndDrop] Drag started:', {
        id: data?.id,
        type: data?.type,
        path,
      });
      return dragItem;
    },

    // Collect function to monitor drag state
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
      canDrag: monitor.canDrag(),
    }),

    // Drag end handler
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      console.log('🎯 [useDragAndDrop] Drag ended:', {
        item,
        dropResult,
        didDrop: monitor.didDrop(),
      });

      // Handle successful drop if needed
      if (monitor.didDrop() && dropResult) {
        console.log('✅ [useDragAndDrop] Drop successful:', dropResult);
      }
    },

    // Determine if item can be dragged
    canDrag: () => {
      // Add any custom logic for when dragging should be disabled
      return !!(data?.id && data?.type);
    },
  });

  // Combine refs for drag functionality
  // This ensures the drag functionality is properly attached to the DOM element
  useEffect(() => {
    if (dragRef.current) {
      drag(dragRef.current);
    }
  }, [drag]);

  /**
   * Gets drag status information for debugging and UI feedback
   *
   * @returns {Object} Comprehensive drag status
   */
  const getDragStatus = () => ({
    isDragging,
    canDrag,
    hasValidData: !!(data?.id && data?.type),
    dragItemId: dragItem.id,
    dragItemType: dragItem.type,
    path,
  });

  /**
   * Manually triggers drag preview update
   * Useful for custom drag previews or when drag item changes
   */
  const updateDragPreview = (previewElement) => {
    if (dragPreview && previewElement) {
      dragPreview(previewElement);
    }
  };

  /**
   * Checks if the component is currently being dragged
   *
   * @returns {boolean} True if component is being dragged
   */
  const isCurrentlyDragging = () => isDragging;

  /**
   * Checks if the component can be dragged
   *
   * @returns {boolean} True if component can be dragged
   */
  const isCurrentlyDraggable = () => canDrag;

  /**
   * Gets the current drag item data
   *
   * @returns {Object} Current drag item
   */
  const getCurrentDragItem = () => dragItem;

  /**
   * Validates drag data integrity
   *
   * @returns {Object} Validation result
   */
  const validateDragData = () => {
    const errors = [];

    if (!data) {
      errors.push('No component data provided');
    }

    if (!data?.id) {
      errors.push('Component data missing required id');
    }

    if (!data?.type) {
      errors.push('Component data missing required type');
    }

    if (!path) {
      errors.push('No path provided for component');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  /**
   * Performance monitoring for drag operations
   *
   * @returns {Object} Performance metrics
   */
  const getPerformanceMetrics = () => ({
    dragItemCreationTime: dragItem.timestamp,
    isDragging,
    canDrag,
    hasRef: !!dragRef.current,
    dragItemSize: JSON.stringify(dragItem).length,
  });

  return {
    // Core drag functionality
    dragRef,
    isDragging,
    canDrag,
    dragItem,
    dragPreview,

    // Utility functions
    getDragStatus,
    updateDragPreview,
    isCurrentlyDragging,
    isCurrentlyDraggable,
    getCurrentDragItem,
    validateDragData,
    getPerformanceMetrics,

    // State helpers for conditional rendering
    dragClassName: isDragging ? 'dragging' : '',
    dragStyle: isDragging
      ? { opacity: 0.8, transform: 'rotate(2deg) scale(1.02)' }
      : {},

    // Accessibility helpers
    dragAriaLabel: `Draggable ${data?.type || 'component'} ${
      data?.label || data?.id || ''
    }`,
    dragRole: 'button',
    dragTabIndex: canDrag ? 0 : -1,
  };
};

/**
 * Hook for drag preview customization
 *
 * Provides utilities for creating custom drag previews with better visual feedback.
 *
 * @param {Object} options - Preview options
 * @returns {Object} Preview utilities
 */
export const useDragPreview = ({ component, isDragging }) => {
  /**
   * Creates a custom drag preview element
   *
   * @param {HTMLElement} element - Element to use as preview
   * @returns {HTMLElement} Customized preview element
   */
  const createCustomPreview = (element) => {
    if (!element) return null;

    const preview = element.cloneNode(true);
    preview.style.transform = 'rotate(2deg) scale(1.02)';
    preview.style.opacity = '0.8';
    preview.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.16)';
    preview.style.zIndex = '1000';

    return preview;
  };

  /**
   * Gets preview styles for the dragged element
   *
   * @returns {Object} CSS styles for preview
   */
  const getPreviewStyles = () => ({
    transform: isDragging ? 'rotate(2deg) scale(1.02)' : 'none',
    opacity: isDragging ? 0.8 : 1,
    boxShadow: isDragging ? '0 8px 32px rgba(0, 0, 0, 0.16)' : 'none',
    zIndex: isDragging ? 1000 : 'auto',
    transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  });

  return {
    createCustomPreview,
    getPreviewStyles,
  };
};
