/**
 * Property Processing Utilities
 * 
 * Utility functions for processing, transforming, and validating component props.
 * These functions handle the complex logic of converting component configurations
 * into proper Ant Design component props.
 */

/**
 * Processes styling props for Ant Design components
 * 
 * @param {Object} styling - Styling configuration object
 * @returns {Object} Processed styling props
 */
export const processStylingProps = (styling = {}) => {
  const processed = {};
  
  // Size mapping
  if (styling.size) {
    processed.size = styling.size;
  }
  
  // Disabled state
  if (styling.disabled !== undefined) {
    processed.disabled = styling.disabled;
  }
  
  // Loading state
  if (styling.loading !== undefined) {
    processed.loading = styling.loading;
  }
  
  // Style object
  if (styling.style) {
    processed.style = {
      width: '100%',
      ...styling.style,
    };
  } else {
    processed.style = { width: '100%' };
  }
  
  // Placeholder
  if (styling.placeholder) {
    processed.placeholder = styling.placeholder;
  }
  
  return processed;
};

/**
 * Processes validation props for form components
 * 
 * @param {Object} validation - Validation configuration object
 * @param {string} fieldLabel - Label of the field for error messages
 * @returns {Array} Array of Ant Design validation rules
 */
export const processValidationProps = (validation = {}, fieldLabel = 'Field') => {
  const rules = [];
  
  // Required validation
  if (validation.required) {
    rules.push({
      required: true,
      message: validation.message || `${fieldLabel} is required`,
    });
  }
  
  // Pattern validation
  if (validation.pattern) {
    rules.push({
      pattern: new RegExp(validation.pattern),
      message: validation.patternMessage || 'Invalid format',
    });
  }
  
  // Length validations
  if (validation.min !== undefined) {
    rules.push({
      min: validation.min,
      message: validation.minMessage || `Minimum length is ${validation.min}`,
    });
  }
  
  if (validation.max !== undefined) {
    rules.push({
      max: validation.max,
      message: validation.maxMessage || `Maximum length is ${validation.max}`,
    });
  }
  
  // Value range validations
  if (validation.minValue !== undefined) {
    rules.push({
      type: 'number',
      min: validation.minValue,
      message: validation.minValueMessage || `Minimum value is ${validation.minValue}`,
    });
  }
  
  if (validation.maxValue !== undefined) {
    rules.push({
      type: 'number',
      max: validation.maxValue,
      message: validation.maxValueMessage || `Maximum value is ${validation.maxValue}`,
    });
  }
  
  // Type validations
  if (validation.email) {
    rules.push({
      type: 'email',
      message: validation.emailMessage || 'Please enter a valid email address',
    });
  }
  
  if (validation.url) {
    rules.push({
      type: 'url',
      message: validation.urlMessage || 'Please enter a valid URL',
    });
  }
  
  // Custom validator
  if (validation.validator && typeof validation.validator === 'function') {
    rules.push({
      validator: validation.validator,
    });
  }
  
  return rules;
};

/**
 * Processes options for select-type components
 * 
 * @param {Array} options - Array of option objects
 * @returns {Array} Processed options array
 */
export const processOptionsProps = (options = []) => {
  return options.map(option => {
    if (typeof option === 'string') {
      return {
        label: option,
        value: option,
      };
    }
    
    return {
      label: option.label || option.text || option.value,
      value: option.value,
      disabled: option.disabled || false,
      ...option,
    };
  });
};

/**
 * Processes container props for layout components
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Processed container props
 */
export const processContainerProps = (component) => {
  const props = {};
  
  // Tab container props
  if (component.type === 'TAB_CONTAINER') {
    props.type = component.styling?.type || 'line';
    props.size = component.styling?.size || 'default';
    props.tabPosition = component.styling?.tabPosition || 'top';
    
    if (component.tabs) {
      props.items = component.tabs.map(tab => ({
        key: tab.key || tab.id,
        label: tab.label || tab.title,
        children: tab.children,
      }));
    }
  }
  
  // Card container props
  if (component.type === 'CARD_CONTAINER') {
    const cardProps = component.cardProps || {};
    props.title = cardProps.title;
    props.extra = cardProps.extra;
    props.hoverable = component.styling?.hoverable;
    props.size = component.styling?.size || 'default';
    props.bordered = component.styling?.bordered !== false;
  }
  
  // Steps container props
  if (component.type === 'STEPS_CONTAINER') {
    const stepsProps = component.stepsProps || {};
    props.current = stepsProps.current || 0;
    props.direction = stepsProps.direction || 'horizontal';
    props.size = stepsProps.size || 'default';
    
    if (component.steps) {
      props.items = component.steps.map(step => ({
        key: step.key || step.id,
        title: step.title,
        description: step.description,
      }));
    }
  }
  
  return props;
};

/**
 * Processes data props for data display components
 * 
 * @param {Object} component - Component configuration object
 * @returns {Object} Processed data props
 */
export const processDataProps = (component) => {
  const props = {};
  
  // Table props
  if (component.type === 'table') {
    props.columns = component.columns || [];
    props.dataSource = component.dataSource || [];
    props.bordered = component.styling?.bordered;
    props.size = component.styling?.size;
    props.pagination = component.styling?.pagination;
    props.scroll = component.styling?.scroll;
  }
  
  // List props
  if (component.type === 'list') {
    props.dataSource = component.dataSource || [];
    props.header = component.header;
    props.footer = component.footer;
    props.bordered = component.styling?.bordered;
    props.split = component.styling?.split;
    props.size = component.styling?.size;
    props.itemLayout = component.styling?.itemLayout;
  }
  
  // Tree props
  if (component.type === 'tree') {
    props.treeData = component.treeData || [];
    props.showLine = component.styling?.showLine;
    props.showIcon = component.styling?.showIcon;
    props.checkable = component.styling?.checkable;
    props.selectable = component.styling?.selectable;
    props.multiple = component.styling?.multiple;
  }
  
  return props;
};

/**
 * Processes event handlers for components
 * 
 * @param {Object} component - Component configuration object
 * @param {Function} onChange - Default change handler
 * @returns {Object} Processed event handlers
 */
export const processEventHandlers = (component, onChange) => {
  const handlers = {};
  
  // Standard change handler
  if (onChange) {
    handlers.onChange = onChange;
  }
  
  // Component-specific event handlers
  if (component.events) {
    if (component.events.onFocus) {
      handlers.onFocus = component.events.onFocus;
    }
    
    if (component.events.onBlur) {
      handlers.onBlur = component.events.onBlur;
    }
    
    if (component.events.onClick) {
      handlers.onClick = component.events.onClick;
    }
    
    if (component.events.onSelect) {
      handlers.onSelect = component.events.onSelect;
    }
  }
  
  return handlers;
};

/**
 * Merges all processed props into final component props
 * 
 * @param {Object} component - Component configuration object
 * @param {Object} commonProps - Common props for all components
 * @param {Function} onChange - Change handler function
 * @returns {Object} Final merged props object
 */
export const mergeFinalProps = (component, commonProps = {}, onChange) => {
  const stylingProps = processStylingProps(component.styling);
  const eventHandlers = processEventHandlers(component, onChange);
  const containerProps = processContainerProps(component);
  const dataProps = processDataProps(component);
  
  return {
    ...commonProps,
    ...stylingProps,
    ...eventHandlers,
    ...containerProps,
    ...dataProps,
    ...component.componentProps, // User-provided props override everything
  };
};
