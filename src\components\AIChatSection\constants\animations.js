/**
 * AIChatSection Animation Constants
 * 
 * This module contains all Framer Motion animation variants, global CSS animations,
 * and animation utilities used in the AIChatSection component.
 * 
 * Features:
 * - Framer Motion variants for smooth message animations
 * - Global CSS keyframes for pulse and other effects
 * - Enterprise-grade animation timing and easing
 * - Consistent animation patterns across the chat interface
 * 
 * Animation Philosophy:
 * - Subtle and professional animations that enhance UX
 * - Consistent timing and easing curves
 * - Performance-optimized with proper GPU acceleration
 * - Accessibility-friendly with reduced motion support
 */

/**
 * Global CSS animations injected into the document head
 * Used for simple pulse effects and other global animations
 */
export const GlobalAnimations = `
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }
`;

/**
 * Injects global animations into the document head
 * Only runs in browser environment to avoid SSR issues
 */
export const injectGlobalAnimations = () => {
  if (typeof document !== 'undefined') {
    // Check if animations are already injected to avoid duplicates
    if (!document.querySelector('#ai-chat-animations')) {
      const style = document.createElement('style');
      style.id = 'ai-chat-animations';
      style.textContent = GlobalAnimations;
      document.head.appendChild(style);
    }
  }
};

/**
 * Framer Motion animation variants for message bubbles
 * Provides smooth enter/exit animations with professional timing
 */
export const messageVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1], // Custom cubic-bezier for smooth motion
    },
  },
  exit: {
    opacity: 0,
    y: -10,
    scale: 0.95,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1],
    },
  },
};

/**
 * Container animation variants for staggered children animations
 * Creates a wave effect when multiple messages appear
 */
export const containerVariants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1, // Delay between child animations
      delayChildren: 0.1,   // Initial delay before children start
    },
  },
};

/**
 * Header animation variants for smooth entrance
 * Used for the chat header with connection status
 */
export const headerVariants = {
  initial: { 
    opacity: 0, 
    y: -20 
  },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.4,
      ease: [0.4, 0, 0.2, 1]
    }
  }
};

/**
 * Processing indicator animation variants
 * Used during AI form generation with smooth scaling
 */
export const processingVariants = {
  initial: { 
    scale: 0.95, 
    opacity: 0 
  },
  animate: { 
    scale: 1, 
    opacity: 1,
    transition: {
      duration: 0.2,
      delay: 0.1
    }
  }
};

/**
 * Loading message animation variants
 * Used for the temporary loading message bubble
 */
export const loadingMessageVariants = {
  initial: { 
    opacity: 0, 
    y: 20 
  },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1]
    }
  },
  exit: { 
    opacity: 0, 
    y: -10,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1]
    }
  }
};

/**
 * Message content animation variants
 * Used for individual message content with subtle scaling
 */
export const messageContentVariants = {
  initial: { 
    opacity: 0, 
    scale: 0.95 
  },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: {
      duration: 0.2,
      delay: 0.1,
      ease: [0.4, 0, 0.2, 1]
    }
  }
};

/**
 * Standard easing curves used throughout the chat interface
 * Based on Material Design and modern UI principles
 */
export const easingCurves = {
  // Standard easing for most animations
  standard: [0.4, 0, 0.2, 1],
  
  // Decelerated easing for entrance animations
  decelerate: [0, 0, 0.2, 1],
  
  // Accelerated easing for exit animations
  accelerate: [0.4, 0, 1, 1],
  
  // Sharp easing for quick transitions
  sharp: [0.4, 0, 0.6, 1],
};

/**
 * Animation duration constants
 * Consistent timing across all chat animations
 */
export const animationDurations = {
  // Quick micro-interactions
  fast: 0.15,
  
  // Standard UI animations
  normal: 0.3,
  
  // Slower, more deliberate animations
  slow: 0.5,
  
  // Very slow for complex state changes
  verySlow: 0.8,
};

/**
 * Spring animation configurations
 * For more natural, physics-based animations
 */
export const springConfigs = {
  // Gentle spring for subtle animations
  gentle: {
    type: "spring",
    stiffness: 120,
    damping: 14,
  },
  
  // Bouncy spring for playful interactions
  bouncy: {
    type: "spring",
    stiffness: 200,
    damping: 10,
  },
  
  // Stiff spring for quick, responsive animations
  stiff: {
    type: "spring",
    stiffness: 300,
    damping: 20,
  },
};
