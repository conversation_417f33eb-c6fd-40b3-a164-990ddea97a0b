/**
 * ComponentContent Component
 * 
 * Renders the actual form component content with proper Form.Item wrapper
 * when needed. Handles the integration between the component renderer
 * and the form system.
 * 
 * Features:
 * - Automatic Form.Item wrapper for form components
 * - Integration with component renderer utilities
 * - Proper styling and spacing for canvas display
 * - Support for all component types
 * - Inline editing integration
 * 
 * @module ComponentContent
 */

import React, { memo } from 'react';
import { Form } from 'antd';

// Import utilities
import { renderFormComponent } from '../utils/componentRenderer';
import { shouldHaveFormLabel, getCanvasFormItemProps } from '../utils/componentUtils';

// Import styled components
import { ComponentContent as StyledComponentContent } from '../styles/StyledComponents';

/**
 * ComponentContent Component
 * 
 * Renders the main content of a form component with appropriate wrappers
 * and styling. Determines whether to use Form.Item wrapper based on
 * component type.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.component - Component data object
 * @param {Function} props.renderEditableText - Function to render editable text
 * @returns {JSX.Element} Rendered component content
 */
const ComponentContent = memo(({ component, renderEditableText }) => {
  // Safety check for component data
  if (!component) {
    return (
      <StyledComponentContent>
        <div
          style={{
            color: '#999',
            fontSize: '12px',
            textAlign: 'center',
            padding: '16px',
          }}
        >
          No component data available
        </div>
      </StyledComponentContent>
    );
  }

  // Get the rendered component from the renderer utilities
  const renderedComponent = renderFormComponent(component, renderEditableText);

  // Check if this component should have a Form.Item wrapper
  const needsFormWrapper = shouldHaveFormLabel(component.type);

  // If component needs Form.Item wrapper, wrap it
  if (needsFormWrapper) {
    const formItemProps = getCanvasFormItemProps(component, renderEditableText);
    
    return (
      <StyledComponentContent>
        <Form layout='vertical' size='small'>
          <Form.Item {...formItemProps}>
            {renderedComponent}
          </Form.Item>
        </Form>
      </StyledComponentContent>
    );
  }

  // Return component without Form.Item wrapper for non-form components
  return (
    <StyledComponentContent>
      {renderedComponent}
    </StyledComponentContent>
  );
});

// Set display name for debugging
ComponentContent.displayName = 'ComponentContent';

export default ComponentContent;
