/**
 * Message Utilities
 * 
 * Utility functions for handling form messages, notifications, and user feedback.
 * Provides consistent messaging across the FormRenderer module.
 */

import { message } from 'antd';

/**
 * Message types enum for consistent messaging
 */
export const MESSAGE_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  LOADING: 'loading',
};

/**
 * Default form messages configuration
 */
export const DEFAULT_MESSAGES = {
  SUBMIT_SUCCESS: 'Form submitted successfully!',
  SUBMIT_ERROR: 'Failed to submit form. Please try again.',
  VALIDATION_ERROR: 'Please check the form and fix any errors',
  RESET_SUCCESS: 'Form has been reset',
  LOADING: 'Processing...',
  REQUIRED_FIELD: 'This field is required',
  INVALID_FORMAT: 'Invalid format',
};

/**
 * Shows a success message to the user
 * 
 * @param {string} content - Message content
 * @param {number} duration - Duration in seconds (default: 3)
 */
export const showSuccessMessage = (content = DEFAULT_MESSAGES.SUBMIT_SUCCESS, duration = 3) => {
  message.success(content, duration);
};

/**
 * Shows an error message to the user
 * 
 * @param {string} content - Message content
 * @param {number} duration - Duration in seconds (default: 5)
 */
export const showErrorMessage = (content = DEFAULT_MESSAGES.SUBMIT_ERROR, duration = 5) => {
  message.error(content, duration);
};

/**
 * Shows a warning message to the user
 * 
 * @param {string} content - Message content
 * @param {number} duration - Duration in seconds (default: 4)
 */
export const showWarningMessage = (content, duration = 4) => {
  message.warning(content, duration);
};

/**
 * Shows an info message to the user
 * 
 * @param {string} content - Message content
 * @param {number} duration - Duration in seconds (default: 3)
 */
export const showInfoMessage = (content, duration = 3) => {
  message.info(content, duration);
};

/**
 * Shows a loading message and returns a function to hide it
 * 
 * @param {string} content - Loading message content
 * @returns {Function} Function to hide the loading message
 */
export const showLoadingMessage = (content = DEFAULT_MESSAGES.LOADING) => {
  return message.loading(content, 0); // 0 means don't auto-hide
};

/**
 * Generates a validation error message for a specific field
 * 
 * @param {string} fieldName - Name of the field
 * @param {string} validationType - Type of validation that failed
 * @param {*} value - The invalid value (optional)
 * @returns {string} Formatted error message
 */
export const generateValidationMessage = (fieldName, validationType, value) => {
  const fieldLabel = fieldName || 'Field';
  
  switch (validationType) {
    case 'required':
      return `${fieldLabel} is required`;
    case 'email':
      return 'Please enter a valid email address';
    case 'url':
      return 'Please enter a valid URL';
    case 'number':
      return 'Please enter a valid number';
    case 'min':
      return `${fieldLabel} is too short`;
    case 'max':
      return `${fieldLabel} is too long`;
    case 'pattern':
      return `${fieldLabel} format is invalid`;
    default:
      return `${fieldLabel} is invalid`;
  }
};

/**
 * Handles form submission messages based on result
 * 
 * @param {boolean} success - Whether submission was successful
 * @param {string} successMessage - Custom success message
 * @param {string} errorMessage - Custom error message
 */
export const handleSubmissionMessage = (
  success,
  successMessage = DEFAULT_MESSAGES.SUBMIT_SUCCESS,
  errorMessage = DEFAULT_MESSAGES.SUBMIT_ERROR
) => {
  if (success) {
    showSuccessMessage(successMessage);
  } else {
    showErrorMessage(errorMessage);
  }
};
