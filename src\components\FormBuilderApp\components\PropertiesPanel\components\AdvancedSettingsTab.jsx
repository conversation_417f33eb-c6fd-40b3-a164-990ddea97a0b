/**
 * @fileoverview Advanced Settings Tab component for technical configuration
 *
 * This component renders the Advanced Settings tab content with technical
 * configuration options including CSS classes, custom attributes, and conditional logic.
 *
 * @module AdvancedSettingsTab
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useCallback, useState } from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Tag,
  Divider,
  Typography,
  Collapse,
  Alert,
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  CodeOutlined,
  SettingOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;

/**
 * Advanced Settings Tab component
 *
 * Renders technical configuration forms for advanced settings including
 * CSS classes, custom attributes, conditional logic, and developer options.
 *
 * @param {Object} props - Component props
 * @param {Object} props.componentData - Current component data
 * @param {string} props.componentId - Component ID
 * @param {Function} props.onPropertyUpdate - Property update callback
 * @param {Object} props.formSchema - Current form schema for context
 *
 * @returns {JSX.Element} Advanced settings tab content
 */
const AdvancedSettingsTab = ({
  componentData,
  componentId,
  onPropertyUpdate,
  formSchema,
}) => {
  const [form] = Form.useForm();
  const [customAttributes, setCustomAttributes] = useState(
    componentData?.customAttributes || [],
  );

  // Handle form field changes
  const handleFieldChange = useCallback(
    (field, value) => {
      onPropertyUpdate(field, value, { immediate: false });
    },
    [onPropertyUpdate],
  );

  // Handle form field blur (immediate update)
  const handleFieldBlur = useCallback(
    (field, value) => {
      onPropertyUpdate(field, value, { immediate: true });
    },
    [onPropertyUpdate],
  );

  // Handle custom attribute addition
  const handleAddAttribute = useCallback(() => {
    const newAttribute = { key: '', value: '', id: Date.now() };
    const updatedAttributes = [...customAttributes, newAttribute];
    setCustomAttributes(updatedAttributes);
    handleFieldChange('customAttributes', updatedAttributes);
  }, [customAttributes, handleFieldChange]);

  // Handle custom attribute removal
  const handleRemoveAttribute = useCallback(
    (id) => {
      const updatedAttributes = customAttributes.filter(
        (attr) => attr.id !== id,
      );
      setCustomAttributes(updatedAttributes);
      handleFieldChange('customAttributes', updatedAttributes);
      handleFieldBlur('customAttributes', updatedAttributes);
    },
    [customAttributes, handleFieldChange, handleFieldBlur],
  );

  // Handle custom attribute update
  const handleAttributeUpdate = useCallback(
    (id, field, value) => {
      const updatedAttributes = customAttributes.map((attr) =>
        attr.id === id ? { ...attr, [field]: value } : attr,
      );
      setCustomAttributes(updatedAttributes);
      handleFieldChange('customAttributes', updatedAttributes);
    },
    [customAttributes, handleFieldChange],
  );

  if (!componentData) {
    return (
      <div
        style={{ textAlign: 'center', padding: '40px 20px', color: '#8c8c8c' }}
      >
        <SettingOutlined style={{ fontSize: '24px', marginBottom: '12px' }} />
        <div>No component selected</div>
      </div>
    );
  }

  return (
    <div>
      {/* Advanced Settings Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={5} style={{ margin: 0, color: '#262626' }}>
          Advanced Settings
        </Title>
        <Text type='secondary' style={{ fontSize: '12px' }}>
          Technical configuration and developer options
        </Text>
      </div>

      <Form
        form={form}
        layout='vertical'
        size='small'
        initialValues={{
          className: componentData.className || '',
          style: componentData.style
            ? JSON.stringify(componentData.style, null, 2)
            : '',
          hidden: componentData.hidden || false,
          readonly: componentData.readonly || false,
        }}
      >
        {/* CSS and Styling */}
        <Collapse defaultActiveKey={['styling']} ghost>
          <Panel header='CSS & Styling' key='styling'>
            <Space direction='vertical' style={{ width: '100%' }}>
              <Form.Item
                label='CSS Classes'
                name='className'
                tooltip='Additional CSS classes to apply to this component'
              >
                <Input
                  placeholder='Enter CSS class names (space-separated)'
                  onChange={(e) =>
                    handleFieldChange('className', e.target.value)
                  }
                  onBlur={(e) => handleFieldBlur('className', e.target.value)}
                />
              </Form.Item>

              <Form.Item
                label='Inline Styles'
                name='style'
                tooltip='Custom CSS styles in JSON format'
              >
                <TextArea
                  rows={4}
                  placeholder='{"color": "#000", "fontSize": "14px", "margin": "10px"}'
                  onChange={(e) => handleFieldChange('style', e.target.value)}
                  onBlur={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value || '{}');
                      handleFieldBlur('style', parsed);
                    } catch (error) {
                      console.warn('Invalid JSON in style field');
                    }
                  }}
                />
              </Form.Item>

              <Form.Item
                label='CSS Variables'
                tooltip='Custom CSS variables for this component'
              >
                <TextArea
                  rows={3}
                  placeholder='{"--primary-color": "#1890ff", "--border-radius": "6px"}'
                  defaultValue={
                    componentData.cssVariables
                      ? JSON.stringify(componentData.cssVariables, null, 2)
                      : ''
                  }
                  onChange={(e) =>
                    handleFieldChange('cssVariables', e.target.value)
                  }
                  onBlur={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value || '{}');
                      handleFieldBlur('cssVariables', parsed);
                    } catch (error) {
                      console.warn('Invalid JSON in CSS variables field');
                    }
                  }}
                />
              </Form.Item>

              <Form.Item
                label='Animation Class'
                tooltip='CSS animation class to apply to this component'
              >
                <Select
                  placeholder='Select animation'
                  allowClear
                  defaultValue={componentData.animationClass || undefined}
                  onChange={(value) =>
                    handleFieldChange('animationClass', value)
                  }
                  onBlur={() =>
                    handleFieldBlur(
                      'animationClass',
                      form.getFieldValue('animationClass'),
                    )
                  }
                >
                  <Option value='fade-in'>Fade In</Option>
                  <Option value='slide-in-left'>Slide In Left</Option>
                  <Option value='slide-in-right'>Slide In Right</Option>
                  <Option value='slide-in-up'>Slide In Up</Option>
                  <Option value='slide-in-down'>Slide In Down</Option>
                  <Option value='bounce-in'>Bounce In</Option>
                  <Option value='zoom-in'>Zoom In</Option>
                </Select>
              </Form.Item>
            </Space>
          </Panel>

          {/* Custom Attributes */}
          <Panel header='Custom Attributes' key='attributes'>
            <div style={{ marginBottom: '16px' }}>
              <Text type='secondary' style={{ fontSize: '12px' }}>
                Add custom HTML attributes to the component
              </Text>
            </div>

            <Space direction='vertical' style={{ width: '100%' }}>
              {customAttributes.map((attr) => (
                <div
                  key={attr.id}
                  style={{ display: 'flex', gap: '8px', alignItems: 'center' }}
                >
                  <Input
                    placeholder='Attribute name'
                    value={attr.key}
                    onChange={(e) =>
                      handleAttributeUpdate(attr.id, 'key', e.target.value)
                    }
                    onBlur={(e) =>
                      handleFieldBlur('customAttributes', customAttributes)
                    }
                    style={{ flex: 1 }}
                  />
                  <Input
                    placeholder='Attribute value'
                    value={attr.value}
                    onChange={(e) =>
                      handleAttributeUpdate(attr.id, 'value', e.target.value)
                    }
                    onBlur={(e) =>
                      handleFieldBlur('customAttributes', customAttributes)
                    }
                    style={{ flex: 1 }}
                  />
                  <Button
                    type='text'
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveAttribute(attr.id)}
                    size='small'
                  />
                </div>
              ))}

              <Button
                type='dashed'
                icon={<PlusOutlined />}
                onClick={handleAddAttribute}
                style={{ width: '100%' }}
                size='small'
              >
                Add Custom Attribute
              </Button>
            </Space>
          </Panel>

          {/* Behavior Settings */}
          <Panel header='Behavior Settings' key='behavior'>
            <Space direction='vertical' style={{ width: '100%' }}>
              <Form.Item
                label='Hidden'
                name='hidden'
                valuePropName='checked'
                tooltip='Hide this component from view'
              >
                <Switch
                  onChange={(checked) => {
                    handleFieldChange('hidden', checked);
                    handleFieldBlur('hidden', checked);
                  }}
                />
              </Form.Item>

              <Form.Item
                label='Read Only'
                name='readonly'
                valuePropName='checked'
                tooltip='Make this component read-only (display only)'
              >
                <Switch
                  onChange={(checked) => {
                    handleFieldChange('readonly', checked);
                    handleFieldBlur('readonly', checked);
                  }}
                />
              </Form.Item>

              <Form.Item
                label='Tab Index'
                tooltip='Control tab order for keyboard navigation'
              >
                <Input
                  type='number'
                  placeholder='Tab index (e.g., 1, 2, 3...)'
                  defaultValue={componentData.tabIndex || ''}
                  onChange={(e) =>
                    handleFieldChange(
                      'tabIndex',
                      parseInt(e.target.value) || undefined,
                    )
                  }
                  onBlur={(e) =>
                    handleFieldBlur(
                      'tabIndex',
                      parseInt(e.target.value) || undefined,
                    )
                  }
                />
              </Form.Item>
            </Space>
          </Panel>

          {/* Conditional Logic */}
          <Panel header='Conditional Logic' key='conditional'>
            <Space direction='vertical' style={{ width: '100%' }}>
              <Form.Item
                label='Visibility Condition'
                tooltip='JavaScript expression to control component visibility'
              >
                <TextArea
                  rows={3}
                  placeholder="e.g., formData.userType === 'admin' || formData.level > 5"
                  defaultValue={componentData.conditionalLogic?.showWhen || ''}
                  onChange={(e) =>
                    handleFieldChange(
                      'conditionalLogic.showWhen',
                      e.target.value,
                    )
                  }
                  onBlur={(e) =>
                    handleFieldBlur('conditionalLogic.showWhen', e.target.value)
                  }
                />
              </Form.Item>

              <Form.Item
                label='Enable Condition'
                tooltip='JavaScript expression to control when component is enabled'
              >
                <TextArea
                  rows={3}
                  placeholder='e.g., formData.agreement === true'
                  defaultValue={
                    componentData.conditionalLogic?.enableWhen || ''
                  }
                  onChange={(e) =>
                    handleFieldChange(
                      'conditionalLogic.enableWhen',
                      e.target.value,
                    )
                  }
                  onBlur={(e) =>
                    handleFieldBlur(
                      'conditionalLogic.enableWhen',
                      e.target.value,
                    )
                  }
                />
              </Form.Item>

              <Form.Item
                label='Required Condition'
                tooltip='JavaScript expression to control when component is required'
              >
                <TextArea
                  rows={3}
                  placeholder="e.g., formData.contactMethod === 'email'"
                  defaultValue={
                    componentData.conditionalLogic?.requiredWhen || ''
                  }
                  onChange={(e) =>
                    handleFieldChange(
                      'conditionalLogic.requiredWhen',
                      e.target.value,
                    )
                  }
                  onBlur={(e) =>
                    handleFieldBlur(
                      'conditionalLogic.requiredWhen',
                      e.target.value,
                    )
                  }
                />
              </Form.Item>

              <Alert
                message='Conditional Logic Syntax'
                description="Use 'formData.fieldName' to reference other form fields. Supported operators: ===, !==, >, <, >=, <=, &&, ||, !"
                type='info'
                showIcon
                style={{ fontSize: '12px' }}
              />
            </Space>
          </Panel>

          {/* Developer Options */}
          <Panel header='Developer Options' key='developer'>
            <Space direction='vertical' style={{ width: '100%' }}>
              <Form.Item
                label='Component ID'
                tooltip='Unique identifier for this component'
              >
                <Input
                  value={componentId}
                  disabled
                  addonBefore={<CodeOutlined />}
                />
              </Form.Item>

              <Form.Item
                label='Component Type'
                tooltip='Type of this component'
              >
                <Input
                  value={componentData.type}
                  disabled
                  addonBefore={<CodeOutlined />}
                />
              </Form.Item>

              <Form.Item
                label='Data Attributes'
                tooltip='Custom data-* attributes for testing and analytics'
              >
                <TextArea
                  rows={3}
                  placeholder='{"data-testid": "my-component", "data-analytics": "form-field"}'
                  defaultValue={
                    componentData.dataAttributes
                      ? JSON.stringify(componentData.dataAttributes, null, 2)
                      : ''
                  }
                  onChange={(e) =>
                    handleFieldChange('dataAttributes', e.target.value)
                  }
                  onBlur={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value || '{}');
                      handleFieldBlur('dataAttributes', parsed);
                    } catch (error) {
                      console.warn('Invalid JSON in data attributes field');
                    }
                  }}
                />
              </Form.Item>

              <Form.Item
                label='Event Handlers'
                tooltip='Custom event handlers in JSON format'
              >
                <TextArea
                  rows={4}
                  placeholder='{"onClick": "handleClick", "onFocus": "handleFocus"}'
                  defaultValue={
                    componentData.eventHandlers
                      ? JSON.stringify(componentData.eventHandlers, null, 2)
                      : ''
                  }
                  onChange={(e) =>
                    handleFieldChange('eventHandlers', e.target.value)
                  }
                  onBlur={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value || '{}');
                      handleFieldBlur('eventHandlers', parsed);
                    } catch (error) {
                      console.warn('Invalid JSON in event handlers field');
                    }
                  }}
                />
              </Form.Item>

              <Form.Item
                label='Component Schema'
                tooltip='View the complete component schema (read-only)'
              >
                <TextArea
                  rows={6}
                  value={JSON.stringify(componentData, null, 2)}
                  disabled
                  style={{
                    background: '#f5f5f5',
                    fontSize: '11px',
                    fontFamily: 'monospace',
                  }}
                />
              </Form.Item>

              <Form.Item
                label='Debug Mode'
                tooltip='Enable debug logging for this component'
                valuePropName='checked'
              >
                <Switch
                  defaultChecked={componentData.debugMode || false}
                  onChange={(checked) => {
                    handleFieldChange('debugMode', checked);
                    handleFieldBlur('debugMode', checked);
                  }}
                />
              </Form.Item>
            </Space>
          </Panel>
        </Collapse>
      </Form>
    </div>
  );
};

export default AdvancedSettingsTab;
