/**
 * @fileoverview Property Group component
 *
 * This component provides a collapsible group container for organizing
 * related properties with progressive disclosure and visual hierarchy.
 *
 * @module PropertyGroup
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useState, useCallback } from 'react';
import { Collapse, Badge, Tooltip, Space } from 'antd';
import {
  RightOutlined,
  InfoCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

const { Panel } = Collapse;

/**
 * Styled components for property group
 */
const StyledPropertyGroup = styled.div`
  margin-bottom: 16px;

  .ant-collapse {
    background: transparent;
    border: none;

    .ant-collapse-item {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      margin-bottom: 8px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      .ant-collapse-header {
        padding: 12px 16px;
        background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
        border-bottom: 1px solid #f0f0f0;

        &:hover {
          background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
        }

        .ant-collapse-arrow {
          color: #666;
          font-size: 12px;
        }
      }

      .ant-collapse-content {
        border-top: none;
        background: #ffffff;

        .ant-collapse-content-box {
          padding: 16px;
        }
      }

      &.ant-collapse-item-active {
        .ant-collapse-header {
          background: linear-gradient(135deg, #e6f2ff 0%, #f0f8ff 100%);
          border-bottom-color: #d9d9d9;
        }
      }
    }
  }
`;

const GroupHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

const GroupTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #262626;
`;

const GroupMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

/**
 * Property Group component
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Group title
 * @param {string} props.description - Group description (optional)
 * @param {React.ReactNode} props.icon - Group icon (optional)
 * @param {React.ReactNode} props.children - Group content
 * @param {boolean} props.defaultExpanded - Whether group is expanded by default
 * @param {number} props.propertyCount - Number of properties in group (optional)
 * @param {boolean} props.hasChanges - Whether group has unsaved changes
 * @param {Function} props.onExpand - Expand/collapse callback
 * @param {string} props.level - Nesting level ('primary', 'secondary')
 * @returns {React.ReactNode} Property group JSX
 */
const PropertyGroup = ({
  title,
  description,
  icon = <SettingOutlined />,
  children,
  defaultExpanded = false,
  propertyCount,
  hasChanges = false,
  onExpand,
  level = 'primary',
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  /**
   * Handle expand/collapse
   */
  const handleExpand = useCallback(
    (keys) => {
      const expanded = keys.includes('content');
      setIsExpanded(expanded);

      if (onExpand) {
        onExpand(expanded);
      }
    },
    [onExpand],
  );

  /**
   * Render group header
   */
  const renderHeader = useCallback(
    () => (
      <GroupHeader>
        <GroupTitle>
          {icon && (
            <span style={{ color: '#1890ff', fontSize: '14px' }}>{icon}</span>
          )}
          <span>{title}</span>
          {description && (
            <Tooltip title={description}>
              <InfoCircleOutlined
                style={{
                  color: '#8c8c8c',
                  fontSize: '12px',
                  cursor: 'help',
                }}
              />
            </Tooltip>
          )}
        </GroupTitle>

        <GroupMeta>
          {propertyCount !== undefined && (
            <Badge
              count={propertyCount}
              size='small'
              style={{
                backgroundColor: '#f0f0f0',
                color: '#666',
                fontSize: '10px',
              }}
            />
          )}
          {hasChanges && (
            <Tooltip title='Has unsaved changes'>
              <div
                style={{
                  width: '6px',
                  height: '6px',
                  borderRadius: '50%',
                  backgroundColor: '#faad14',
                }}
              />
            </Tooltip>
          )}
        </GroupMeta>
      </GroupHeader>
    ),
    [title, description, icon, propertyCount, hasChanges],
  );

  return (
    <StyledPropertyGroup>
      <Collapse
        activeKey={isExpanded ? ['content'] : []}
        onChange={handleExpand}
        expandIcon={({ isActive }) => (
          <motion.div
            animate={{ rotate: isActive ? 90 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <RightOutlined />
          </motion.div>
        )}
        ghost
      >
        <Panel header={renderHeader()} key='content' showArrow={true}>
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2, ease: 'easeInOut' }}
              >
                {children}
              </motion.div>
            )}
          </AnimatePresence>
        </Panel>
      </Collapse>
    </StyledPropertyGroup>
  );
};

export default PropertyGroup;
