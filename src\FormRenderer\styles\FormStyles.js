/**
 * Form-specific Styling Utilities
 * 
 * CSS-in-JS utilities and theme configurations for form styling.
 * Provides consistent styling patterns across the FormRenderer.
 */

/**
 * Default form theme configuration
 */
export const formTheme = {
  colors: {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: '#262626',
    textSecondary: '#8c8c8c',
    border: '#d9d9d9',
    background: '#ffffff',
    backgroundLight: '#fafafa',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
  borderRadius: {
    sm: '4px',
    md: '6px',
    lg: '8px',
  },
  shadows: {
    sm: '0 1px 2px rgba(0, 0, 0, 0.03)',
    md: '0 1px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 4px 12px rgba(0, 0, 0, 0.15)',
  },
};

/**
 * Form layout styles
 */
export const formLayoutStyles = {
  vertical: {
    '.ant-form-item-label': {
      textAlign: 'left',
      paddingBottom: '4px',
    },
  },
  horizontal: {
    '.ant-form-item-label': {
      textAlign: 'right',
      paddingRight: '8px',
    },
  },
  inline: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '16px',
    '.ant-form-item': {
      marginBottom: '0',
      marginRight: '16px',
    },
  },
};

/**
 * Form size configurations
 */
export const formSizeStyles = {
  small: {
    '.ant-form-item': {
      marginBottom: '12px',
    },
    '.ant-form-item-label': {
      fontSize: '12px',
    },
    '.ant-input, .ant-select-selector': {
      fontSize: '12px',
      padding: '4px 8px',
    },
  },
  middle: {
    '.ant-form-item': {
      marginBottom: '16px',
    },
    '.ant-form-item-label': {
      fontSize: '14px',
    },
  },
  large: {
    '.ant-form-item': {
      marginBottom: '20px',
    },
    '.ant-form-item-label': {
      fontSize: '16px',
      fontWeight: '500',
    },
    '.ant-input, .ant-select-selector': {
      fontSize: '16px',
      padding: '8px 12px',
    },
  },
};

/**
 * Form validation state styles
 */
export const validationStyles = {
  error: {
    borderColor: formTheme.colors.error,
    boxShadow: `0 0 0 2px rgba(255, 77, 79, 0.2)`,
  },
  warning: {
    borderColor: formTheme.colors.warning,
    boxShadow: `0 0 0 2px rgba(250, 173, 20, 0.2)`,
  },
  success: {
    borderColor: formTheme.colors.success,
    boxShadow: `0 0 0 2px rgba(82, 196, 26, 0.2)`,
  },
};

/**
 * Responsive breakpoints for form styling
 */
export const breakpoints = {
  xs: '480px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1200px',
  xxl: '1600px',
};

/**
 * Media query helpers
 */
export const mediaQueries = {
  mobile: `@media (max-width: ${breakpoints.sm})`,
  tablet: `@media (min-width: ${breakpoints.sm}) and (max-width: ${breakpoints.lg})`,
  desktop: `@media (min-width: ${breakpoints.lg})`,
  largeDesktop: `@media (min-width: ${breakpoints.xl})`,
};
