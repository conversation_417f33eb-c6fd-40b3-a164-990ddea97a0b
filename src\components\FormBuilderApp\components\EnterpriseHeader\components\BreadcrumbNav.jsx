/**
 * @fileoverview Breadcrumb Navigation component
 *
 * This component provides breadcrumb navigation for complex forms
 * showing the current location within nested form structures.
 *
 * @module BreadcrumbNav
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useMemo, useCallback } from 'react';
import { Breadcrumb, Tooltip } from 'antd';
import { HomeOutlined, FolderOutlined, FileOutlined } from '@ant-design/icons';
import { BreadcrumbContainer } from '../styles/EnterpriseHeader.styles';

/**
 * Breadcrumb Navigation component
 *
 * @param {Object} props - Component props
 * @param {Array} props.layout - Current form layout
 * @param {string} props.selectedComponentPath - Currently selected component path
 * @param {Function} props.onNavigate - Navigation callback function
 * @returns {React.ReactNode} Breadcrumb navigation JSX
 */
const BreadcrumbNav = ({
  layout = [],
  selectedComponentPath = '',
  onNavigate,
}) => {
  /**
   * Generate breadcrumb items from component path
   */
  const breadcrumbItems = useMemo(() => {
    const items = [
      {
        key: 'root',
        title: (
          <Tooltip title="Form Root">
            <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <HomeOutlined />
              Form
            </span>
          </Tooltip>
        ),
        onClick: () => onNavigate?.('root'),
      },
    ];

    if (!selectedComponentPath) {
      return items;
    }

    // Parse the component path (e.g., "root.0.children.1.children.0")
    const pathParts = selectedComponentPath.split('.');
    let currentPath = '';
    
    for (let i = 0; i < pathParts.length; i += 2) {
      const index = pathParts[i + 1];
      if (index === undefined) continue;
      
      currentPath += currentPath ? `.${pathParts[i]}.${index}` : `${pathParts[i]}.${index}`;
      
      // Find the component at this path
      const component = findComponentByPath(layout, currentPath);
      if (component) {
        const isContainer = isContainerComponent(component.type);
        
        items.push({
          key: currentPath,
          title: (
            <Tooltip title={`${component.type} component`}>
              <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                {isContainer ? <FolderOutlined /> : <FileOutlined />}
                {component.name || component.type || `Item ${index}`}
              </span>
            </Tooltip>
          ),
          onClick: () => onNavigate?.(currentPath),
        });
      }
    }

    return items;
  }, [layout, selectedComponentPath, onNavigate]);

  /**
   * Find component by path in layout
   */
  const findComponentByPath = useCallback((items, path) => {
    const pathParts = path.split('.');
    let current = items;
    
    for (let i = 1; i < pathParts.length; i += 2) {
      const index = parseInt(pathParts[i]);
      if (!current[index]) return null;
      
      if (i === pathParts.length - 1) {
        return current[index];
      }
      
      current = current[index].children || [];
    }
    
    return null;
  }, []);

  /**
   * Check if component type is a container
   */
  const isContainerComponent = useCallback((type) => {
    const containerTypes = [
      'tabContainer',
      'cardContainer',
      'formSection',
      'accordionContainer',
      'stepsContainer',
      'row',
      'column',
    ];
    return containerTypes.includes(type);
  }, []);

  // Don't render if no meaningful breadcrumb
  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <BreadcrumbContainer>
      <Breadcrumb
        items={breadcrumbItems}
        separator="/"
      />
    </BreadcrumbContainer>
  );
};

export default BreadcrumbNav;
