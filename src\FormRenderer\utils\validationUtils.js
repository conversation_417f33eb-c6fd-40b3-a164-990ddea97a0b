/**
 * Validation Utilities
 * 
 * Utility functions for processing validation rules, generating validation messages,
 * and handling form validation logic.
 */

/**
 * Generates validation rules from component configuration
 * 
 * @param {Object} component - Component configuration object
 * @returns {Array} Array of Ant Design validation rules
 */
export const generateValidationRules = (component) => {
  if (!component || !component.validation) {
    return [];
  }

  const rules = [];
  const validation = component.validation;

  // Required field validation
  if (validation.required) {
    rules.push({
      required: true,
      message: validation.message || `${component.label || 'Field'} is required`,
    });
  }

  // Pattern validation (regex)
  if (validation.pattern) {
    rules.push({
      pattern: new RegExp(validation.pattern),
      message: validation.patternMessage || 'Invalid format',
    });
  }

  // Minimum length validation
  if (validation.min !== undefined) {
    rules.push({
      min: validation.min,
      message: `Minimum length is ${validation.min}`,
    });
  }

  // Maximum length validation
  if (validation.max !== undefined) {
    rules.push({
      max: validation.max,
      message: `Maximum length is ${validation.max}`,
    });
  }

  // Email validation
  if (validation.email) {
    rules.push({
      type: 'email',
      message: 'Please enter a valid email address',
    });
  }

  // URL validation
  if (validation.url) {
    rules.push({
      type: 'url',
      message: 'Please enter a valid URL',
    });
  }

  // Number validation
  if (validation.number) {
    rules.push({
      type: 'number',
      message: 'Please enter a valid number',
    });
  }

  // Custom validator function
  if (validation.validator && typeof validation.validator === 'function') {
    rules.push({
      validator: validation.validator,
    });
  }

  return rules;
};

/**
 * Validates a single field value against its rules
 * 
 * @param {*} value - Field value to validate
 * @param {Array} rules - Validation rules array
 * @returns {Object} Validation result with isValid flag and error message
 */
export const validateFieldValue = (value, rules = []) => {
  for (const rule of rules) {
    // Required validation
    if (rule.required && (value === undefined || value === null || value === '')) {
      return {
        isValid: false,
        error: rule.message || 'This field is required',
      };
    }

    // Pattern validation
    if (rule.pattern && value && !rule.pattern.test(value)) {
      return {
        isValid: false,
        error: rule.message || 'Invalid format',
      };
    }

    // Length validations
    if (rule.min !== undefined && value && value.length < rule.min) {
      return {
        isValid: false,
        error: rule.message || `Minimum length is ${rule.min}`,
      };
    }

    if (rule.max !== undefined && value && value.length > rule.max) {
      return {
        isValid: false,
        error: rule.message || `Maximum length is ${rule.max}`,
      };
    }
  }

  return { isValid: true, error: null };
};

/**
 * Generates default validation messages for common validation types
 * 
 * @param {string} fieldLabel - Label of the field being validated
 * @param {string} validationType - Type of validation (required, email, etc.)
 * @returns {string} Default validation message
 */
export const getDefaultValidationMessage = (fieldLabel, validationType) => {
  const label = fieldLabel || 'Field';
  
  const messages = {
    required: `${label} is required`,
    email: 'Please enter a valid email address',
    url: 'Please enter a valid URL',
    number: 'Please enter a valid number',
    min: `${label} is too short`,
    max: `${label} is too long`,
    pattern: `${label} format is invalid`,
  };

  return messages[validationType] || `${label} is invalid`;
};
