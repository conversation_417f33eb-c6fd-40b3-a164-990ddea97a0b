/**
 * AI Form Generation Test Suite
 * Comprehensive tests for AI-powered form generation functionality
 */

import { generateFormSchema, testAIConnection } from '../services/aiService';
import { validateAIGeneratedSchema, enhanceAISchema } from '../utils/aiSchemaValidator';
import { detectComplexity, suggestComponents } from '../services/promptTemplates';

// Mock test prompts for different complexity levels
const TEST_PROMPTS = {
  SIMPLE: {
    prompt: "Create a simple contact form with name, email, phone, and message fields",
    expectedComponents: ['input', 'textarea'],
    expectedComplexity: 'SIMPLE'
  },
  
  INTERMEDIATE: {
    prompt: "Create a user registration form with personal details, account information, and preferences",
    expectedComponents: ['input', 'select', 'checkbox', 'datePicker'],
    expectedComplexity: 'INTERMEDIATE'
  },
  
  COMPLEX: {
    prompt: "Create a detailed employee application form with multiple sections for personal info, work history, and references",
    expectedComponents: ['input', 'textarea', 'select', 'datePicker', 'upload', 'tabContainer'],
    expectedComplexity: 'COMPLEX'
  },
  
  ENTERPRISE: {
    prompt: "Create a comprehensive 5-step supplier onboarding form with company information, contact details, financial information, compliance certifications, and final review",
    expectedComponents: ['stepsContainer', 'input', 'select', 'upload', 'checkbox', 'datePicker'],
    expectedComplexity: 'ENTERPRISE'
  }
};

// Test cases for specific form types
const SPECIFIC_FORM_TESTS = [
  {
    name: "Multi-step Wizard",
    prompt: "Create a 4-step product order form: 1) Product selection, 2) Customer details, 3) Payment info, 4) Review and confirm",
    expectedFeatures: ['stepsContainer', 'progress tracking', 'navigation buttons']
  },
  
  {
    name: "Nested Layout Form",
    prompt: "Create a complex form with tabs containing cards, where each card has accordion sections with form fields",
    expectedFeatures: ['tabContainer', 'cardContainer', 'accordionContainer', 'nested structure']
  },
  
  {
    name: "Dynamic Form with Conditions",
    prompt: "Create a survey form where questions change based on previous answers, with conditional logic",
    expectedFeatures: ['conditional logic', 'dynamic fields', 'validation rules']
  },
  
  {
    name: "File Upload Heavy Form",
    prompt: "Create a document submission form with multiple file upload sections for different document types",
    expectedFeatures: ['upload', 'file validation', 'multiple upload areas']
  },
  
  {
    name: "Data Collection Form",
    prompt: "Create a comprehensive data collection form with all types of inputs: text, numbers, dates, selections, ratings, and toggles",
    expectedFeatures: ['input', 'select', 'datePicker', 'rate', 'switch', 'slider', 'cascader']
  }
];

/**
 * Test AI Connection
 */
export const testAIConnectionStatus = async () => {
  console.log('🔌 Testing AI Connection...');
  
  try {
    const result = await testAIConnection();
    
    if (result.success) {
      console.log('✅ AI Connection successful');
      console.log(`   Model: ${result.model}`);
      console.log(`   Response: ${JSON.stringify(result.response)}`);
      return true;
    } else {
      console.error('❌ AI Connection failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ AI Connection test error:', error.message);
    return false;
  }
};

/**
 * Test Complexity Detection
 */
export const testComplexityDetection = () => {
  console.log('🧠 Testing Complexity Detection...');
  
  let passed = 0;
  let total = 0;
  
  Object.entries(TEST_PROMPTS).forEach(([expectedLevel, testCase]) => {
    total++;
    const detectedComplexity = detectComplexity(testCase.prompt);
    
    if (detectedComplexity === testCase.expectedComplexity) {
      console.log(`✅ ${testCase.prompt.substring(0, 50)}... → ${detectedComplexity}`);
      passed++;
    } else {
      console.log(`❌ ${testCase.prompt.substring(0, 50)}... → Expected: ${testCase.expectedComplexity}, Got: ${detectedComplexity}`);
    }
  });
  
  console.log(`📊 Complexity Detection: ${passed}/${total} tests passed`);
  return passed === total;
};

/**
 * Test Component Suggestion
 */
export const testComponentSuggestion = () => {
  console.log('🎯 Testing Component Suggestion...');
  
  let passed = 0;
  let total = 0;
  
  Object.entries(TEST_PROMPTS).forEach(([level, testCase]) => {
    total++;
    const suggestedComponents = suggestComponents(testCase.prompt);
    const hasExpectedComponents = testCase.expectedComponents.some(component => 
      suggestedComponents.includes(component)
    );
    
    if (hasExpectedComponents) {
      console.log(`✅ ${level}: Found expected components in suggestions`);
      console.log(`   Suggested: [${suggestedComponents.join(', ')}]`);
      passed++;
    } else {
      console.log(`❌ ${level}: Missing expected components`);
      console.log(`   Expected: [${testCase.expectedComponents.join(', ')}]`);
      console.log(`   Suggested: [${suggestedComponents.join(', ')}]`);
    }
  });
  
  console.log(`📊 Component Suggestion: ${passed}/${total} tests passed`);
  return passed === total;
};

/**
 * Test AI Form Generation
 */
export const testAIFormGeneration = async () => {
  console.log('🤖 Testing AI Form Generation...');
  
  let passed = 0;
  let total = 0;
  
  for (const [level, testCase] of Object.entries(TEST_PROMPTS)) {
    total++;
    console.log(`\n🔄 Testing ${level} form generation...`);
    
    try {
      const result = await generateFormSchema(testCase.prompt, {
        includeExamples: true,
        includeValidation: true,
        includeStyling: true
      });
      
      if (result.success) {
        console.log(`✅ ${level}: Schema generated successfully`);
        console.log(`   Components: ${Object.keys(result.schema.components || {}).length}`);
        console.log(`   Layout items: ${(result.schema.layout || []).length}`);
        console.log(`   Tokens used: ${result.metadata.tokensUsed}`);
        
        // Validate the generated schema
        const validation = validateAIGeneratedSchema(result.schema);
        if (validation.isValid) {
          console.log(`✅ ${level}: Schema validation passed (Score: ${validation.score}/100)`);
          passed++;
        } else {
          console.log(`❌ ${level}: Schema validation failed`);
          console.log(`   Errors: ${validation.errors.join(', ')}`);
        }
      } else {
        console.log(`❌ ${level}: Schema generation failed - ${result.error}`);
        if (result.fallbackSchema) {
          console.log(`   Fallback schema provided`);
        }
      }
    } catch (error) {
      console.log(`❌ ${level}: Generation error - ${error.message}`);
    }
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`📊 AI Form Generation: ${passed}/${total} tests passed`);
  return passed === total;
};

/**
 * Test Specific Form Types
 */
export const testSpecificFormTypes = async () => {
  console.log('🎨 Testing Specific Form Types...');
  
  let passed = 0;
  let total = SPECIFIC_FORM_TESTS.length;
  
  for (const testCase of SPECIFIC_FORM_TESTS) {
    console.log(`\n🔄 Testing ${testCase.name}...`);
    
    try {
      const result = await generateFormSchema(testCase.prompt);
      
      if (result.success) {
        const schema = result.schema;
        let hasExpectedFeatures = true;
        
        // Check for expected features
        testCase.expectedFeatures.forEach(feature => {
          if (feature === 'stepsContainer') {
            const hasSteps = schema.layout?.some(item => item.type === 'stepsContainer') ||
                            Object.values(schema.components || {}).some(comp => comp.type === 'stepsContainer');
            if (!hasSteps) {
              console.log(`   ⚠️ Missing expected feature: ${feature}`);
              hasExpectedFeatures = false;
            }
          }
          // Add more feature checks as needed
        });
        
        if (hasExpectedFeatures) {
          console.log(`✅ ${testCase.name}: All expected features found`);
          passed++;
        } else {
          console.log(`❌ ${testCase.name}: Some expected features missing`);
        }
      } else {
        console.log(`❌ ${testCase.name}: Generation failed - ${result.error}`);
      }
    } catch (error) {
      console.log(`❌ ${testCase.name}: Test error - ${error.message}`);
    }
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1500));
  }
  
  console.log(`📊 Specific Form Types: ${passed}/${total} tests passed`);
  return passed === total;
};

/**
 * Test Schema Enhancement
 */
export const testSchemaEnhancement = async () => {
  console.log('🔧 Testing Schema Enhancement...');
  
  try {
    // Generate a basic schema
    const result = await generateFormSchema("Create a simple contact form");
    
    if (result.success) {
      const originalSchema = result.schema;
      const enhancedSchema = enhanceAISchema(originalSchema);
      
      // Check enhancements
      const hasEnhancements = 
        enhancedSchema.metadata.enhancedByAI === true &&
        enhancedSchema.metadata.updatedAt &&
        enhancedSchema.metadata.version;
      
      if (hasEnhancements) {
        console.log('✅ Schema enhancement successful');
        console.log(`   Enhanced metadata: ${Object.keys(enhancedSchema.metadata).length} properties`);
        return true;
      } else {
        console.log('❌ Schema enhancement failed');
        return false;
      }
    } else {
      console.log('❌ Could not generate schema for enhancement test');
      return false;
    }
  } catch (error) {
    console.log(`❌ Schema enhancement error: ${error.message}`);
    return false;
  }
};

/**
 * Run All Tests
 */
export const runAllAITests = async () => {
  console.log('🚀 Starting AI Form Generation Test Suite...\n');
  
  const results = {
    connection: false,
    complexity: false,
    components: false,
    generation: false,
    specificForms: false,
    enhancement: false
  };
  
  // Test AI connection first
  results.connection = await testAIConnectionStatus();
  
  if (!results.connection) {
    console.log('\n❌ AI connection failed. Skipping generation tests.');
    return results;
  }
  
  // Run all tests
  results.complexity = testComplexityDetection();
  results.components = testComponentSuggestion();
  results.generation = await testAIFormGeneration();
  results.specificForms = await testSpecificFormTypes();
  results.enhancement = await testSchemaEnhancement();
  
  // Summary
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log('\n📋 Test Suite Summary:');
  console.log(`   Connection Test: ${results.connection ? '✅' : '❌'}`);
  console.log(`   Complexity Detection: ${results.complexity ? '✅' : '❌'}`);
  console.log(`   Component Suggestion: ${results.components ? '✅' : '❌'}`);
  console.log(`   AI Generation: ${results.generation ? '✅' : '❌'}`);
  console.log(`   Specific Forms: ${results.specificForms ? '✅' : '❌'}`);
  console.log(`   Schema Enhancement: ${results.enhancement ? '✅' : '❌'}`);
  console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! AI form generation is working perfectly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
  
  return results;
};

// Export individual test functions for manual testing
export default {
  runAllAITests,
  testAIConnectionStatus,
  testComplexityDetection,
  testComponentSuggestion,
  testAIFormGeneration,
  testSpecificFormTypes,
  testSchemaEnhancement
};
