/**
 * AIChatSection Component - Refactored for Better Maintainability
 *
 * This is the main AI chat interface component for the form builder.
 * It has been refactored into smaller, focused modules while preserving
 * all existing functionality and behavior.
 *
 * Features:
 * - AI-powered form generation with real-time feedback
 * - Enterprise-grade chat interface with modern design
 * - Smooth animations and professional UX
 * - Comprehensive error handling and connection management
 * - Auto-application of generated forms to builder
 *
 * Architecture:
 * - Modular component structure with separated concerns
 * - Custom hooks for state management and side effects
 * - Styled components for consistent theming
 * - Utility functions for common operations
 * - Constants for configuration and text content
 *
 * @param {Object} props - Component props
 * @param {Function} props.onFormGenerated - Callback when form is generated
 */

import React, { memo, useEffect } from 'react';
import { Input, Button, Space } from 'antd';
import { SendOutlined } from '@ant-design/icons';

// Import refactored modules
import {
  AIChatContainer,
  ChatMessages,
  ChatInput,
} from './AIChatSection/styles/StyledComponents';
import ChatHeader from './AIChatSection/components/ChatHeader';
import ChatMessage from './AIChatSection/components/ChatMessage';
import LoadingMessage from './AIChatSection/components/LoadingMessage';
import StatusBar from './AIChatSection/components/StatusBar';

// Import custom hooks
import { useAIConnection } from './AIChatSection/hooks/useAIConnection';
import { useMessageHandling } from './AIChatSection/hooks/useMessageHandling';

// Import animation constants and utilities
import {
  containerVariants,
  injectGlobalAnimations,
} from './AIChatSection/constants/animations';
import { UI_TEXT } from './AIChatSection/constants';

const { TextArea } = Input;

/**
 * Main AIChatSection component with refactored architecture
 *
 * This component now uses custom hooks and separated components for better
 * maintainability while preserving all original functionality.
 */
const AIChatSection = memo(({ onFormGenerated }) => {
  // Initialize global animations on component mount
  useEffect(() => {
    injectGlobalAnimations();
  }, []);

  // Use custom hooks for state management
  const connectionState = useAIConnection();
  const messageState = useMessageHandling({
    onFormGenerated,
    aiConnected: connectionState.aiConnected,
  });

  /**
   * Handles the modify form functionality
   * Sets the input value for modifying the previous form
   */
  const handleModifyForm = (modifyText) => {
    messageState.setModifyInput(modifyText);
  };

  // Note: Schema copying is handled within the ChatMessage component

  return (
    <AIChatContainer>
      {/* Chat Header with AI branding and connection status */}
      <ChatHeader
        aiConnected={connectionState.aiConnected}
        statusText={connectionState.statusText}
        statusColor={connectionState.statusColor}
      />

      {/* Messages Container with smooth animations */}
      <ChatMessages
        variants={containerVariants}
        initial='hidden'
        animate='visible'
      >
        {/* Render all chat messages */}
        {messageState.messages.map((message, index) => (
          <ChatMessage
            key={message.id}
            message={message}
            index={index}
            onModifyForm={handleModifyForm}
          />
        ))}

        {/* Show loading message during AI processing */}
        {messageState.isLoading && (
          <LoadingMessage
            processingStage={messageState.processingStage}
            processingProgress={messageState.processingProgress}
          />
        )}
      </ChatMessages>

      {/* AI Status Bar with connection and processing status */}
      <StatusBar
        aiConnected={connectionState.aiConnected}
        aiStatus={messageState.aiStatus}
        isProcessing={messageState.isProcessing}
      />

      {/* Chat Input Area */}
      <ChatInput>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            value={messageState.inputValue}
            onChange={(e) => messageState.setInputValue(e.target.value)}
            onKeyDown={messageState.handleKeyPress}
            placeholder={UI_TEXT.INPUT_PLACEHOLDER}
            autoSize={{ minRows: 1, maxRows: 3 }}
            disabled={messageState.isLoading}
            style={{ resize: 'none' }}
          />
          <Button
            type='primary'
            icon={<SendOutlined />}
            onClick={messageState.handleSendMessage}
            disabled={!messageState.canSendMessage}
            style={{ height: 'auto' }}
          />
        </Space.Compact>
      </ChatInput>
    </AIChatContainer>
  );
});

// Set display name for debugging
AIChatSection.displayName = 'AIChatSection';

export default AIChatSection;
