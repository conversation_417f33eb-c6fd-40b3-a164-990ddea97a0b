import { useDrag } from 'react-dnd';
import React, { useRef, memo, useMemo, useCallback, useState } from 'react';
import { Card, Button, Modal, Input, Form, Switch } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { CARD_CONTAINER } from '../../../constants';
import DropZone from '../DropZone';
import Component from '../Component';
import styled from 'styled-components';

// Styled component for inline editable text
const InlineEditableText = styled.span`
  .editable-text {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: inline-block;
    min-width: 20px;

    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
      border: 1px solid rgba(24, 144, 255, 0.3);
    }

    &.placeholder-text {
      color: #999;
      font-style: italic;
    }
  }

  .editing-input {
    border: 1px solid #1890ff;
    border-radius: 3px;
    padding: 2px 4px;
    outline: none;
    background: white;
    box-shadow: 0 0 4px rgba(24, 144, 255, 0.3);
    min-width: 100px;
  }
`;

// Memoized CardContainer component for advanced nested drag-and-drop
const CardContainer = memo(
  ({ data, components, handleDrop, path, onUpdateComponent }) => {
    const ref = useRef(null);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [form] = Form.useForm();

    // Inline editing state
    const [isEditing, setIsEditing] = useState(false);
    const [editingValue, setEditingValue] = useState('');
    const [originalValue, setOriginalValue] = useState('');
    const [editingProperty, setEditingProperty] = useState(null);

    // Memoized drag item
    const dragItem = useMemo(
      () => ({
        id: data?.id,
        type: CARD_CONTAINER,
        children: data?.children || [],
        path: path || `card-${data?.id}`, // Ensure path is always defined
      }),
      [data?.id, data?.children, path],
    );

    // Memoized component renderer
    const renderComponent = useCallback(
      (component, currentPath) => {
        return (
          <Component
            key={component.id}
            data={component}
            components={components}
            path={currentPath}
            handleDrop={handleDrop}
            onUpdateComponent={onUpdateComponent}
          />
        );
      },
      [components, handleDrop, onUpdateComponent],
    );

    // Inline editing functions
    const handleLabelClick = useCallback(
      (e, currentText, propertyName = 'cardProps.title') => {
        e.stopPropagation();
        setIsEditing(true);
        setEditingValue(currentText || '');
        setOriginalValue(currentText || '');
        setEditingProperty(propertyName);
      },
      [],
    );

    const handleEditingKeyDown = useCallback(
      (e, propertyName = 'cardProps.title') => {
        if (e.key === 'Enter') {
          handleEditingSave(propertyName);
        } else if (e.key === 'Escape') {
          handleEditingCancel();
        }
      },
      [],
    );

    const handleEditingBlur = useCallback(
      (propertyName = 'cardProps.title') => {
        handleEditingSave(propertyName);
      },
      [],
    );

    const handleEditingSave = useCallback(
      (propertyName = 'cardProps.title') => {
        if (onUpdateComponent && editingValue !== originalValue) {
          // Handle nested properties like 'cardProps.title'
          if (propertyName.includes('.')) {
            const [parentProp, childProp] = propertyName.split('.');
            const component = components[data.id] || {};
            const currentParent = component[parentProp] || {};
            onUpdateComponent(data?.id, {
              [parentProp]: {
                ...currentParent,
                [childProp]: editingValue,
              },
            });
          } else {
            onUpdateComponent(data?.id, {
              [propertyName]: editingValue,
            });
          }
        }
        setIsEditing(false);
        setEditingValue('');
        setOriginalValue('');
        setEditingProperty(null);
      },
      [onUpdateComponent, data?.id, editingValue, originalValue, components],
    );

    const handleEditingCancel = useCallback(() => {
      setIsEditing(false);
      setEditingValue('');
      setOriginalValue('');
      setEditingProperty(null);
    }, []);

    // Helper function to render editable text
    const renderEditableText = useCallback(
      (
        text,
        propertyName = 'cardProps.title',
        placeholder = 'Click to edit',
      ) => {
        const displayText = text || placeholder;
        const isPlaceholder = !text;

        const isCurrentlyEditing =
          isEditing && editingProperty === propertyName;

        if (isCurrentlyEditing) {
          return (
            <input
              className='editing-input'
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              onKeyDown={(e) => handleEditingKeyDown(e, propertyName)}
              onBlur={() => handleEditingBlur(propertyName)}
              autoFocus
              style={{
                fontSize: 'inherit',
                fontFamily: 'inherit',
                fontWeight: 'inherit',
                color: 'inherit',
              }}
            />
          );
        }

        return (
          <InlineEditableText>
            <span
              className={`editable-text ${
                isPlaceholder ? 'placeholder-text' : ''
              }`}
              onClick={(e) => handleLabelClick(e, text, propertyName)}
              title='Click to edit'
            >
              {displayText}
            </span>
          </InlineEditableText>
        );
      },
      [
        isEditing,
        editingValue,
        handleEditingKeyDown,
        handleEditingBlur,
        handleLabelClick,
      ],
    );

    const [{ isDragging }, drag] = useDrag({
      type: CARD_CONTAINER,
      item: dragItem,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    // Memoized style
    const containerStyle = useMemo(
      () => ({
        opacity: isDragging ? 0.5 : 1,
        margin: '8px 0',
        border: isDragging ? '2px dashed #1890ff' : 'none',
      }),
      [isDragging],
    );

    // Card configuration - get from components registry for styling, layout for children
    const component = components[data.id] || {};
    const cardProps = component.cardProps || data.cardProps || {};
    const styling = component.styling || data.styling || {};
    const children = data.children || []; // Children come from layout structure

    // Handle card settings update
    const handleUpdateSettings = useCallback(() => {
      form.setFieldsValue({
        title: cardProps.title || '',
        bordered: styling.bordered !== false,
        hoverable: styling.hoverable === true,
        size: styling.size || 'default',
      });
      setIsEditModalVisible(true);
    }, [cardProps, styling, form]);

    const handleSaveSettings = useCallback(() => {
      form.validateFields().then((values) => {
        const updatedCardProps = {
          ...cardProps,
          title: values.title,
        };

        const updatedStyling = {
          ...styling,
          bordered: values.bordered,
          hoverable: values.hoverable,
          size: values.size,
        };

        onUpdateComponent?.(data.id, {
          cardProps: updatedCardProps,
          styling: updatedStyling,
        });

        setIsEditModalVisible(false);
        form.resetFields();
      });
    }, [form, data.id, cardProps, styling, onUpdateComponent]);

    // Memoized card extra content
    const cardExtra = useMemo(
      () => (
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          <span style={{ fontSize: '12px', color: '#666' }}>
            Card Container
          </span>
          <Button
            type='text'
            size='small'
            icon={<SettingOutlined />}
            onClick={handleUpdateSettings}
            style={{ padding: '0 4px' }}
          />
        </div>
      ),
      [handleUpdateSettings],
    );

    drag(ref);

    return (
      <div ref={ref} style={containerStyle}>
        <Card
          title={renderEditableText(
            cardProps.title,
            'cardProps.title',
            'Card Container',
          )}
          extra={cardExtra}
          bordered={styling.bordered !== false}
          hoverable={styling.hoverable === true}
          size={styling.size || 'default'}
          style={{
            minHeight: '200px',
            background: '#fafafa',
            border: '2px dashed #d9d9d9',
          }}
          bodyStyle={{
            minHeight: '150px',
            padding: '16px',
          }}
        >
          {/* Enhanced drop zone handling for better UX */}
          {children.length === 0 ? (
            // Empty state with prominent drop zone
            <DropZone
              data={{
                path: `${path}-0`,
                childrenCount: 0,
                containerId: data.id,
                containerType: 'card-content',
                index: 0,
              }}
              onDrop={handleDrop}
              className='empty-card-drop-zone'
              style={{
                minHeight: '150px',
                border: '2px dashed #d9d9d9',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)',
                transition: 'all 0.2s ease',
              }}
            >
              <div style={{ textAlign: 'center', color: '#999' }}>
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>🗃️</div>
                <div style={{ fontSize: '14px', fontWeight: 500 }}>
                  Drop components into this card
                </div>
                <div style={{ fontSize: '12px', marginTop: '4px' }}>
                  This card can contain form fields, other containers, and
                  sections
                </div>
              </div>
            </DropZone>
          ) : (
            // Render existing components with enhanced drop zones
            <div style={{ minHeight: '150px' }}>
              {/* Check if we need to render as rows or columns */}
              {children.some((child) => child.type === 'row') ? (
                // Vertical layout for rows
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '8px',
                  }}
                >
                  {children.map((component, index) => {
                    const currentPath = `${path}-${index}`;
                    const dropZoneData = {
                      path: currentPath,
                      childrenCount: children.length,
                      containerId: data.id,
                      containerType: 'card-content',
                      index: index,
                    };

                    return (
                      <React.Fragment key={component.id}>
                        {/* Vertical drop zone before each component */}
                        <DropZone
                          data={dropZoneData}
                          onDrop={handleDrop}
                          className='card-vertical-drop-zone'
                          style={{
                            minHeight: '8px',
                            margin: '4px 0',
                            borderRadius: '4px',
                          }}
                        />
                        {/* Component wrapper with enhanced styling */}
                        <div
                          style={{
                            border: '1px solid #f0f0f0',
                            borderRadius: '6px',
                            padding: '12px',
                            background: '#fafafa',
                            transition: 'all 0.2s ease',
                          }}
                        >
                          {renderComponent(component, currentPath)}
                        </div>
                      </React.Fragment>
                    );
                  })}
                  {/* Final vertical drop zone */}
                  <DropZone
                    data={{
                      path: `${path}-${children.length}`,
                      childrenCount: children.length,
                      containerId: data.id,
                      containerType: 'card-content',
                      index: children.length,
                    }}
                    onDrop={handleDrop}
                    className='card-vertical-drop-zone'
                    style={{
                      minHeight: '12px',
                      margin: '8px 0',
                      borderRadius: '4px',
                    }}
                    isLast
                  />
                </div>
              ) : (
                // Horizontal layout for columns
                <div
                  style={{
                    display: 'flex',
                    gap: '12px',
                    minHeight: '150px',
                    alignItems: 'stretch',
                  }}
                >
                  {children.map((component, index) => {
                    const currentPath = `${path}-${index}`;
                    const dropZoneData = {
                      path: currentPath,
                      childrenCount: children.length,
                      containerId: data.id,
                      containerType: 'card-content',
                      index: index,
                    };

                    return (
                      <React.Fragment key={component.id}>
                        {/* Horizontal drop zone for column creation */}
                        <DropZone
                          data={dropZoneData}
                          onDrop={handleDrop}
                          className='horizontalDrag'
                          style={{
                            minHeight: '150px',
                            width: '24px',
                            borderRadius: '6px',
                            background:
                              'linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%)',
                            border: '2px dashed transparent',
                            transition: 'all 0.2s ease',
                          }}
                        />
                        {/* Enhanced column content wrapper */}
                        <div
                          style={{
                            flex: 1,
                            minHeight: '150px',
                            border: '2px solid #f0f0f0',
                            borderRadius: '8px',
                            padding: '16px',
                            background: '#ffffff',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.02)',
                            transition: 'all 0.2s ease',
                            position: 'relative',
                          }}
                        >
                          {renderComponent(component, currentPath)}
                        </div>
                      </React.Fragment>
                    );
                  })}
                  {/* Final horizontal drop zone with enhanced styling */}
                  <DropZone
                    data={{
                      path: `${path}-${children.length}`,
                      childrenCount: children.length,
                      containerId: data.id,
                      containerType: 'card-content',
                      index: children.length,
                    }}
                    onDrop={handleDrop}
                    className='horizontalDrag'
                    style={{
                      minHeight: '150px',
                      width: '24px',
                      borderRadius: '6px',
                      background:
                        'linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%)',
                      border: '2px dashed transparent',
                      transition: 'all 0.2s ease',
                    }}
                    isLast
                  />
                </div>
              )}
            </div>
          )}
        </Card>

        {/* Settings Modal */}
        <Modal
          title='Card Settings'
          open={isEditModalVisible}
          onOk={handleSaveSettings}
          onCancel={() => {
            setIsEditModalVisible(false);
            form.resetFields();
          }}
          width={500}
        >
          <Form form={form} layout='vertical'>
            <Form.Item
              name='title'
              label='Card Title'
              rules={[{ required: true, message: 'Please enter card title' }]}
            >
              <Input placeholder='Enter card title' />
            </Form.Item>

            <Form.Item name='size' label='Card Size'>
              <select style={{ width: '100%', padding: '4px 8px' }}>
                <option value='small'>Small</option>
                <option value='default'>Default</option>
                <option value='large'>Large</option>
              </select>
            </Form.Item>

            <Form.Item name='bordered' valuePropName='checked' label='Bordered'>
              <Switch />
            </Form.Item>

            <Form.Item
              name='hoverable'
              valuePropName='checked'
              label='Hoverable'
            >
              <Switch />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance - avoid JSON.stringify for deep comparison
    if (
      prevProps.data?.id !== nextProps.data?.id ||
      prevProps.path !== nextProps.path ||
      prevProps.handleDrop !== nextProps.handleDrop ||
      prevProps.onUpdateComponent !== nextProps.onUpdateComponent ||
      prevProps.components !== nextProps.components
    ) {
      return false;
    }

    // Compare children array length and IDs only (shallow comparison)
    const prevChildren = prevProps.data?.children || [];
    const nextChildren = nextProps.data?.children || [];

    if (prevChildren.length !== nextChildren.length) {
      return false;
    }

    // Compare children IDs and types only (avoid deep comparison)
    for (let i = 0; i < prevChildren.length; i++) {
      if (
        prevChildren[i]?.id !== nextChildren[i]?.id ||
        prevChildren[i]?.type !== nextChildren[i]?.type
      ) {
        return false;
      }
    }

    return true;
  },
);

CardContainer.displayName = 'CardContainer';

export default CardContainer;
