/**
 * @fileoverview Preview Tab component for form preview interface
 *
 * This component renders the form preview tab with toolbar actions
 * and the FormRenderer component with error boundary protection.
 *
 * @module PreviewTab
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { Suspense, lazy } from 'react';
import { <PERSON><PERSON>, Spin } from 'antd';
import {
  EyeOutlined,
  DownloadOutlined,
  UploadOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import ErrorBoundary from './ErrorBoundary';
import * as S from '../../../styles';
import { loadingVariants, containerVariants } from '../constants/animations';

// Lazy load FormRenderer for better code splitting
const FormRenderer = lazy(() =>
  import('../../../FormRenderer').then((module) => ({
    default: module.FormRenderer,
  })),
);

/**
 * Preview Tab component
 *
 * Renders the form preview interface with action toolbar and
 * FormRenderer component wrapped in error boundary protection.
 *
 * @param {Object} props - Component props
 * @param {Array} props.layout - Current form layout structure
 * @param {Object} props.components - Component registry object
 * @param {number} props.forceRenderKey - Key for forcing re-renders
 * @param {number} props.componentUpdateCounter - Component update counter
 * @param {Function} props.handleFormSubmit - Form submission handler
 * @param {Object} props.formProps - Form properties object
 * @param {Function} props.handleViewJSON - JSON view handler
 * @param {Function} props.handleImportJSON - JSON import handler
 * @param {Function} props.handleExportJSON - JSON export handler
 * @param {Function} props.handleLoadEnhancedContainerTest - Test schema loader
 *
 * @returns {React.ReactNode} Preview tab JSX
 *
 * @example
 * ```jsx
 * <PreviewTab
 *   layout={layout}
 *   components={components}
 *   forceRenderKey={forceRenderKey}
 *   componentUpdateCounter={componentUpdateCounter}
 *   handleFormSubmit={handleFormSubmit}
 *   formProps={formProps}
 *   handleViewJSON={handleViewJSON}
 *   handleImportJSON={handleImportJSON}
 *   handleExportJSON={handleExportJSON}
 *   handleLoadEnhancedContainerTest={handleLoadEnhancedContainerTest}
 * />
 * ```
 */
const PreviewTab = ({
  layout,
  components,
  forceRenderKey,
  componentUpdateCounter,
  handleFormSubmit,
  formProps,
  handleViewJSON,
  handleImportJSON,
  handleExportJSON,
  handleLoadEnhancedContainerTest,
}) => {
  return (
    <motion.div
      variants={containerVariants}
      initial='hidden'
      animate='visible'
      style={{ height: '100%' }}
    >
      <S.PreviewContainer>
        {/* Preview Header with Actions */}
        <S.PreviewHeader>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              fontSize: '18px',
              fontWeight: 600,
              color: '#212121',
            }}
          >
            <EyeOutlined style={{ color: '#0066cc', fontSize: '20px' }} />
            Form Preview
          </div>
          <div style={{ display: 'flex', gap: '12px' }}>
            <Button
              icon={<FileTextOutlined />}
              onClick={handleViewJSON}
              style={{
                borderRadius: '8px',
                border: '2px solid #e0e0e0',
                fontWeight: 500,
              }}
            >
              View JSON
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={handleImportJSON}
              style={{
                borderRadius: '8px',
                border: '2px solid #e0e0e0',
                fontWeight: 500,
              }}
            >
              Import JSON
            </Button>
            <Button
              type='primary'
              icon={<DownloadOutlined />}
              onClick={handleExportJSON}
              style={{
                borderRadius: '8px',
                background: 'linear-gradient(135deg, #0066cc 0%, #0052a3 100%)',
                border: 'none',
                boxShadow: '0 2px 8px rgba(0, 102, 204, 0.3)',
                fontWeight: 500,
              }}
            >
              Export JSON
            </Button>
            <Button
              icon={<FileTextOutlined />}
              onClick={handleLoadEnhancedContainerTest}
              style={{
                borderRadius: '8px',
                border: '2px solid #52c41a',
                color: '#52c41a',
                fontWeight: 500,
              }}
            >
              🧪 Test Enhanced Containers
            </Button>
          </div>
        </S.PreviewHeader>

        {/* Preview Content */}
        <S.PreviewContent>
          <Suspense
            fallback={
              <motion.div
                variants={loadingVariants}
                initial='initial'
                animate='animate'
                exit='exit'
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '50px',
                  gap: '16px',
                }}
              >
                <Spin size='large' />
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  style={{
                    fontSize: '14px',
                    color: '#666',
                    fontWeight: 500,
                  }}
                >
                  Loading Form Renderer...
                </motion.div>
              </motion.div>
            }
          >
            <ErrorBoundary>
              <FormRenderer
                key={`preview-${forceRenderKey}-${componentUpdateCounter}`}
                layout={layout}
                components={components}
                onSubmit={handleFormSubmit}
                formProps={formProps}
              />
            </ErrorBoundary>
          </Suspense>
        </S.PreviewContent>
      </S.PreviewContainer>
    </motion.div>
  );
};

export default PreviewTab;
