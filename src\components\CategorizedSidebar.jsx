/**
 * CategorizedSidebar Component - Refactored for Better Maintainability
 *
 * This is the main categorized sidebar component for the form builder.
 * It has been refactored into smaller, focused modules while preserving
 * all existing drag-and-drop functionality and categorization behavior.
 *
 * Features:
 * - Component categorization with intelligent sorting
 * - Advanced search and filtering capabilities
 * - Drag-and-drop integration with form builder canvas
 * - Responsive design with modern UI/UX patterns
 * - Performance-optimized rendering and state management
 *
 * Architecture:
 * - Modular component structure with separated concerns
 * - Custom hooks for state management and filtering logic
 * - Styled components for consistent theming
 * - Utility functions for component categorization
 * - Constants for configuration and category metadata
 *
 * @returns {JSX.Element} Rendered categorized sidebar component
 */

import React, { memo } from 'react';
import { Collapse } from 'antd';

// Import constants and data
import { SIDEBAR_ITEMS } from '../constants';

// Import refactored modules
import {
  SidebarContainer,
  StyledCollapse,
} from './CategorizedSidebar/styles/StyledComponents';
import SidebarHeader from './CategorizedSidebar/components/SidebarHeader';
import CategoryPanel, {
  CategoryPanelHeader,
} from './CategorizedSidebar/components/CategoryPanel';
import StatsCard from './CategorizedSidebar/components/StatsCard';
import EmptyState from './CategorizedSidebar/components/EmptyState';

// Import custom hooks
import { useSidebarState } from './CategorizedSidebar/hooks/useSidebarState';
import { useComponentFiltering } from './CategorizedSidebar/hooks/useComponentFiltering';

// Import configuration
import { categoryConfig } from './CategorizedSidebar/constants/categoryConfig';

const { Panel } = Collapse;

/**
 * Main CategorizedSidebar component with refactored architecture
 *
 * This component now uses custom hooks and separated components for better
 * maintainability while preserving all original drag-and-drop functionality.
 */
const CategorizedSidebar = memo(() => {
  // Use custom hooks for state management and filtering
  const sidebarState = useSidebarState();
  const filteringState = useComponentFiltering(
    SIDEBAR_ITEMS,
    sidebarState.searchTerm,
  );

  /**
   * Gets the effective active keys for the collapse component
   *
   * When search is active, all categories with results are expanded.
   * When search is inactive, manually controlled expansion is used.
   */
  const effectiveActiveKeys = sidebarState.getEffectiveActiveKeys(
    filteringState.filteredItems,
  );

  /**
   * Handles search input changes
   *
   * Updates the search term state which triggers filtering
   * and category expansion logic.
   *
   * @param {string} value - New search input value
   */
  const handleSearchChange = (value) => {
    sidebarState.setSearchTerm(value);
  };

  /**
   * Handles clearing the search
   *
   * Resets search state and returns to default category expansion.
   */
  const handleClearSearch = () => {
    sidebarState.clearSearch();
  };

  /**
   * Renders the main content area
   *
   * Shows either the categorized components or an appropriate empty state
   * based on the current filtering results.
   *
   * @returns {JSX.Element} Main content area
   */
  const renderMainContent = () => {
    // Show empty state if no components after filtering
    if (filteringState.emptyStateInfo.shouldShowEmptyState) {
      return (
        <EmptyState
          type={filteringState.emptyStateInfo.emptyStateType}
          searchTerm={sidebarState.searchTerm}
          onClearSearch={handleClearSearch}
        />
      );
    }

    // Render categorized components in collapse panels
    return (
      <StyledCollapse
        activeKey={effectiveActiveKeys}
        onChange={sidebarState.handleCollapseChange}
        size='small'
      >
        {Object.entries(filteringState.filteredItems).map(
          ([category, items]) =>
            items.length > 0 && (
              <Panel
                key={category}
                header={
                  <CategoryPanelHeader
                    category={category}
                    categoryConfig={categoryConfig}
                    itemCount={items.length}
                  />
                }
              >
                <CategoryPanel category={category} items={items} />
              </Panel>
            ),
        )}
      </StyledCollapse>
    );
  };

  return (
    <SidebarContainer>
      {/* Sidebar header with search functionality */}
      <SidebarHeader
        totalComponents={filteringState.totalComponents}
        searchTerm={sidebarState.searchTerm}
        onSearchChange={handleSearchChange}
        isSearchActive={sidebarState.isSearchActive}
      />

      {/* Main content area with categories or empty state */}
      <div style={{ flex: 1, overflow: 'hidden', padding: '8px 12px' }}>
        {renderMainContent()}
      </div>

      {/* Statistics card at bottom */}
      <StatsCard
        totalComponents={filteringState.totalComponents}
        originalTotal={SIDEBAR_ITEMS.length}
        isSearchActive={sidebarState.isSearchActive}
        searchTerm={sidebarState.searchTerm}
      />
    </SidebarContainer>
  );
});

// Set display name for debugging
CategorizedSidebar.displayName = 'CategorizedSidebar';

export default CategorizedSidebar;
