/**
 * ChatHeader Component
 * 
 * Displays the AI chat header with branding, connection status, and visual indicators.
 * Features enterprise-grade design with smooth animations and connection feedback.
 * 
 * Features:
 * - AI branding and avatar display
 * - Real-time connection status indicator
 * - Animated connection status dot
 * - Professional enterprise styling
 * - Accessibility-compliant design
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.aiConnected - AI connection status
 * @param {string} props.statusText - Status text to display
 * @param {string} props.statusColor - Color for status indicators
 */

import React, { memo } from 'react';
import { Avatar, Typography } from 'antd';
import { RobotOutlined } from '@ant-design/icons';
import { ChatHeader as StyledChatHeader } from '../styles/StyledComponents';
import { headerVariants } from '../constants/animations';

const { Title, Text } = Typography;

/**
 * ChatHeader component for AI chat interface
 * 
 * @param {Object} props - Component props
 * @returns {JSX.Element} Rendered header component
 */
const ChatHeader = memo(({ aiConnected, statusText, statusColor }) => {
  /**
   * Gets the avatar background color based on connection status
   */
  const getAvatarColor = () => {
    return aiConnected ? '#3b82f6' : '#9ca3af';
  };

  /**
   * Gets the avatar box shadow based on connection status
   */
  const getAvatarShadow = () => {
    return aiConnected
      ? '0 4px 12px rgba(59, 130, 246, 0.2)'
      : '0 2px 6px rgba(0, 0, 0, 0.1)';
  };

  /**
   * Gets the status dot animation based on connection status
   */
  const getStatusAnimation = () => {
    return aiConnected ? 'pulse 2s infinite' : 'none';
  };

  return (
    <StyledChatHeader
      connected={aiConnected}
      variants={headerVariants}
      initial="initial"
      animate="animate"
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {/* AI Avatar */}
        <Avatar
          size={40}
          icon={<RobotOutlined />}
          style={{
            backgroundColor: getAvatarColor(),
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            boxShadow: getAvatarShadow(),
            flexShrink: 0,
          }}
        />

        {/* AI Branding and Status */}
        <div style={{ flex: 1, minWidth: 0 }}>
          {/* AI Name/Title */}
          <Title
            level={5}
            style={{
              margin: 0,
              fontSize: '16px',
              fontWeight: 600,
              color: '#1f2937',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
              lineHeight: 1.2,
            }}
          >
            Highpoint AI
          </Title>

          {/* Connection Status */}
          <Text
            style={{
              fontSize: '12px',
              color: statusColor,
              fontWeight: 500,
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              marginTop: '2px',
            }}
          >
            {/* Animated Status Dot */}
            <div
              style={{
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                backgroundColor: statusColor,
                animation: getStatusAnimation(),
                flexShrink: 0,
              }}
            />
            
            {/* Status Text */}
            <span style={{ 
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}>
              {statusText}
            </span>
          </Text>
        </div>

        {/* Additional Status Indicators (if needed) */}
        {aiConnected && (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center',
            gap: '4px',
          }}>
            <div
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: '#10b981',
                boxShadow: '0 0 6px rgba(16, 185, 129, 0.4)',
                animation: 'pulse 2s infinite',
              }}
            />
          </div>
        )}
      </div>
    </StyledChatHeader>
  );
});

// Set display name for debugging
ChatHeader.displayName = 'ChatHeader';

export default ChatHeader;
