/**
 * @fileoverview Roles Tab component for permission and access control
 *
 * This component renders the Roles tab content with permission and access
 * control settings for component visibility and interaction based on user roles.
 *
 * @module RolesTab
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useCallback, useState } from 'react';
import {
  Form,
  Select,
  Switch,
  Button,
  Space,
  Tag,
  Divider,
  Typography,
  Alert,
  Checkbox,
  Input,
  Card,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  LockOutlined,
  EyeOutlined,
  EditOutlined,
  PlusOutlined,
  DeleteOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

// Default user roles for demonstration
const DEFAULT_ROLES = [
  {
    id: 'admin',
    name: 'Administrator',
    color: 'red',
    level: 100,
    description: 'Full system access',
  },
  {
    id: 'manager',
    name: 'Manager',
    color: 'orange',
    level: 80,
    description: 'Management access',
  },
  {
    id: 'editor',
    name: 'Editor',
    color: 'blue',
    level: 60,
    description: 'Content editing access',
  },
  {
    id: 'contributor',
    name: 'Contributor',
    color: 'cyan',
    level: 40,
    description: 'Limited editing access',
  },
  {
    id: 'viewer',
    name: 'Viewer',
    color: 'green',
    level: 20,
    description: 'Read-only access',
  },
  {
    id: 'user',
    name: 'User',
    color: 'default',
    level: 10,
    description: 'Basic user access',
  },
  {
    id: 'guest',
    name: 'Guest',
    color: 'gray',
    level: 0,
    description: 'Guest access',
  },
];

// Permission types
const PERMISSION_TYPES = [
  { id: 'view', name: 'View', description: 'Can see the component' },
  { id: 'edit', name: 'Edit', description: 'Can modify component values' },
  { id: 'delete', name: 'Delete', description: 'Can remove the component' },
  {
    id: 'configure',
    name: 'Configure',
    description: 'Can change component settings',
  },
];

/**
 * Roles Tab component
 *
 * Renders permission and access control interface for the Roles tab with
 * user role management and component visibility/interaction settings.
 *
 * @param {Object} props - Component props
 * @param {Object} props.componentData - Current component data
 * @param {string} props.componentId - Component ID
 * @param {Function} props.onPropertyUpdate - Property update callback
 * @param {Object} props.formSchema - Current form schema for context
 *
 * @returns {JSX.Element} Roles tab content
 */
const RolesTab = ({
  componentData,
  componentId,
  onPropertyUpdate,
  formSchema,
}) => {
  const [form] = Form.useForm();
  const [availableRoles] = useState(DEFAULT_ROLES);
  const [selectedRoles, setSelectedRoles] = useState(
    componentData?.roles?.visible || [],
  );

  // Handle form field changes
  const handleFieldChange = useCallback(
    (field, value) => {
      onPropertyUpdate(field, value, { immediate: false });
    },
    [onPropertyUpdate],
  );

  // Handle form field blur (immediate update)
  const handleFieldBlur = useCallback(
    (field, value) => {
      onPropertyUpdate(field, value, { immediate: true });
    },
    [onPropertyUpdate],
  );

  // Handle role selection changes
  const handleRoleChange = useCallback(
    (type, roles) => {
      const updatedRoles = {
        ...componentData?.roles,
        [type]: roles,
      };

      if (type === 'visible') {
        setSelectedRoles(roles);
      }

      handleFieldChange('roles', updatedRoles);
      handleFieldBlur('roles', updatedRoles);
    },
    [componentData?.roles, handleFieldChange, handleFieldBlur],
  );

  if (!componentData) {
    return (
      <div
        style={{ textAlign: 'center', padding: '40px 20px', color: '#8c8c8c' }}
      >
        <TeamOutlined style={{ fontSize: '24px', marginBottom: '12px' }} />
        <div>No component selected</div>
      </div>
    );
  }

  return (
    <div>
      {/* Roles Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={5} style={{ margin: 0, color: '#262626' }}>
          Roles & Permissions
        </Title>
        <Text type='secondary' style={{ fontSize: '12px' }}>
          Control component access based on user roles
        </Text>
      </div>

      {/* Feature Notice */}
      <Alert
        message='Role-Based Access Control'
        description='This feature allows you to control component visibility and interaction based on user roles. Role management integration will be available in a future update.'
        type='info'
        showIcon
        style={{ marginBottom: '24px' }}
        icon={<LockOutlined />}
      />

      <Form
        form={form}
        layout='vertical'
        size='small'
        initialValues={{
          roleBasedAccess: componentData?.roles?.enabled || false,
          visibleRoles: componentData?.roles?.visible || [],
          editableRoles: componentData?.roles?.editable || [],
          requireAuth: componentData?.roles?.requireAuth || false,
        }}
      >
        {/* Enable Role-Based Access */}
        <Card size='small' style={{ marginBottom: '16px' }}>
          <Form.Item
            label='Enable Role-Based Access Control'
            name='roleBasedAccess'
            valuePropName='checked'
            tooltip='Enable role-based visibility and interaction control for this component'
            style={{ marginBottom: 0 }}
          >
            <Switch
              onChange={(checked) => {
                handleFieldChange('roles.enabled', checked);
                handleFieldBlur('roles.enabled', checked);
              }}
            />
          </Form.Item>
        </Card>

        {/* Visibility Roles */}
        <Card
          title={
            <Space>
              <EyeOutlined />
              <span>Visibility Control</span>
            </Space>
          }
          size='small'
          style={{ marginBottom: '16px' }}
        >
          <Form.Item
            label='Visible to Roles'
            name='visibleRoles'
            tooltip='Select which user roles can see this component'
          >
            <Select
              mode='multiple'
              placeholder='Select roles that can view this component'
              value={selectedRoles}
              onChange={(roles) => handleRoleChange('visible', roles)}
              style={{ width: '100%' }}
            >
              {availableRoles.map((role) => (
                <Option key={role.id} value={role.id}>
                  <Space>
                    <UserOutlined />
                    <div>
                      <div>{role.name}</div>
                      <Text type='secondary' style={{ fontSize: '11px' }}>
                        Level {role.level} • {role.description}
                      </Text>
                    </div>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* Selected Roles Display */}
          {selectedRoles.length > 0 && (
            <div style={{ marginTop: '8px' }}>
              <Text
                type='secondary'
                style={{
                  fontSize: '12px',
                  marginBottom: '8px',
                  display: 'block',
                }}
              >
                Component visible to:
              </Text>
              <Space wrap>
                {selectedRoles.map((roleId) => {
                  const role = availableRoles.find((r) => r.id === roleId);
                  return role ? (
                    <Tag
                      key={roleId}
                      color={role.color}
                      icon={<UserOutlined />}
                    >
                      {role.name}
                    </Tag>
                  ) : null;
                })}
              </Space>
            </div>
          )}
        </Card>

        {/* Interaction Roles */}
        <Card
          title={
            <Space>
              <EditOutlined />
              <span>Interaction Control</span>
            </Space>
          }
          size='small'
          style={{ marginBottom: '16px' }}
        >
          <Form.Item
            label='Editable by Roles'
            name='editableRoles'
            tooltip='Select which user roles can interact with/edit this component'
          >
            <Select
              mode='multiple'
              placeholder='Select roles that can edit this component'
              defaultValue={componentData?.roles?.editable || []}
              onChange={(roles) => handleRoleChange('editable', roles)}
              style={{ width: '100%' }}
            >
              {availableRoles.map((role) => (
                <Option key={role.id} value={role.id}>
                  <Space>
                    <EditOutlined />
                    {role.name}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label='Read-Only for Other Roles'
            tooltip='Make component read-only for roles not listed in editable roles'
            valuePropName='checked'
          >
            <Switch
              defaultChecked={componentData?.roles?.readOnlyForOthers || false}
              onChange={(checked) => {
                handleFieldChange('roles.readOnlyForOthers', checked);
                handleFieldBlur('roles.readOnlyForOthers', checked);
              }}
            />
          </Form.Item>
        </Card>

        {/* Authentication Requirements */}
        <Card
          title={
            <Space>
              <LockOutlined />
              <span>Authentication</span>
            </Space>
          }
          size='small'
          style={{ marginBottom: '16px' }}
        >
          <Form.Item
            label='Require Authentication'
            name='requireAuth'
            valuePropName='checked'
            tooltip='Require user to be authenticated to see this component'
          >
            <Switch
              onChange={(checked) => {
                handleFieldChange('roles.requireAuth', checked);
                handleFieldBlur('roles.requireAuth', checked);
              }}
            />
          </Form.Item>

          <Form.Item
            label='Fallback Message'
            tooltip="Message to show when user doesn't have permission to view this component"
          >
            <Input
              placeholder="You don't have permission to view this content"
              defaultValue={componentData?.roles?.fallbackMessage || ''}
              onChange={(e) =>
                handleFieldChange('roles.fallbackMessage', e.target.value)
              }
              onBlur={(e) =>
                handleFieldBlur('roles.fallbackMessage', e.target.value)
              }
            />
          </Form.Item>
        </Card>

        {/* Granular Permissions */}
        <Card
          title={
            <Space>
              <LockOutlined />
              <span>Granular Permissions</span>
            </Space>
          }
          size='small'
          style={{ marginBottom: '16px' }}
        >
          <div style={{ marginBottom: '16px' }}>
            <Text type='secondary' style={{ fontSize: '12px' }}>
              Configure specific permissions for each role
            </Text>
          </div>

          {PERMISSION_TYPES.map((permission) => (
            <Card
              key={permission.id}
              size='small'
              style={{ marginBottom: '12px' }}
              title={
                <Space>
                  <Text strong>{permission.name}</Text>
                  <Text type='secondary' style={{ fontSize: '11px' }}>
                    {permission.description}
                  </Text>
                </Space>
              }
            >
              <Select
                mode='multiple'
                placeholder={`Select roles that can ${permission.name.toLowerCase()}`}
                defaultValue={componentData?.permissions?.[permission.id] || []}
                onChange={(roles) =>
                  handleRoleChange(`permissions.${permission.id}`, roles)
                }
                style={{ width: '100%' }}
                size='small'
              >
                {availableRoles.map((role) => (
                  <Option key={role.id} value={role.id}>
                    <Space>
                      <Tag color={role.color} size='small'>
                        {role.name}
                      </Tag>
                      <Text type='secondary' style={{ fontSize: '10px' }}>
                        L{role.level}
                      </Text>
                    </Space>
                  </Option>
                ))}
              </Select>
            </Card>
          ))}
        </Card>

        {/* Advanced Role Settings */}
        <Card
          title={
            <Space>
              <TeamOutlined />
              <span>Advanced Settings</span>
            </Space>
          }
          size='small'
        >
          <Space direction='vertical' style={{ width: '100%' }}>
            <Form.Item
              label='Inherit Parent Permissions'
              tooltip='Inherit role permissions from parent container'
              valuePropName='checked'
            >
              <Switch
                defaultChecked={componentData?.roles?.inheritParent || true}
                onChange={(checked) => {
                  handleFieldChange('roles.inheritParent', checked);
                  handleFieldBlur('roles.inheritParent', checked);
                }}
              />
            </Form.Item>

            <Form.Item
              label='Override Child Permissions'
              tooltip='Override role permissions for all child components'
              valuePropName='checked'
            >
              <Switch
                defaultChecked={componentData?.roles?.overrideChildren || false}
                onChange={(checked) => {
                  handleFieldChange('roles.overrideChildren', checked);
                  handleFieldBlur('roles.overrideChildren', checked);
                }}
              />
            </Form.Item>

            <Divider style={{ margin: '16px 0' }} />

            <div>
              <Text type='secondary' style={{ fontSize: '12px' }}>
                <strong>Note:</strong> Role-based access control requires
                integration with your authentication system. Contact your system
                administrator to configure user roles and permissions.
              </Text>
            </div>
          </Space>
        </Card>
      </Form>
    </div>
  );
};

export default RolesTab;
