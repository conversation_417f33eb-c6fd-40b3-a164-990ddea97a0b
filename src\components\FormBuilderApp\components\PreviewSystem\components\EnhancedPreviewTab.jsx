/**
 * @fileoverview Enhanced Preview Tab component
 *
 * This component provides a comprehensive preview system with multiple device
 * previews, interactive testing, validation preview, and performance metrics.
 *
 * @module EnhancedPreviewTab
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Layout, Card, Space, Button, Tooltip, Divider } from 'antd';
import {
  DesktopOutlined,
  TabletOutlined,
  MobileOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  DashboardOutlined,
  FullscreenOutlined,
  ReloadOutlined,
  FileTextOutlined,
  DownloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
// Note: These components would be implemented in a full system
// For now, we'll create simplified placeholder components
// import PreviewToolbar from './PreviewToolbar';
// import DevicePreview from './DevicePreview';
// import InteractiveTestMode from './InteractiveTestMode';
// import ValidationPreview from './ValidationPreview';
// import PerformanceMetrics from './PerformanceMetrics';
// import { useDevicePreview } from '../hooks/useDevicePreview';
// import { useFormTesting } from '../hooks/useFormTesting';
// import { usePerformanceMonitoring } from '../hooks/usePerformanceMonitoring';
import {
  DEVICE_CONFIGS,
  PREVIEW_MODES,
  PREVIEW_ANIMATIONS,
} from '../constants/previewConstants';

const { Content, Sider } = Layout;

/**
 * Styled components
 */
const PreviewContainer = styled(Layout)`
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .ant-layout-content {
    display: flex;
    flex-direction: column;
    padding: 0;
    overflow: hidden;
  }

  .ant-layout-sider {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-left: 1px solid #e8e8e8;
  }
`;

const PreviewArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  position: relative;
  overflow: hidden;
  background: #f8f9fa;

  /* Full-size preview container */
  .preview-content {
    flex: 1;
    padding: 24px;
    overflow: auto;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    /* Custom scrollbar */
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }

  /* Form container styling */
  .form-preview-container {
    max-width: 1200px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 32px;
    min-height: calc(100vh - 200px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }
  }
`;

const ModeSelector = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 16px;

  .mode-button {
    flex: 1;
    height: auto;
    padding: 12px 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-radius: 8px;
    transition: all 0.2s ease;

    &.active {
      background: #1890ff;
      border-color: #1890ff;
      color: white;

      .anticon {
        color: white;
      }
    }

    .mode-label {
      font-size: 11px;
      text-align: center;
      line-height: 1.2;
    }
  }
`;

const MetricsPanel = styled(Card)`
  margin-bottom: 16px;

  .ant-card-body {
    padding: 12px;
  }

  .metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .metric-label {
      font-size: 11px;
      color: #666;
    }

    .metric-value {
      font-size: 12px;
      font-weight: 500;
    }
  }
`;

/**
 * Enhanced Preview Tab component
 *
 * @param {Object} props - Component props
 * @param {Object} props.formSchema - Current form schema
 * @param {Array} props.layout - Form layout data
 * @param {Function} props.onFormSubmit - Form submission handler
 * @param {Function} props.onValidationTest - Validation test handler
 * @returns {React.ReactNode} Enhanced preview tab JSX
 */
const EnhancedPreviewTab = ({
  formSchema,
  layout = [],
  components = {},
  onFormSubmit,
  onValidationTest,
  handleViewJSON,
  handleImportJSON,
  handleExportJSON,
  handleLoadEnhancedContainerTest,
}) => {
  // State management
  const [activeMode, setActiveMode] = useState(PREVIEW_MODES.STATIC.key);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Simplified state management (would use custom hooks in full implementation)
  const [selectedDevice, setSelectedDevice] = useState(
    DEVICE_CONFIGS.DESKTOP.key,
  );
  const [zoom, setZoom] = useState(1);
  const [orientation, setOrientation] = useState('portrait');
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [testResults, setTestResults] = useState(null);
  const [performanceMetrics, setPerformanceMetrics] = useState({});
  const [isMonitoring, setIsMonitoring] = useState(false);

  const deviceConfig =
    DEVICE_CONFIGS[selectedDevice.toUpperCase().replace('-', '_')] ||
    DEVICE_CONFIGS.DESKTOP;

  const toggleOrientation = useCallback(() => {
    setOrientation((prev) => (prev === 'portrait' ? 'landscape' : 'portrait'));
  }, []);

  const runInteractiveTest = useCallback(async () => {
    setIsTestRunning(true);
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setTestResults({
      status: 'success',
      message: 'Interactive test completed',
    });
    setIsTestRunning(false);
  }, []);

  const runValidationTest = useCallback(async () => {
    setIsTestRunning(true);
    await new Promise((resolve) => setTimeout(resolve, 1500));
    setTestResults({ status: 'success', message: 'Validation test completed' });
    setIsTestRunning(false);
  }, []);

  const clearTestResults = useCallback(() => {
    setTestResults(null);
  }, []);

  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    setPerformanceMetrics({
      renderTime: Math.random() * 500,
      componentCount: layout.length,
      memoryUsage: Math.random() * 50,
    });
  }, [layout.length]);

  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
  }, []);

  /**
   * Handle mode change
   */
  const handleModeChange = useCallback(
    (mode) => {
      setActiveMode(mode);

      // Start performance monitoring if needed
      if (mode === PREVIEW_MODES.PERFORMANCE.key) {
        startMonitoring();
      } else {
        stopMonitoring();
      }
    },
    [startMonitoring, stopMonitoring],
  );

  /**
   * Handle device change
   */
  const handleDeviceChange = useCallback(
    (device) => {
      setSelectedDevice(device);
    },
    [setSelectedDevice],
  );

  /**
   * Handle test execution
   */
  const handleRunTest = useCallback(async () => {
    clearTestResults();

    switch (activeMode) {
      case PREVIEW_MODES.INTERACTIVE.key:
        await runInteractiveTest();
        break;
      case PREVIEW_MODES.VALIDATION.key:
        await runValidationTest();
        break;
      default:
        break;
    }
  }, [activeMode, runInteractiveTest, runValidationTest, clearTestResults]);

  /**
   * Render full-size preview content with actual form
   */
  const renderPreviewContent = useCallback(() => {
    // Import FormRenderer dynamically to avoid circular dependencies
    const FormRenderer = React.lazy(() =>
      import('../../../../../FormRenderer').catch(() => ({
        default: () => (
          <div
            style={{
              padding: '40px',
              textAlign: 'center',
              color: '#666',
              fontSize: '16px',
            }}
          >
            <div style={{ marginBottom: '16px', fontSize: '48px' }}>📝</div>
            <div style={{ marginBottom: '8px', fontWeight: 600 }}>
              Form Preview
            </div>
            <div style={{ fontSize: '14px' }}>
              {layout.length > 0
                ? `Rendering ${layout.length} form components...`
                : 'Add components to see preview'}
            </div>
            {formSchema?.title && (
              <div
                style={{
                  marginTop: '16px',
                  padding: '12px',
                  background: '#f0f2f5',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: 500,
                }}
              >
                Form: {formSchema.title}
              </div>
            )}
          </div>
        ),
      })),
    );

    // Get device configuration
    const deviceConfig =
      DEVICE_CONFIGS[selectedDevice.toUpperCase().replace('-', '_')] ||
      DEVICE_CONFIGS.DESKTOP;

    // Device simulation styles
    const deviceSimulationStyle = {
      width: deviceConfig.width * deviceConfig.scale,
      height: deviceConfig.height * deviceConfig.scale,
      border: '1px solid #e8e8e8',
      borderRadius: deviceConfig.category === 'mobile' ? '20px' : '12px',
      background: '#ffffff',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
      overflow: 'hidden',
      transform: `scale(${zoom})`,
      transformOrigin: 'center',
      transition: 'all 0.3s ease',
      margin: '20px auto',
      position: 'relative',
    };

    // Responsive container style
    const responsiveContainerStyle = {
      width: '100%',
      maxWidth: deviceConfig.width,
      margin: '0 auto',
      padding: '20px',
      background: '#ffffff',
      borderRadius: '8px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    };

    return (
      <div className='preview-content'>
        {/* Preview Toolbar */}
        <div
          style={{
            height: '60px',
            background: 'rgba(255, 255, 255, 0.95)',
            borderBottom: '1px solid #e8e8e8',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 24px',
            backdropFilter: 'blur(10px)',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <span style={{ fontSize: '14px', fontWeight: 500 }}>
              {deviceConfig.name} Preview
            </span>
            <Divider type='vertical' />
            <span style={{ fontSize: '12px', color: '#666' }}>
              {Math.round(zoom * 100)}% zoom
            </span>
            <Divider type='vertical' />
            <span style={{ fontSize: '12px', color: '#666' }}>
              Mode:{' '}
              {PREVIEW_MODES[activeMode.toUpperCase()]?.name || activeMode}
            </span>
          </div>

          <Space>
            <Button
              size='small'
              onClick={() => setZoom(zoom > 0.5 ? zoom - 0.25 : zoom)}
              disabled={zoom <= 0.25}
            >
              Zoom Out
            </Button>
            <Button
              size='small'
              onClick={() => setZoom(zoom < 2 ? zoom + 0.25 : zoom)}
              disabled={zoom >= 2}
            >
              Zoom In
            </Button>
            <Button size='small' onClick={() => setZoom(1)}>
              Reset
            </Button>
            <Button
              size='small'
              icon={<ReloadOutlined />}
              onClick={() => window.location.reload()}
            >
              Refresh
            </Button>
            <Button
              size='small'
              type={sidebarCollapsed ? 'default' : 'primary'}
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            >
              {sidebarCollapsed ? 'Show Controls' : 'Hide Controls'}
            </Button>
          </Space>
        </div>

        {/* Device Preview Area */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '24px',
            background: activeMode === 'responsive' ? '#f0f2f5' : 'transparent',
          }}
        >
          {activeMode === 'responsive' ? (
            // Device simulation mode
            <div style={deviceSimulationStyle}>
              {/* Device header for mobile */}
              {deviceConfig.category === 'mobile' && (
                <div
                  style={{
                    height: '20px',
                    background: '#000',
                    borderRadius: '20px 20px 0 0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <div
                    style={{
                      width: '60px',
                      height: '4px',
                      background: '#333',
                      borderRadius: '2px',
                    }}
                  />
                </div>
              )}

              <div
                style={{
                  height: '100%',
                  overflow: 'auto',
                  padding: deviceConfig.category === 'mobile' ? '10px' : '20px',
                }}
              >
                <React.Suspense
                  fallback={
                    <div
                      style={{
                        padding: '40px',
                        textAlign: 'center',
                        color: '#1890ff',
                      }}
                    >
                      <div style={{ fontSize: '18px', marginBottom: '8px' }}>
                        ⏳
                      </div>
                      <div>Loading...</div>
                    </div>
                  }
                >
                  {/* Debug Information */}
                  {process.env.NODE_ENV === 'development' && (
                    <div
                      style={{
                        padding: '8px',
                        background: '#f0f0f0',
                        marginBottom: '16px',
                        fontSize: '12px',
                        borderRadius: '4px',
                      }}
                    >
                      <strong>🔍 Preview Debug:</strong> Layout:{' '}
                      {layout?.length || 0} items, Components:{' '}
                      {Object.keys(components || {}).length} registered
                    </div>
                  )}
                  <FormRenderer
                    layout={layout}
                    components={components}
                    onSubmit={onFormSubmit}
                    formProps={{
                      layout: 'vertical',
                      size:
                        deviceConfig.category === 'mobile' ? 'small' : 'large',
                    }}
                    submitButtonText='Test Submit'
                    resetButtonText='Reset Form'
                  />
                </React.Suspense>
              </div>
            </div>
          ) : (
            // Full-size responsive mode
            <div style={responsiveContainerStyle}>
              <div style={{ marginBottom: '20px' }}>
                <h2
                  style={{
                    margin: 0,
                    fontSize: '24px',
                    fontWeight: 600,
                    color: '#262626',
                  }}
                >
                  {formSchema?.title || 'Form Preview'}
                </h2>
                <p
                  style={{
                    margin: '4px 0 0 0',
                    color: '#8c8c8c',
                    fontSize: '14px',
                  }}
                >
                  {activeMode === 'validation'
                    ? 'Validation Testing Mode'
                    : activeMode === 'interactive'
                    ? 'Interactive Testing Mode'
                    : activeMode === 'performance'
                    ? 'Performance Monitoring Mode'
                    : 'Live Preview'}{' '}
                  • {layout.length} components
                </p>
              </div>

              <React.Suspense
                fallback={
                  <div
                    style={{
                      padding: '60px',
                      textAlign: 'center',
                      color: '#1890ff',
                    }}
                  >
                    <div style={{ fontSize: '24px', marginBottom: '16px' }}>
                      ⏳
                    </div>
                    <div>Loading form preview...</div>
                  </div>
                }
              >
                {/* Debug Information for Full Mode */}
                {process.env.NODE_ENV === 'development' && (
                  <div
                    style={{
                      padding: '12px',
                      background: '#f0f0f0',
                      marginBottom: '20px',
                      fontSize: '14px',
                      borderRadius: '6px',
                      border: '1px solid #d9d9d9',
                    }}
                  >
                    <strong>🔍 Full Preview Debug:</strong>
                    <br />
                    Layout Items: {layout?.length || 0}
                    <br />
                    Components Registered:{' '}
                    {Object.keys(components || {}).length}
                    <br />
                    Active Mode: {activeMode}
                    <br />
                    Form Schema Available: {formSchema ? 'Yes' : 'No'}
                  </div>
                )}
                <FormRenderer
                  layout={layout}
                  components={components}
                  onSubmit={onFormSubmit}
                  formProps={{
                    layout: 'vertical',
                    size: 'large',
                  }}
                  submitButtonText={
                    activeMode === 'validation'
                      ? 'Validate Form'
                      : activeMode === 'interactive'
                      ? 'Test Submit'
                      : 'Submit'
                  }
                  resetButtonText='Reset Form'
                />
              </React.Suspense>

              {/* Test Results Display */}
              {testResults && (
                <div
                  style={{
                    marginTop: '20px',
                    padding: '16px',
                    background:
                      testResults.status === 'success' ? '#f6ffed' : '#fff2f0',
                    border: `1px solid ${
                      testResults.status === 'success' ? '#b7eb8f' : '#ffccc7'
                    }`,
                    borderRadius: '8px',
                  }}
                >
                  <div
                    style={{
                      color:
                        testResults.status === 'success'
                          ? '#52c41a'
                          : '#ff4d4f',
                      fontWeight: 500,
                    }}
                  >
                    {testResults.status === 'success' ? '✅' : '❌'}{' '}
                    {testResults.message}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }, [
    formSchema,
    layout,
    components,
    onFormSubmit,
    selectedDevice,
    zoom,
    activeMode,
    testResults,
  ]);

  /**
   * Render sidebar content
   */
  const renderSidebarContent = useCallback(
    () => (
      <div style={{ padding: '16px' }}>
        {/* Mode Selector */}
        <div style={{ marginBottom: '24px' }}>
          <div
            style={{
              fontSize: '12px',
              fontWeight: 500,
              marginBottom: '8px',
              color: '#666',
            }}
          >
            Preview Mode
          </div>
          <ModeSelector>
            {Object.values(PREVIEW_MODES).map((mode) => (
              <Tooltip key={mode.key} title={mode.description}>
                <Button
                  className={`mode-button ${
                    activeMode === mode.key ? 'active' : ''
                  }`}
                  onClick={() => handleModeChange(mode.key)}
                  size='small'
                >
                  <div style={{ fontSize: '14px' }}>
                    {mode.key === 'static' && <EyeOutlined />}
                    {mode.key === 'interactive' && <PlayCircleOutlined />}
                    {mode.key === 'validation' && <CheckCircleOutlined />}
                    {mode.key === 'responsive' && <MobileOutlined />}
                    {mode.key === 'performance' && <DashboardOutlined />}
                  </div>
                  <div className='mode-label'>{mode.name}</div>
                </Button>
              </Tooltip>
            ))}
          </ModeSelector>
        </div>

        {/* Device Selector */}
        <div style={{ marginBottom: '24px' }}>
          <div
            style={{
              fontSize: '12px',
              fontWeight: 500,
              marginBottom: '8px',
              color: '#666',
            }}
          >
            Device Preview
          </div>
          <Space direction='vertical' style={{ width: '100%' }} size='small'>
            {Object.values(DEVICE_CONFIGS).map((device) => (
              <Button
                key={device.key}
                type={selectedDevice === device.key ? 'primary' : 'default'}
                size='small'
                block
                onClick={() => handleDeviceChange(device.key)}
                style={{
                  textAlign: 'left',
                  height: 'auto',
                  padding: '8px 12px',
                }}
              >
                <div
                  style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                >
                  <span style={{ fontSize: '12px' }}>
                    {device.category === 'desktop' && <DesktopOutlined />}
                    {device.category === 'tablet' && <TabletOutlined />}
                    {device.category === 'mobile' && <MobileOutlined />}
                  </span>
                  <div>
                    <div style={{ fontSize: '12px', fontWeight: 500 }}>
                      {device.name}
                    </div>
                    <div style={{ fontSize: '10px', opacity: 0.7 }}>
                      {device.width} × {device.height}
                    </div>
                  </div>
                </div>
              </Button>
            ))}
          </Space>
        </div>

        {/* JSON Actions */}
        <div style={{ marginBottom: '24px' }}>
          <div
            style={{
              fontSize: '12px',
              fontWeight: 500,
              marginBottom: '8px',
              color: '#666',
            }}
          >
            JSON Schema
          </div>
          <Space direction='vertical' style={{ width: '100%' }} size='small'>
            <Button
              size='small'
              block
              onClick={handleViewJSON}
              icon={<FileTextOutlined />}
              style={{
                textAlign: 'left',
                height: 'auto',
                padding: '8px 12px',
              }}
            >
              <div style={{ fontSize: '12px' }}>View JSON</div>
            </Button>
            <Button
              size='small'
              block
              onClick={handleImportJSON}
              icon={<UploadOutlined />}
              style={{
                textAlign: 'left',
                height: 'auto',
                padding: '8px 12px',
              }}
            >
              <div style={{ fontSize: '12px' }}>Import JSON</div>
            </Button>
            <Button
              size='small'
              block
              onClick={handleExportJSON}
              icon={<DownloadOutlined />}
              type='primary'
              style={{
                textAlign: 'left',
                height: 'auto',
                padding: '8px 12px',
              }}
            >
              <div style={{ fontSize: '12px' }}>Export JSON</div>
            </Button>
            {handleLoadEnhancedContainerTest && (
              <Button
                size='small'
                block
                onClick={handleLoadEnhancedContainerTest}
                icon={<FileTextOutlined />}
                style={{
                  textAlign: 'left',
                  height: 'auto',
                  padding: '8px 12px',
                  borderColor: '#52c41a',
                  color: '#52c41a',
                }}
              >
                <div style={{ fontSize: '12px' }}>🧪 Test Containers</div>
              </Button>
            )}
          </Space>
        </div>

        {/* Performance Metrics */}
        {activeMode === PREVIEW_MODES.PERFORMANCE.key && (
          <MetricsPanel title='Performance Metrics' size='small'>
            <div className='metric-item'>
              <span className='metric-label'>Render Time</span>
              <span className='metric-value'>
                {Math.round(performanceMetrics.renderTime || 0)}ms
              </span>
            </div>
            <div className='metric-item'>
              <span className='metric-label'>Components</span>
              <span className='metric-value'>
                {performanceMetrics.componentCount || 0}
              </span>
            </div>
            <div className='metric-item'>
              <span className='metric-label'>Memory</span>
              <span className='metric-value'>
                {Math.round(performanceMetrics.memoryUsage || 0)}MB
              </span>
            </div>
            <div className='metric-item'>
              <span className='metric-label'>Status</span>
              <span
                className='metric-value'
                style={{ color: isMonitoring ? '#52c41a' : '#666' }}
              >
                {isMonitoring ? 'Monitoring' : 'Stopped'}
              </span>
            </div>
          </MetricsPanel>
        )}

        {/* Test Actions */}
        {(activeMode === PREVIEW_MODES.INTERACTIVE.key ||
          activeMode === PREVIEW_MODES.VALIDATION.key) && (
          <div style={{ marginBottom: '24px' }}>
            <div
              style={{
                fontSize: '12px',
                fontWeight: 500,
                marginBottom: '8px',
                color: '#666',
              }}
            >
              Test Actions
            </div>
            <Space direction='vertical' style={{ width: '100%' }} size='small'>
              <Button
                type='primary'
                size='small'
                block
                loading={isTestRunning}
                onClick={handleRunTest}
                icon={<PlayCircleOutlined />}
              >
                Run Test
              </Button>
              <Button
                size='small'
                block
                onClick={clearTestResults}
                icon={<ReloadOutlined />}
              >
                Clear Results
              </Button>
            </Space>
          </div>
        )}
      </div>
    ),
    [
      activeMode,
      selectedDevice,
      isTestRunning,
      performanceMetrics,
      isMonitoring,
      handleModeChange,
      handleDeviceChange,
      handleRunTest,
      clearTestResults,
      handleViewJSON,
      handleImportJSON,
      handleExportJSON,
      handleLoadEnhancedContainerTest,
    ],
  );

  return (
    <PreviewContainer>
      <Content>
        {/* Responsive Preview Area with Device Simulation */}
        <PreviewArea>{renderPreviewContent()}</PreviewArea>
      </Content>

      {/* Sidebar with Device Controls & Testing */}
      <Sider
        width={280}
        collapsible
        collapsed={sidebarCollapsed}
        onCollapse={setSidebarCollapsed}
        collapsedWidth={0}
        trigger={null}
        style={{
          position: 'relative',
          zIndex: 10,
        }}
      >
        {renderSidebarContent()}
      </Sider>
    </PreviewContainer>
  );
};

export default EnhancedPreviewTab;
