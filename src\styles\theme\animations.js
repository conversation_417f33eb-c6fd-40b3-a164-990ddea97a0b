// Modern Animation System - Smooth & Performant
export const animations = {
  // Timing functions (Easing curves)
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    
    // Custom easing for modern feel
    smooth: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    
    // AI-specific animations
    aiPulse: 'cubic-bezier(0.4, 0, 0.6, 1)',
    aiGlow: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  },

  // Duration scale
  duration: {
    instant: '0ms',
    fast: '150ms',
    normal: '250ms',
    slow: '350ms',
    slower: '500ms',
    slowest: '750ms',
  },

  // Common transitions
  transition: {
    // Basic transitions
    all: 'all 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    colors: 'color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    opacity: 'opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    shadow: 'box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    transform: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    
    // Interactive transitions
    hover: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    focus: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    active: 'all 100ms cubic-bezier(0.4, 0, 0.2, 1)',
    
    // Layout transitions
    height: 'height 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    width: 'width 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    size: 'width 250ms cubic-bezier(0.4, 0, 0.2, 1), height 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    
    // AI-specific transitions
    aiProcessing: 'all 500ms cubic-bezier(0.4, 0, 0.6, 1)',
    aiSuccess: 'all 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },

  // Keyframe animations
  keyframes: {
    // Loading animations
    spin: `
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `,
    
    pulse: `
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
    `,
    
    bounce: `
      @keyframes bounce {
        0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8, 0, 1, 1); }
        50% { transform: translateY(0); animation-timing-function: cubic-bezier(0, 0, 0.2, 1); }
      }
    `,
    
    // AI-specific animations
    aiGlow: `
      @keyframes aiGlow {
        0%, 100% { 
          box-shadow: 0 0 5px rgba(99, 102, 241, 0.3), 0 0 10px rgba(99, 102, 241, 0.2), 0 0 15px rgba(99, 102, 241, 0.1);
        }
        50% { 
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.4), 0 0 20px rgba(99, 102, 241, 0.3), 0 0 30px rgba(99, 102, 241, 0.2);
        }
      }
    `,
    
    aiPulse: `
      @keyframes aiPulse {
        0%, 100% { 
          transform: scale(1);
          opacity: 1;
        }
        50% { 
          transform: scale(1.05);
          opacity: 0.8;
        }
      }
    `,
    
    aiTyping: `
      @keyframes aiTyping {
        0%, 60%, 100% { transform: translateY(0); }
        30% { transform: translateY(-10px); }
      }
    `,
    
    // Entrance animations
    fadeIn: `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    `,
    
    slideInUp: `
      @keyframes slideInUp {
        from { 
          opacity: 0;
          transform: translateY(20px);
        }
        to { 
          opacity: 1;
          transform: translateY(0);
        }
      }
    `,
    
    slideInDown: `
      @keyframes slideInDown {
        from { 
          opacity: 0;
          transform: translateY(-20px);
        }
        to { 
          opacity: 1;
          transform: translateY(0);
        }
      }
    `,
    
    slideInLeft: `
      @keyframes slideInLeft {
        from { 
          opacity: 0;
          transform: translateX(-20px);
        }
        to { 
          opacity: 1;
          transform: translateX(0);
        }
      }
    `,
    
    slideInRight: `
      @keyframes slideInRight {
        from { 
          opacity: 0;
          transform: translateX(20px);
        }
        to { 
          opacity: 1;
          transform: translateX(0);
        }
      }
    `,
    
    scaleIn: `
      @keyframes scaleIn {
        from { 
          opacity: 0;
          transform: scale(0.9);
        }
        to { 
          opacity: 1;
          transform: scale(1);
        }
      }
    `,
    
    // Drag and drop animations
    dragStart: `
      @keyframes dragStart {
        from { 
          transform: scale(1) rotate(0deg);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        to { 
          transform: scale(1.05) rotate(2deg);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
      }
    `,
    
    dropZoneActive: `
      @keyframes dropZoneActive {
        0%, 100% { 
          border-color: #6366f1;
          background-color: rgba(99, 102, 241, 0.05);
        }
        50% { 
          border-color: #8b5cf6;
          background-color: rgba(139, 92, 246, 0.08);
        }
      }
    `,
  },

  // Animation classes (CSS-in-JS ready)
  classes: {
    // Loading states
    spin: {
      animation: 'spin 1s linear infinite',
    },
    pulse: {
      animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
    },
    bounce: {
      animation: 'bounce 1s infinite',
    },
    
    // AI animations
    aiGlow: {
      animation: 'aiGlow 2s ease-in-out infinite',
    },
    aiPulse: {
      animation: 'aiPulse 1.5s ease-in-out infinite',
    },
    aiTyping: {
      animation: 'aiTyping 1.4s ease-in-out infinite',
    },
    
    // Entrance animations
    fadeIn: {
      animation: 'fadeIn 250ms ease-out',
    },
    slideInUp: {
      animation: 'slideInUp 250ms ease-out',
    },
    slideInDown: {
      animation: 'slideInDown 250ms ease-out',
    },
    slideInLeft: {
      animation: 'slideInLeft 250ms ease-out',
    },
    slideInRight: {
      animation: 'slideInRight 250ms ease-out',
    },
    scaleIn: {
      animation: 'scaleIn 200ms ease-out',
    },
    
    // Drag and drop
    dragStart: {
      animation: 'dragStart 150ms ease-out forwards',
    },
    dropZoneActive: {
      animation: 'dropZoneActive 1s ease-in-out infinite',
    },
  },
};

// Export all keyframes as a single string for global injection
export const globalKeyframes = Object.values(animations.keyframes).join('\n');
