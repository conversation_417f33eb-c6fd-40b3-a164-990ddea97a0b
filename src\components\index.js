/**
 * Component exports for the form builder
 */

import { lazy } from 'react';

// Core drag and drop components
export { default as DropZone } from './DragAndDrop/DropZone';
export { default as SideBarItem } from './DragAndDrop/SideBarItem';
export { default as Row } from './DragAndDrop/Row';
export { default as Column } from './DragAndDrop/Column';
export { default as Component } from './DragAndDrop/Component';

// Container components
export * from './DragAndDrop/ContainerComponents';

// Sidebar components
export { default as CategorizedSidebar } from './CategorizedSidebar';

// AI Chat section
export { default as AIChatSection } from './AIChatSection';

// Lazy-loaded components
export const TrashDropZone = lazy(() => import('./DragAndDrop/TrashDropZone'));

// Performance utilities
export * from '../utils/performanceUtils';
