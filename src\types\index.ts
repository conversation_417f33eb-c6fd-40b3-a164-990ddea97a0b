/**
 * @fileoverview Core TypeScript type definitions
 *
 * This module contains all the core type definitions for the Enterprise Form Builder,
 * providing type safety and better developer experience.
 *
 * @module Types
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

// ============================================================================
// Core Form Types
// ============================================================================

/**
 * Form schema interface defining the complete form structure
 */
export interface FormSchema {
  /** Unique form identifier */
  id: string;
  /** Form title */
  title: string;
  /** Form description */
  description?: string;
  /** Schema version for compatibility */
  version: string;
  /** Array of form components */
  components: ComponentData[];
  /** Form-level settings */
  settings: FormSettings;
  /** Form metadata */
  metadata: FormMetadata;
  /** Creation timestamp */
  createdAt: string;
  /** Last modified timestamp */
  updatedAt: string;
}

/**
 * Individual component data structure
 */
export interface ComponentData {
  /** Unique component identifier */
  id: string;
  /** Component type (input, select, etc.) */
  type: ComponentType;
  /** Display label */
  label: string;
  /** Component-specific properties */
  properties: ComponentProperties;
  /** Visual styling */
  style: ComponentStyle;
  /** Validation rules */
  validation: ValidationRules;
  /** Child components for containers */
  children?: ComponentData[];
  /** Component metadata */
  metadata: ComponentMetadata;
  /** Visibility state */
  visible?: boolean;
  /** Disabled state */
  disabled?: boolean;
}

/**
 * Component types enumeration
 */
export type ComponentType = 
  | 'input'
  | 'textarea'
  | 'select'
  | 'checkbox'
  | 'radio'
  | 'button'
  | 'divider'
  | 'text'
  | 'image'
  | 'card'
  | 'tabs'
  | 'steps'
  | 'accordion'
  | 'row'
  | 'column'
  | 'form-section'
  | 'alert'
  | 'progress'
  | 'table'
  | 'list'
  | 'tree'
  | 'upload'
  | 'date-picker'
  | 'time-picker'
  | 'slider'
  | 'switch'
  | 'rate'
  | 'transfer'
  | 'cascader'
  | 'auto-complete'
  | 'mention'
  | 'color-picker';

// ============================================================================
// Component Properties
// ============================================================================

/**
 * Base component properties
 */
export interface ComponentProperties {
  /** Placeholder text */
  placeholder?: string;
  /** Default value */
  defaultValue?: any;
  /** Help text */
  helpText?: string;
  /** Tooltip text */
  tooltip?: string;
  /** Size variant */
  size?: 'small' | 'middle' | 'large';
  /** Custom CSS classes */
  className?: string;
  /** Data attributes */
  dataAttributes?: Record<string, string>;
  /** Accessibility attributes */
  ariaAttributes?: Record<string, string>;
}

/**
 * Input-specific properties
 */
export interface InputProperties extends ComponentProperties {
  /** Input type */
  inputType?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  /** Maximum length */
  maxLength?: number;
  /** Minimum length */
  minLength?: number;
  /** Allow clear button */
  allowClear?: boolean;
  /** Show character count */
  showCount?: boolean;
  /** Input prefix */
  prefix?: string;
  /** Input suffix */
  suffix?: string;
  /** Addon before */
  addonBefore?: string;
  /** Addon after */
  addonAfter?: string;
}

/**
 * Select-specific properties
 */
export interface SelectProperties extends ComponentProperties {
  /** Select options */
  options: SelectOption[];
  /** Allow multiple selection */
  multiple?: boolean;
  /** Enable search */
  showSearch?: boolean;
  /** Filter option function */
  filterOption?: boolean | ((input: string, option: SelectOption) => boolean);
  /** Loading state */
  loading?: boolean;
  /** Not found content */
  notFoundContent?: string;
}

/**
 * Select option interface
 */
export interface SelectOption {
  /** Option value */
  value: string | number;
  /** Option label */
  label: string;
  /** Option disabled state */
  disabled?: boolean;
  /** Option group */
  group?: string;
}

// ============================================================================
// Styling and Layout
// ============================================================================

/**
 * Component styling interface
 */
export interface ComponentStyle {
  /** Width */
  width?: string | number;
  /** Height */
  height?: string | number;
  /** Margin */
  margin?: string;
  /** Padding */
  padding?: string;
  /** Background color */
  backgroundColor?: string;
  /** Text color */
  color?: string;
  /** Font size */
  fontSize?: string | number;
  /** Font weight */
  fontWeight?: string | number;
  /** Text alignment */
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  /** Border radius */
  borderRadius?: string | number;
  /** Border */
  border?: string;
  /** Box shadow */
  boxShadow?: string;
  /** Display */
  display?: 'block' | 'inline' | 'inline-block' | 'flex' | 'grid' | 'none';
  /** Position */
  position?: 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky';
  /** Z-index */
  zIndex?: number;
  /** Opacity */
  opacity?: number;
  /** Transform */
  transform?: string;
  /** Transition */
  transition?: string;
}

/**
 * Layout configuration
 */
export interface LayoutConfig {
  /** Grid columns */
  columns?: number;
  /** Grid gap */
  gap?: string | number;
  /** Flex direction */
  flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  /** Justify content */
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  /** Align items */
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
  /** Flex wrap */
  flexWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
}

// ============================================================================
// Validation
// ============================================================================

/**
 * Validation rules interface
 */
export interface ValidationRules {
  /** Required field */
  required?: boolean;
  /** Minimum length */
  minLength?: number;
  /** Maximum length */
  maxLength?: number;
  /** Pattern validation */
  pattern?: string | RegExp;
  /** Minimum value */
  min?: number;
  /** Maximum value */
  max?: number;
  /** Email validation */
  email?: boolean;
  /** URL validation */
  url?: boolean;
  /** Custom validation function */
  custom?: (value: any) => boolean | string | Promise<boolean | string>;
  /** Validation message */
  message?: string;
  /** Validation trigger */
  trigger?: 'onChange' | 'onBlur' | 'onSubmit';
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  /** Validation success */
  valid: boolean;
  /** Error message */
  message?: string;
  /** Field name */
  field?: string;
  /** Error code */
  code?: string;
}

// ============================================================================
// Form Settings and Metadata
// ============================================================================

/**
 * Form settings interface
 */
export interface FormSettings {
  /** Form layout */
  layout?: 'horizontal' | 'vertical' | 'inline';
  /** Label alignment */
  labelAlign?: 'left' | 'right';
  /** Label width */
  labelWidth?: string | number;
  /** Form size */
  size?: 'small' | 'middle' | 'large';
  /** Disable all fields */
  disabled?: boolean;
  /** Validation trigger */
  validateTrigger?: 'onChange' | 'onBlur' | 'onSubmit';
  /** Scroll to first error */
  scrollToFirstError?: boolean;
  /** Preserve values on unmount */
  preserve?: boolean;
  /** Auto-save settings */
  autoSave?: {
    enabled: boolean;
    interval: number;
    endpoint?: string;
  };
}

/**
 * Form metadata interface
 */
export interface FormMetadata {
  /** Form category */
  category?: string;
  /** Form tags */
  tags?: string[];
  /** Form author */
  author?: string;
  /** Form status */
  status?: 'draft' | 'published' | 'archived';
  /** Form complexity score */
  complexity?: number;
  /** Usage statistics */
  usage?: {
    views: number;
    submissions: number;
    lastUsed: string;
  };
  /** Form permissions */
  permissions?: {
    read: string[];
    write: string[];
    admin: string[];
  };
}

/**
 * Component metadata interface
 */
export interface ComponentMetadata {
  /** Component category */
  category?: string;
  /** Component description */
  description?: string;
  /** Component version */
  version?: string;
  /** Component author */
  author?: string;
  /** Component tags */
  tags?: string[];
  /** Component complexity */
  complexity?: 'simple' | 'moderate' | 'complex' | 'advanced';
  /** Usage count */
  usageCount?: number;
  /** Last modified */
  lastModified?: string;
  /** Component dependencies */
  dependencies?: string[];
}

// ============================================================================
// UI and Interaction Types
// ============================================================================

/**
 * Position interface for panels and overlays
 */
export interface Position {
  /** X coordinate */
  x: number;
  /** Y coordinate */
  y: number;
  /** Width */
  width?: number;
  /** Height */
  height?: number;
}

/**
 * Panel configuration
 */
export interface PanelConfig {
  /** Panel width */
  width?: number;
  /** Panel height */
  height?: number;
  /** Resizable */
  resizable?: boolean;
  /** Draggable */
  draggable?: boolean;
  /** Collapsible */
  collapsible?: boolean;
  /** Default collapsed state */
  defaultCollapsed?: boolean;
  /** Panel position */
  position?: 'left' | 'right' | 'top' | 'bottom' | 'floating';
}

/**
 * Theme configuration
 */
export interface ThemeConfig {
  /** Primary color */
  primaryColor?: string;
  /** Success color */
  successColor?: string;
  /** Warning color */
  warningColor?: string;
  /** Error color */
  errorColor?: string;
  /** Background color */
  backgroundColor?: string;
  /** Text color */
  textColor?: string;
  /** Border radius */
  borderRadius?: number;
  /** Font family */
  fontFamily?: string;
  /** Font size scale */
  fontSizes?: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  /** Spacing scale */
  spacing?: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

// ============================================================================
// Event Types
// ============================================================================

/**
 * Form builder events
 */
export interface FormBuilderEvents {
  /** Schema changed */
  'schema:changed': (schema: FormSchema) => void;
  /** Component added */
  'component:added': (component: ComponentData) => void;
  /** Component updated */
  'component:updated': (componentId: string, updates: Partial<ComponentData>) => void;
  /** Component removed */
  'component:removed': (componentId: string) => void;
  /** Component selected */
  'component:selected': (componentId: string) => void;
  /** Form saved */
  'form:saved': (schema: FormSchema) => void;
  /** Form loaded */
  'form:loaded': (schema: FormSchema) => void;
  /** Validation failed */
  'validation:failed': (errors: ValidationResult[]) => void;
}

/**
 * Drag and drop event data
 */
export interface DragDropEventData {
  /** Source component type */
  componentType: ComponentType;
  /** Source position */
  sourcePosition: Position;
  /** Target position */
  targetPosition: Position;
  /** Drop zone identifier */
  dropZone: string;
  /** Drag operation type */
  operation: 'move' | 'copy' | 'create';
}

// ============================================================================
// API Types
// ============================================================================

/**
 * API response wrapper
 */
export interface ApiResponse<T = any> {
  /** Response data */
  data: T;
  /** Success status */
  success: boolean;
  /** Error message */
  message?: string;
  /** Error code */
  code?: string;
  /** Response metadata */
  meta?: {
    timestamp: string;
    version: string;
    requestId: string;
  };
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  /** Page number */
  page: number;
  /** Page size */
  pageSize: number;
  /** Total items */
  total?: number;
  /** Sort field */
  sortBy?: string;
  /** Sort order */
  sortOrder?: 'asc' | 'desc';
}

/**
 * Search parameters
 */
export interface SearchParams {
  /** Search query */
  query?: string;
  /** Search filters */
  filters?: Record<string, any>;
  /** Pagination */
  pagination?: PaginationParams;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Deep partial type for nested objects
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * Make specific properties required
 */
export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Make specific properties optional
 */
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Extract component properties by type
 */
export type ComponentPropertiesByType<T extends ComponentType> = 
  T extends 'input' ? InputProperties :
  T extends 'select' ? SelectProperties :
  ComponentProperties;
