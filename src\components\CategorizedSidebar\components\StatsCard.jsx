/**
 * StatsCard Component
 *
 * Statistics card displayed at the bottom of the CategorizedSidebar
 * showing component counts, filtering status, and other metrics.
 *
 * Features:
 * - Total component count display
 * - Filtered vs available component counts
 * - Search status indicator
 * - Visual feedback for different states
 * - Responsive design with consistent spacing
 * - Gradient background for visual appeal
 *
 * Props:
 * - totalComponents: Total number of components after filtering
 * - originalTotal: Original total before filtering
 * - isSearchActive: <PERSON>olean indicating if search is active
 * - searchTerm: Current search term for display
 *
 * @param {Object} props - Component props
 * @returns {JSX.Element} Rendered statistics card component
 */

import React, { memo } from 'react';
import { Badge, Typography, Space } from 'antd';
import { StarOutlined } from '@ant-design/icons';
import { StatsCard as StyledStatsCard } from '../styles/StyledComponents';
import { colors } from '../../../styles/theme';

const { Text } = Typography;

/**
 * StatsCard component for displaying sidebar statistics
 *
 * This component provides users with information about the current
 * state of the component library, including total counts, filtering
 * status, and search feedback.
 *
 * Design Principles:
 * - Clear presentation of key metrics
 * - Visual feedback for different states
 * - Consistent typography and spacing
 * - Subtle visual enhancements without distraction
 *
 * @param {Object} props - Component props
 * @param {number} props.totalComponents - Number of components after filtering
 * @param {number} props.originalTotal - Original total number of components
 * @param {boolean} props.isSearchActive - Whether search is currently active
 * @param {string} props.searchTerm - Current search term
 * @returns {JSX.Element} Rendered statistics card
 */
const StatsCard = memo(
  ({
    totalComponents = 0,
    originalTotal = 0,
    isSearchActive = false,
    searchTerm = '',
  }) => {
    /**
     * Gets the appropriate badge color based on component count
     *
     * Provides visual feedback about the number of components
     * using color coding for different ranges.
     *
     * @param {number} count - Component count
     * @returns {string} Badge color
     */
    const getBadgeColor = (count) => {
      if (count === 0) return colors.textTertiary;
      if (count < 5) return colors.error;
      if (count < 15) return colors.warning;
      if (count < 30) return colors.info;
      return colors.success;
    };

    /**
     * Gets the status text based on current state
     *
     * Provides contextual text describing the current filtering
     * or search state for better user understanding.
     *
     * @returns {string} Status text
     */
    const getStatusText = () => {
      if (isSearchActive) {
        return totalComponents === 0 ? 'No matches found' : 'Search results';
      }
      return 'Available components';
    };

    // Note: getCountLabel function available but not currently used
    // const getCountLabel = () => {
    //   if (isSearchActive) {
    //     return 'Filtered';
    //   }
    //   return 'Total';
    // };

    /**
     * Renders the search status indicator
     *
     * Shows a visual indicator when search is active with
     * the current search term and styling.
     *
     * @returns {JSX.Element|null} Search status element or null
     */
    const renderSearchStatus = () => {
      if (!isSearchActive) return null;

      return (
        <div
          style={{
            padding: '4px 8px',
            background: colors.primary + '10',
            borderRadius: '6px',
            fontSize: '10px',
            color: colors.primary,
            textAlign: 'center',
            marginTop: '4px',
          }}
        >
          <StarOutlined style={{ marginRight: '4px' }} />
          Search active
          {searchTerm && (
            <span
              style={{
                marginLeft: '4px',
                fontWeight: 500,
                opacity: 0.8,
              }}
            >
              "
              {searchTerm.length > 15
                ? searchTerm.substring(0, 15) + '...'
                : searchTerm}
              "
            </span>
          )}
        </div>
      );
    };

    /**
     * Renders the filtering ratio indicator
     *
     * Shows the ratio of filtered to total components when
     * search is active, providing context about filter effectiveness.
     *
     * @returns {JSX.Element|null} Filtering ratio element or null
     */
    const renderFilteringRatio = () => {
      if (!isSearchActive || originalTotal === 0) return null;

      const ratio = Math.round((totalComponents / originalTotal) * 100);

      return (
        <div
          style={{
            fontSize: '10px',
            color: colors.textTertiary,
            textAlign: 'center',
            marginTop: '2px',
          }}
        >
          {ratio}% of total components
        </div>
      );
    };

    return (
      <StyledStatsCard size='small' bordered={false}>
        <Space direction='vertical' size='small' style={{ width: '100%' }}>
          {/* Main component count display */}
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text
              strong
              style={{
                fontSize: '12px',
                color: colors.textPrimary,
              }}
            >
              Total Components
            </Text>
            <Badge
              count={originalTotal}
              style={{
                backgroundColor: getBadgeColor(originalTotal),
                fontSize: '10px',
              }}
              showZero
            />
          </div>

          {/* Current state display */}
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text type='secondary' style={{ fontSize: '11px' }}>
              {getStatusText()}
            </Text>
            <Text
              style={{
                fontSize: '11px',
                color: isSearchActive ? colors.primary : colors.textSecondary,
                fontWeight: isSearchActive ? 600 : 500,
              }}
            >
              {totalComponents}
            </Text>
          </div>

          {/* Search status indicator */}
          {renderSearchStatus()}

          {/* Filtering ratio indicator */}
          {renderFilteringRatio()}

          {/* No results message */}
          {isSearchActive && totalComponents === 0 && (
            <div
              style={{
                fontSize: '10px',
                color: colors.textTertiary,
                textAlign: 'center',
                fontStyle: 'italic',
                marginTop: '4px',
              }}
            >
              Try different search terms
            </div>
          )}
        </Space>
      </StyledStatsCard>
    );
  },
);

// Set display name for debugging
StatsCard.displayName = 'StatsCard';

export default StatsCard;
