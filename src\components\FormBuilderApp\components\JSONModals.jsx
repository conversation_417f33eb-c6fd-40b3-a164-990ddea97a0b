/**
 * @fileoverview JSON Modals component for JSON operations
 *
 * This component renders both the JSON view modal and JSON import modal
 * with comprehensive functionality for schema viewing and importing.
 *
 * @module JSONModals
 * @version 1.0.0
 * <AUTHOR> Builder Team
 * @since 2024-01-01
 */

import React, { useCallback } from 'react';
import { Modal, Input, Button, message } from 'antd';
import {
  FileTextOutlined,
  UploadOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { modalVariants } from '../constants/animations';

/**
 * JSON Modals component
 *
 * Renders both JSON view and import modals with comprehensive
 * functionality for schema operations and user interactions.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.jsonModalVisible - JSON modal visibility state
 * @param {Function} props.setJsonModalVisible - JSON modal visibility setter
 * @param {boolean} props.importModalVisible - Import modal visibility state
 * @param {Function} props.setImportModalVisible - Import modal visibility setter
 * @param {string} props.jsonInput - JSON input field value
 * @param {Function} props.setJsonInput - JSON input setter
 * @param {Object} props.currentFormSchema - Current form schema object
 * @param {Function} props.handleImportSubmit - Import submission handler
 *
 * @returns {React.ReactNode} JSON modals JSX
 *
 * @example
 * ```jsx
 * <JSONModals
 *   jsonModalVisible={jsonModalVisible}
 *   setJsonModalVisible={setJsonModalVisible}
 *   importModalVisible={importModalVisible}
 *   setImportModalVisible={setImportModalVisible}
 *   jsonInput={jsonInput}
 *   setJsonInput={setJsonInput}
 *   currentFormSchema={currentFormSchema}
 *   handleImportSubmit={handleImportSubmit}
 * />
 * ```
 */
const JSONModals = ({
  jsonModalVisible,
  setJsonModalVisible,
  importModalVisible,
  setImportModalVisible,
  jsonInput,
  setJsonInput,
  currentFormSchema,
  handleImportSubmit,
}) => {
  /**
   * Get current schema for display
   *
   * Memoized function to get the current form schema
   * optimized to prevent unnecessary re-computations.
   *
   * @returns {Object} Current form schema
   */
  const getCurrentSchema = useCallback(() => {
    return currentFormSchema;
  }, [currentFormSchema]);

  /**
   * Handle copy to clipboard
   *
   * Copies the current JSON schema to clipboard with user feedback.
   */
  const handleCopyToClipboard = useCallback(() => {
    navigator.clipboard.writeText(JSON.stringify(getCurrentSchema(), null, 2));
    message.success('JSON copied to clipboard!');
  }, [getCurrentSchema]);

  return (
    <>
      {/* Enhanced JSON View Modal */}
      <AnimatePresence>
        {jsonModalVisible && (
          <Modal
            title={
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  fontSize: '16px',
                  fontWeight: 600,
                  color: '#212121',
                }}
              >
                <FileTextOutlined style={{ color: '#0066cc' }} />
                Form JSON Schema
              </div>
            }
            open={jsonModalVisible}
            onCancel={() => setJsonModalVisible(false)}
            modalRender={(modal) => (
              <motion.div
                variants={modalVariants}
                initial='hidden'
                animate='visible'
                exit='exit'
              >
                {modal}
              </motion.div>
            )}
            footer={[
              <Button
                key='close'
                onClick={() => setJsonModalVisible(false)}
                style={{ borderRadius: '8px' }}
              >
                Close
              </Button>,
              <Button
                key='copy'
                type='primary'
                icon={<CopyOutlined />}
                onClick={handleCopyToClipboard}
                style={{
                  borderRadius: '8px',
                  background:
                    'linear-gradient(135deg, #0066cc 0%, #0052a3 100%)',
                  border: 'none',
                  boxShadow: '0 2px 8px rgba(0, 102, 204, 0.3)',
                }}
              >
                Copy to Clipboard
              </Button>,
            ]}
            width={900}
            style={{ top: 20 }}
            styles={{
              header: {
                borderBottom: '2px solid #e0e0e0',
                paddingBottom: '16px',
                marginBottom: '20px',
              },
              body: {
                padding: '20px 24px',
              },
            }}
          >
            <Input.TextArea
              value={JSON.stringify(getCurrentSchema(), null, 2)}
              rows={24}
              readOnly
              style={{
                fontFamily:
                  '"JetBrains Mono", "Fira Code", "SF Mono", Consolas, monospace',
                fontSize: '13px',
                lineHeight: '1.6',
                borderRadius: '8px',
                border: '2px solid #e0e0e0',
                background: '#fafafa',
              }}
            />
          </Modal>
        )}
      </AnimatePresence>

      {/* Enhanced JSON Import Modal */}
      <AnimatePresence>
        {importModalVisible && (
          <Modal
            title={
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  fontSize: '16px',
                  fontWeight: 600,
                  color: '#212121',
                }}
              >
                <UploadOutlined style={{ color: '#0066cc' }} />
                Import Form Schema
              </div>
            }
            open={importModalVisible}
            onCancel={() => setImportModalVisible(false)}
            modalRender={(modal) => (
              <motion.div
                variants={modalVariants}
                initial='hidden'
                animate='visible'
                exit='exit'
              >
                {modal}
              </motion.div>
            )}
            onOk={handleImportSubmit}
            okText='Import Schema'
            okButtonProps={{
              style: {
                borderRadius: '8px',
                background: 'linear-gradient(135deg, #0066cc 0%, #0052a3 100%)',
                border: 'none',
                boxShadow: '0 2px 8px rgba(0, 102, 204, 0.3)',
              },
            }}
            cancelButtonProps={{
              style: { borderRadius: '8px' },
            }}
            width={900}
            style={{ top: 20 }}
            styles={{
              header: {
                borderBottom: '2px solid #e0e0e0',
                paddingBottom: '16px',
                marginBottom: '20px',
              },
              body: {
                padding: '20px 24px',
              },
            }}
          >
            <div style={{ marginBottom: '16px' }}>
              <p
                style={{
                  margin: 0,
                  color: '#616161',
                  fontSize: '14px',
                  lineHeight: '1.5',
                }}
              >
                📋 Paste your form JSON schema below. The schema will be
                validated before import.
              </p>
            </div>
            <Input.TextArea
              value={jsonInput}
              onChange={(e) => setJsonInput(e.target.value)}
              placeholder='Paste your JSON schema here... (e.g., {"metadata": {...}, "layout": [...], "components": {...}})'
              rows={18}
              style={{
                fontFamily:
                  '"JetBrains Mono", "Fira Code", "SF Mono", Consolas, monospace',
                fontSize: '13px',
                lineHeight: '1.6',
                borderRadius: '8px',
                border: '2px solid #e0e0e0',
                background: '#fafafa',
              }}
            />
          </Modal>
        )}
      </AnimatePresence>
    </>
  );
};

export default JSONModals;
